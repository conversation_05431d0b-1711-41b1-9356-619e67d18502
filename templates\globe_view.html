<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disease Outbreak Globe View</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/globe.css') }}">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts-gl.min.js') }}"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            -webkit-user-select: none;
            user-select: none;
            touch-action: none;
        }

        html,
        body {
            width: 100%;
            height: 100%;
            overflow: hidden;
            background: #000;
            -webkit-text-size-adjust: 100%;
            text-size-adjust: 100%;
        }

        #container {
            width: 100%;
            height: 100%;
        }

        .loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 16px;
            z-index: 999;
        }

        .error-message {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: red;
            font-size: 16px;
            z-index: 999;
            text-align: center;
            background: rgba(0, 0, 0, 0.8);
            padding: 20px;
            border-radius: 8px;
        }

        /* 设置图标样式 */
        .settings-icon {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .settings-icon:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        .settings-icon svg {
            width: 24px;
            height: 24px;
            fill: white;
        }

        /* 设置抽屉样式 */
        .settings-drawer {
            position: fixed;
            top: 0;
            right: -350px;
            width: 300px;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            transition: right 0.3s ease;
            z-index: 999;
            padding: 20px;
            color: white;
            box-shadow: -2px 0 5px rgba(0, 0, 0, 0.3);
        }

        .settings-drawer.open {
            right: 0;
        }

        .settings-drawer h3 {
            margin-bottom: 20px;
            color: #fff;
        }

        .settings-item {
            margin-bottom: 15px;
        }

        .settings-item label {
            display: block;
            margin-bottom: 5px;
        }

        /* 疾病图例面板样式 */
        .disease-legend {
            position: fixed;
            top: 50%;
            left: 20px;
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 8px;
            z-index: 998;
            min-width: 150px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .toggle-all {
            display: flex;
            align-items: center;
            padding: 8px;
            margin-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            cursor: pointer;
            background-color: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        .toggle-all .disease-color {
            position: relative;
            background-color: #fff !important;
        }

        .toggle-all .disease-color::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #000;
            font-size: 10px;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .toggle-all.checked .disease-color::after {
            opacity: 1;
        }

        .toggle-all:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        .toggle-all-text {
            color: white;
            font-size: 14px;
            margin-left: 10px;
            -webkit-user-select: none;
            user-select: none;
        }

        .disease-item {
            display: flex;
            align-items: center;
            padding: 7px 11px;
            margin: 3px 0;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: rgba(255, 255, 255, 0.08);
        }

        .disease-item:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .disease-color {
            width: 18px;
            height: 18px;
            border-radius: 3px;
            margin-right: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .disease-name {
            color: #fff;
            font-size: 13px;
            font-weight: 400;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
        }

        .disease-item.disabled {
            opacity: 0.5;
            background: rgba(255, 255, 255, 0.05);
        }

        .disease-item.disabled .disease-color {
            border-color: rgba(255, 255, 255, 0.1);
        }

        /* 时间范围选择器样式 */
        .time-range-selector {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(32, 32, 32, 0.7);
            padding: 12px 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            backdrop-filter: blur(5px);
            width: 200px;
        }

        .time-range-title {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 8px;
            text-align: center;
        }

        .time-range-options {
            display: flex;
            gap: 6px;
            justify-content: space-between;
        }

        .time-range-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 2px 0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            flex: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 24px;
        }

        .time-range-btn input[type="radio"] {
            position: absolute;
            opacity: 0;
        }

        .time-range-btn span {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            display: inline-block;
            line-height: 1;
        }

        .time-range-btn:hover {
            background: rgba(255, 255, 255, 0.15);
        }

        .time-range-btn input[type="radio"]:checked+span {
            color: #fff;
        }

        .time-range-btn input[type="radio"]:checked~.time-range-btn {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .time-range-tooltip {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: rgba(255, 255, 255, 0.9);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s;
            pointer-events: none;
            margin-bottom: 5px;
        }

        .time-range-btn:hover .time-range-tooltip {
            opacity: 1;
        }
    </style>
</head>

<body>
    <div id="container"></div>
    <div id="loading" class="loading">加载中...</div>
    <div id="error" class="error-message" style="display: none;"></div>

    <!-- 时间范围选择器 -->
    <div class="time-range-selector">
        <div class="time-range-title">选择时间范围</div>
        <div class="time-range-options">
            <label class="time-range-btn">
                <input type="radio" name="timeRange" value="week">
                <span>周</span>
                <div class="time-range-tooltip">显示最近7天的数据</div>
            </label>
            <label class="time-range-btn">
                <input type="radio" name="timeRange" value="month" checked>
                <span>月</span>
                <div class="time-range-tooltip">显示最近30天的数据</div>
            </label>
            <label class="time-range-btn">
                <input type="radio" name="timeRange" value="year">
                <span>年</span>
                <div class="time-range-tooltip">显示最近365天的数据</div>
            </label>
        </div>
    </div>

    <!-- 设置图标 -->
    <div class="settings-icon" id="settingsIcon">
        <svg viewBox="0 0 24 24">
            <path
                d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" />
        </svg>
    </div>

    <!-- 设置抽屉 -->
    <div class="settings-drawer" id="settingsDrawer">
        <h3>地球设置</h3>
        <div class="settings-item">
            <label>
                <input type="checkbox" id="autoRotate" checked> 自动旋转
            </label>
        </div>
        <div class="settings-item">
            <label for="rotateSpeed">旋转速度</label>
            <input type="range" id="rotateSpeed" min="1" max="10" value="5">
        </div>
    </div>

    <!-- 疾病图例面板 -->
    <div class="disease-legend" id="diseaseLegend">
        <div class="toggle-all checked" id="toggleAll">
            <div class="disease-color"></div>
            <div class="toggle-all-text">显示/隐藏全部</div>
        </div>
        <div id="legendItems"></div>
    </div>

    <script type="text/javascript">
        // 设置按钮的事件处理函数
        function initializeSettingsDrawer() {
            const settingsIcon = document.getElementById('settingsIcon');
            const settingsDrawer = document.getElementById('settingsDrawer');

            if (!settingsIcon.hasListener) {
                settingsIcon.addEventListener('click', function () {
                    settingsDrawer.classList.toggle('open');
                });
                settingsIcon.hasListener = true;
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            var dom = document.getElementById('container');
            var myChart = echarts.init(dom, null, {
                renderer: 'canvas',
                useDirtyRect: false
            });

            // 初始化设置抽屉
            initializeSettingsDrawer();

            // 初始化全选/全不选功能
            function initializeToggleAll() {
                const toggleAll = document.getElementById('toggleAll');
                if (!toggleAll.hasListener) {
                    toggleAll.addEventListener('click', function () {
                        const allItems = document.querySelectorAll('.disease-item');
                        const areAllHidden = Array.from(allItems).every(item => item.classList.contains('disabled'));

                        // 更新全选按钮状态
                        if (areAllHidden) {
                            this.classList.add('checked');
                        } else {
                            this.classList.remove('checked');
                        }

                        allItems.forEach(item => {
                            if (areAllHidden) {
                                item.classList.remove('disabled');
                            } else {
                                item.classList.add('disabled');
                            }
                        });

                        const series = myChart.getOption().series;
                        const newSeries = series.map(s => {
                            if (s.type === 'scatter3D') {
                                return {
                                    ...s,
                                    showSymbol: areAllHidden,
                                    itemStyle: {
                                        ...s.itemStyle,
                                        opacity: areAllHidden ? 1 : 0,
                                        borderWidth: areAllHidden ? 0.5 : 0,
                                        borderColor: areAllHidden ? '#fff' : 'transparent'
                                    }
                                };
                            } else if (s.type === 'lines3D') {
                                return {
                                    ...s,
                                    show: areAllHidden,
                                    effect: {
                                        ...s.effect,
                                        show: areAllHidden
                                    },
                                    lineStyle: {
                                        ...s.lineStyle,
                                        opacity: areAllHidden ? 0.1 : 0,
                                        width: areAllHidden ? 1 : 0
                                    }
                                };
                            }
                            return s;
                        });

                        myChart.setOption({
                            series: newSeries
                        });
                    });
                    toggleAll.hasListener = true;
                }
            }

            // 用于存储疾病颜色映射的对象
            const diseaseColors = {};

            // 预定义的颜色方案（按视觉强度序，红色系靠前）
            const predefinedColors = [
                '#e15759', // 深红色
                '#ff7c7c', // 鲜红色
                '#ff9da7', // 浅红色
                '#f28e2b', // 深橙色
                '#ffb55a', // 浅橙色
                '#edc948', // 深黄色
                '#59a14f', // 深绿色
                '#8bd3c7', // 浅绿色
                '#4e79a7', // 深蓝色
                '#7eb0d5', // 浅蓝色
                '#b07aa1', // 深紫色
                '#c6cbe9', // 浅紫色
                '#9c755f', // 深棕色
                '#bab0ac', // 浅灰色
                '#e3e3e3'  // 最浅的灰色
            ];

            // 获取颜色的函数
            function getRandomColor(index, totalDiseases) {
                // 根据疾病在排序中的位置选择颜色
                return predefinedColors[Math.min(index, predefinedColors.length - 1)];
            }

            // 显示错误信息
            function showError(message) {
                const errorDiv = document.getElementById('error');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                document.getElementById('loading').style.display = 'none';
            }

            // 获取数据并渲染地球
            function fetchDataAndRender() {
                // 清理图表
                myChart.clear();

                // 获取当前选中的时间范围
                const timeRange = document.querySelector('input[name="timeRange"]:checked').value;

                $.ajax({
                    url: '/globe_data',
                    method: 'GET',
                    data: { timeRange: timeRange },
                    success: function (data) {
                        document.getElementById('loading').style.display = 'none';

                        if (data.error) {
                            showError('加载数据时发生错误: ' + data.error);
                            return;
                        }

                        if (!data.diseases || !data.points || data.diseases.length === 0) {
                            showError('没有找到符合条件的疾病数据');
                            return;
                        }

                        // 计算每个疾病的总感染人数
                        const diseaseTotals = {};
                        data.points.forEach(point => {
                            const disease = point[3];
                            const infections = point[2];
                            diseaseTotals[disease] = (diseaseTotals[disease] || 0) + infections;
                        });

                        // 提取括号中的中文名称的函数
                        function getChineseDiseaseName(disease) {
                            const match = disease.match(/\((.*?)\)/);
                            return match ? match[1] : disease;
                        }

                        // 按感染总数倒序排序（用于图例显示）
                        const sortedDiseases = Object.entries(diseaseTotals)
                            .sort(([, a], [, b]) => b - a)
                            .map(([disease]) => disease);

                        // 重置颜色映射
                        Object.keys(diseaseColors).forEach(key => delete diseaseColors[key]);

                        // 为每个疾病分配颜色（按感染人数从高到低）
                        sortedDiseases.forEach((disease, index) => {
                            diseaseColors[disease] = getRandomColor(index, sortedDiseases.length);
                        });

                        // 更新疾病图例（按感染人数排序）
                        const legendItemsContainer = document.getElementById('legendItems');
                        legendItemsContainer.innerHTML = '';

                        // 按感染总数排序显示例
                        sortedDiseases.forEach(disease => {
                            const displayName = getChineseDiseaseName(disease);
                            const item = document.createElement('div');
                            item.className = 'disease-item';
                            item.innerHTML = `
                                <div class="disease-color" style="background-color: ${diseaseColors[disease]}"></div>
                                <div class="disease-name">${displayName} (${Math.round(diseaseTotals[disease]).toLocaleString()})</div>
                            `;
                            item.dataset.disease = disease;
                            item.addEventListener('click', function () {
                                this.classList.toggle('disabled');
                                const seriesName = this.dataset.disease;
                                const series = myChart.getOption().series;
                                const newOption = {
                                    series: series.map(s => {
                                        if (s.name === seriesName || s.name === seriesName + '_lines') {
                                            const isDisabled = this.classList.contains('disabled');
                                            if (s.type === 'scatter3D') {
                                                return {
                                                    ...s,
                                                    showSymbol: !isDisabled,
                                                    itemStyle: {
                                                        ...s.itemStyle,
                                                        opacity: isDisabled ? 0 : 1,
                                                        borderWidth: isDisabled ? 0 : 0.5,
                                                        borderColor: isDisabled ? 'transparent' : '#fff'
                                                    }
                                                };
                                            } else if (s.type === 'lines3D') {
                                                return {
                                                    ...s,
                                                    show: !isDisabled,
                                                    effect: {
                                                        ...s.effect,
                                                        show: !isDisabled
                                                    },
                                                    lineStyle: {
                                                        ...s.lineStyle,
                                                        opacity: isDisabled ? 0 : 0.1,
                                                        width: isDisabled ? 0 : 1
                                                    }
                                                };
                                            }
                                        }
                                        return s;
                                    })
                                };
                                myChart.setOption(newOption);

                                // 更新全选按钮状态
                                const allItems = document.querySelectorAll('.disease-item');
                                const hasVisibleItems = Array.from(allItems).some(item => !item.classList.contains('disabled'));

                                const toggleAll = document.getElementById('toggleAll');

                                if (hasVisibleItems) {
                                    toggleAll.classList.add('checked');
                                } else {
                                    toggleAll.classList.remove('checked');
                                }
                            });
                            legendItemsContainer.appendChild(item);
                        });

                        // 初始化全选/全不选功能
                        initializeToggleAll();

                        const series = sortedDiseases.map((disease, index) => {
                            // 为不同疾病设置不同的符号形状
                            const symbols = ['circle', 'diamond', 'triangle', 'pin', 'rect'];
                            const symbol = symbols[index % symbols.length];

                            return {
                                name: disease,
                                type: 'scatter3D',
                                coordinateSystem: 'globe',
                                blendMode: 'source-over',
                                symbol: symbol,
                                symbolSize: function (val) {
                                    // 使用对数计算来处理大范围数据
                                    const infections = val[2];
                                    if (infections <= 0) return 7;  // 最小值

                                    // 使用自然对数计算大小
                                    // Math.log(400000) ≈ 12.9，所以乘以1.5后范围大约在0-19间
                                    const logSize = Math.log(infections + 1) * 1.5;

                                    // 确保大小在7-22之间
                                    return Math.min(Math.max(logSize + 7, 7), 22);
                                },
                                itemStyle: {
                                    color: diseaseColors[disease],
                                    opacity: 0.85,
                                    borderWidth: 0.5,
                                    borderColor: '#fff'
                                },
                                emphasis: {
                                    itemStyle: {
                                        opacity: 1,
                                        borderWidth: 1,
                                        borderColor: '#fff'
                                    },
                                    label: {
                                        show: true,
                                        formatter: function (params) {
                                            return disease;
                                        },
                                        textStyle: {
                                            color: '#fff',
                                            fontSize: 13,  // 稍微增大文字大小
                                            backgroundColor: 'rgba(0,0,0,0.7)',
                                            padding: [4, 7],  // 稍微增大内边距
                                            borderRadius: 3
                                        }
                                    }
                                },
                                label: {
                                    show: false
                                },
                                data: data.points
                                    .filter(point => point[3] === disease)
                                    .sort((a, b) => new Date(b[5]) - new Date(a[5]))  // 按时间倒序排序
                                    .map(point => ({
                                        value: [point[0], point[1], point[2]],
                                        // 添加自定义信息用于tooltip显示
                                        disease: disease,
                                        location: point[4],
                                        time: point[5],
                                        infections: Math.round(point[2]),
                                        deaths: point[6],
                                        label: {
                                            show: false
                                        }
                                    })),
                                tooltip: {
                                    formatter: function (params) {
                                        if (params.data && params.data.disease) {
                                            // Find all overlapping points at this location
                                            const overlappingPoints = [];
                                            const allSeries = myChart.getOption().series;
                                            const currentLat = params.data.value[0];
                                            const currentLng = params.data.value[1];
                                            
                                            // 获取当前视角下的缩放级别
                                            const viewControl = myChart.getOption().globe[0].viewControl;
                                            const distance = viewControl.distance;
                                            
                                            // 根据视角距离动态调整阈值
                                            // distance 默认是 200，阈值范围从 0.5 到 2 度
                                            const threshold = Math.min(2, Math.max(0.5, (distance / 200) * 1));
                                            
                                            allSeries.forEach(series => {
                                                if (series.type === 'scatter3D' && !series.lineStyle) {  // Exclude line series
                                                    // 检查该系列是否被禁用（通过检查对应的图例项）
                                                    const legendItem = document.querySelector(`.disease-item[data-disease="${series.name}"]`);
                                                    const isDisabled = legendItem && legendItem.classList.contains('disabled');
                                                    
                                                    // 只有未被禁用的系列才添加到重叠点中
                                                    if (!isDisabled) {
                                                        series.data.forEach(point => {
                                                            // 计算两点之间的实际距离
                                                            const lat1 = currentLat * Math.PI / 180;
                                                            const lat2 = point.value[0] * Math.PI / 180;
                                                            const lng1 = currentLng * Math.PI / 180;
                                                            const lng2 = point.value[1] * Math.PI / 180;
                                                            
                                                            // 使用 Haversine 公式计算球面距离
                                                            const R = 6371; // 地球半径（公里）
                                                            const dLat = lat2 - lat1;
                                                            const dLng = lng2 - lng1;
                                                            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                                                                    Math.cos(lat1) * Math.cos(lat2) * 
                                                                    Math.sin(dLng/2) * Math.sin(dLng/2);
                                                            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                                                            const distance = R * c;
                                                            
                                                            // 如果距离小于阈值（公里），认为点重叠
                                                            if (distance < threshold * 111) {  // 1度约等于111公里
                                                                overlappingPoints.push(point);
                                                            }
                                                        });
                                                    }
                                                }
                                            });

                                            // Sort overlapping points by time (descending)
                                            overlappingPoints.sort((a, b) => new Date(b.time) - new Date(a.time));

                                            let tooltipContent = `
                                                <div class="echarts-tooltip" style="
                                                    padding: 0;
                                                    display: flex;
                                                    flex-direction: column;
                                                    max-height: 400px;
                                                    pointer-events: auto;
                                                ">
                                                    <div style="
                                                        font-size: 16px;
                                                        margin-bottom: 5px;
                                                        font-weight: bold;
                                                        padding: 0 5px 10px 5px;
                                                        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                                                        position: sticky;
                                                        top: 0;
                                                        background: rgba(0, 0, 0, 0.7);
                                                        z-index: 1;
                                                    ">
                                                        <div style="
                                                            display: flex;
                                                            flex-direction: column;
                                                        ">
                                                            <span style="
                                                                font-size: 16px;
                                                                margin-bottom: 4px;
                                                            ">${params.data.location}</span>
                                                            <span style="
                                                                font-size: 13px;
                                                                color: rgba(255, 255, 255, 0.7);
                                                                background: rgba(255, 255, 255, 0.1);
                                                                padding: 2px 6px;
                                                                border-radius: 4px;
                                                                align-self: flex-start;
                                                            ">${overlappingPoints.length}条记录</span>
                                                        </div>
                                                    </div>
                                                    <div class="tooltip-content" style="
                                                        overflow-y: auto;
                                                        max-height: 350px;
                                                        padding-right: 5px;
                                                        margin-right: -5px;
                                                        scrollbar-width: thin;
                                                        scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0);
                                                        pointer-events: auto;
                                                    ">`;

                                            // Add content for each overlapping point
                                            overlappingPoints.forEach(point => {
                                                const displayName = getChineseDiseaseName(point.disease);
                                                tooltipContent += `
                                                    <div style="
                                                        background: rgba(255, 255, 255, 0.1); 
                                                        border-radius: 4px; 
                                                        padding: 8px 12px;
                                                        position: relative;
                                                        margin: 4px 0 4px 4px;
                                                    ">
                                                        <div style="
                                                            position: absolute;
                                                            left: -4px;
                                                            top: 0;
                                                            bottom: 0;
                                                            width: 4px;
                                                            background: ${diseaseColors[point.disease]};
                                                            border-radius: 2px 0 0 2px;
                                                        "></div>
                                                        <div style="
                                                            color: ${diseaseColors[point.disease]};
                                                            font-weight: bold;
                                                            font-size: 15px;
                                                            margin-bottom: 4px;
                                                        ">${displayName}</div>
                                                        <div style="font-size: 14px; color: #ccc; font-weight: bold;">日期：${point.time}</div>
                                                        <div style="font-size: 14px; color: #ccc; font-weight: bold;">感染人数：${point.infections}</div>
                                                        <div style="font-size: 14px; color: #ccc; font-weight: bold;">死亡人数：${point.deaths}</div>
                                                    </div>`;
                                            });

                                            tooltipContent += `
                                                    </div>
                                                </div>
                                                <style>
                                                    .echarts-tooltip {
                                                        pointer-events: auto !important;
                                                    }
                                                    .tooltip-content {
                                                        pointer-events: auto !important;
                                                    }
                                                    /* Webkit (Chrome, Safari) scrollbar styles */
                                                    div::-webkit-scrollbar {
                                                        width: 6px;
                                                    }
                                                    div::-webkit-scrollbar-track {
                                                        background: rgba(0, 0, 0, 0);
                                                    }
                                                    div::-webkit-scrollbar-thumb {
                                                        background-color: rgba(255, 255, 255, 0.3);
                                                        border-radius: 3px;
                                                    }
                                                    div::-webkit-scrollbar-thumb:hover {
                                                        background-color: rgba(255, 255, 255, 0.5);
                                                    }
                                                </style>`;

                                            return tooltipContent;
                                        }
                                        return '';
                                    }
                                }
                            };
                        });

                        const lines = sortedDiseases.map(disease => {
                            return {
                                name: disease + '_lines',
                                type: 'lines3D',
                                coordinateSystem: 'globe',
                                effect: {
                                    show: true,
                                    period: 4,
                                    trailWidth: 2,
                                    trailLength: 0.1,
                                    trailOpacity: 0.5,
                                    trailColor: diseaseColors[disease]
                                },
                                lineStyle: {
                                    width: 1,
                                    color: diseaseColors[disease],
                                    opacity: 0.1
                                },
                                data: generateLines(data.points.filter(point => point[3] === disease))
                            };
                        });

                        const option = {
                            backgroundColor: '#000',
                            tooltip: {
                                show: true,
                                enterable: true,
                                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                                borderWidth: 0,
                                borderRadius: 8,
                                padding: 12,
                                textStyle: {
                                    color: '#fff',
                                    fontSize: 14,
                                    fontWeight: 'bold'
                                },
                                formatter: function (params) {
                                    if (params.data && params.data.disease) {
                                        // Find all overlapping points at this location
                                        const overlappingPoints = [];
                                        const allSeries = myChart.getOption().series;
                                        const currentLat = params.data.value[0];
                                        const currentLng = params.data.value[1];
                                        
                                        // 获取当前视角下的缩放级别
                                        const viewControl = myChart.getOption().globe[0].viewControl;
                                        const distance = viewControl.distance;
                                        
                                        // 根据视角距离动态调整阈值
                                        // distance 默认是 200，阈值范围从 0.5 到 2 度
                                        const threshold = Math.min(2, Math.max(0.5, (distance / 200) * 1));
                                        
                                        allSeries.forEach(series => {
                                            if (series.type === 'scatter3D' && !series.lineStyle) {  // Exclude line series
                                                // 检查该系列是否被禁用（通过检查对应的图例项）
                                                const legendItem = document.querySelector(`.disease-item[data-disease="${series.name}"]`);
                                                const isDisabled = legendItem && legendItem.classList.contains('disabled');
                                                
                                                // 只有未被禁用的系列才添加到重叠点中
                                                if (!isDisabled) {
                                                    series.data.forEach(point => {
                                                        // 计算两点之间的实际距离
                                                        const lat1 = currentLat * Math.PI / 180;
                                                        const lat2 = point.value[0] * Math.PI / 180;
                                                        const lng1 = currentLng * Math.PI / 180;
                                                        const lng2 = point.value[1] * Math.PI / 180;
                                                        
                                                        // 使用 Haversine 公式计算球面距离
                                                        const R = 6371; // 地球半径（公里）
                                                        const dLat = lat2 - lat1;
                                                        const dLng = lng2 - lng1;
                                                        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                                                                Math.cos(lat1) * Math.cos(lat2) * 
                                                                Math.sin(dLng/2) * Math.sin(dLng/2);
                                                        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
                                                        const distance = R * c;
                                                        
                                                        // 如果距离小于阈值（公里），认为点重叠
                                                        if (distance < threshold * 111) {  // 1度约等于111公里
                                                            overlappingPoints.push(point);
                                                        }
                                                    });
                                                }
                                            }
                                        });

                                        // Sort overlapping points by time (descending)
                                        overlappingPoints.sort((a, b) => new Date(b.time) - new Date(a.time));

                                        let tooltipContent = `
                                            <div class="echarts-tooltip" style="
                                                padding: 0;
                                                display: flex;
                                                flex-direction: column;
                                                max-height: 400px;
                                                pointer-events: auto;
                                            ">
                                                <div style="
                                                    font-size: 16px;
                                                    margin-bottom: 5px;
                                                    font-weight: bold;
                                                    padding: 0 5px 10px 5px;
                                                    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
                                                    position: sticky;
                                                    top: 0;
                                                    background: rgba(0, 0, 0, 0.7);
                                                    z-index: 1;
                                                ">
                                                    <div style="
                                                        display: flex;
                                                        flex-direction: column;
                                                    ">
                                                        <span style="
                                                            font-size: 16px;
                                                            margin-bottom: 4px;
                                                        ">${params.data.location}</span>
                                                        <span style="
                                                            font-size: 13px;
                                                            color: rgba(255, 255, 255, 0.7);
                                                            background: rgba(255, 255, 255, 0.1);
                                                            padding: 2px 6px;
                                                            border-radius: 4px;
                                                            align-self: flex-start;
                                                        ">${overlappingPoints.length}条记录</span>
                                                    </div>
                                                </div>
                                                <div class="tooltip-content" style="
                                                    overflow-y: auto;
                                                    max-height: 350px;
                                                    padding-right: 5px;
                                                    margin-right: -5px;
                                                    scrollbar-width: thin;
                                                    scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0);
                                                    pointer-events: auto;
                                                ">`;

                                        // Add content for each overlapping point
                                        overlappingPoints.forEach(point => {
                                            const displayName = getChineseDiseaseName(point.disease);
                                            tooltipContent += `
                                                <div style="
                                                    background: rgba(255, 255, 255, 0.1); 
                                                    border-radius: 4px; 
                                                    padding: 8px 12px;
                                                    position: relative;
                                                    margin: 4px 0 4px 4px;
                                                ">
                                                    <div style="
                                                        position: absolute;
                                                        left: -4px;
                                                        top: 0;
                                                        bottom: 0;
                                                        width: 4px;
                                                        background: ${diseaseColors[point.disease]};
                                                        border-radius: 2px 0 0 2px;
                                                    "></div>
                                                    <div style="
                                                        color: ${diseaseColors[point.disease]};
                                                        font-weight: bold;
                                                        font-size: 15px;
                                                        margin-bottom: 4px;
                                                    ">${displayName}</div>
                                                    <div style="font-size: 14px; color: #ccc; font-weight: bold;">日期：${point.time}</div>
                                                    <div style="font-size: 14px; color: #ccc; font-weight: bold;">感染人数：${point.infections}</div>
                                                    <div style="font-size: 14px; color: #ccc; font-weight: bold;">死亡人数：${point.deaths}</div>
                                                </div>`;
                                        });

                                        tooltipContent += `
                                                </div>
                                            </div>
                                            <style>
                                                .echarts-tooltip {
                                                    pointer-events: auto !important;
                                                }
                                                .tooltip-content {
                                                    pointer-events: auto !important;
                                                }
                                                /* Webkit (Chrome, Safari) scrollbar styles */
                                                div::-webkit-scrollbar {
                                                    width: 6px;
                                                }
                                                div::-webkit-scrollbar-track {
                                                    background: rgba(0, 0, 0, 0);
                                                }
                                                div::-webkit-scrollbar-thumb {
                                                    background-color: rgba(255, 255, 255, 0.3);
                                                    border-radius: 3px;
                                                }
                                                div::-webkit-scrollbar-thumb:hover {
                                                    background-color: rgba(255, 255, 255, 0.5);
                                                }
                                            </style>`;

                                        return tooltipContent;
                                    }
                                    return '';
                                }
                            },
                            globe: {
                                baseTexture: "{{ url_for('static', filename='images/world.topo.bathy.200401.jpg') }}",
                                heightTexture: "{{ url_for('static', filename='images/bathymetry_bw_composite_4k.jpg') }}",
                                shading: 'lambert',
                                environment: '#000',
                                light: {
                                    ambient: {
                                        intensity: 0.8
                                    },
                                    main: {
                                        intensity: 0.8
                                    }
                                },
                                viewControl: {
                                    autoRotate: true,
                                    autoRotateSpeed: 5,
                                    distance: 200,  // 调整地球大小，值越大地球越小
                                },
                                globeRadius: 95  // 调整地球半径
                            },
                            series: [...series, ...lines]
                        };

                        myChart.setOption(option);

                        // 设置控件事件处理
                        const settingsIcon = document.getElementById('settingsIcon');
                        const settingsDrawer = document.getElementById('settingsDrawer');
                        const autoRotateCheckbox = document.getElementById('autoRotate');
                        const rotateSpeedInput = document.getElementById('rotateSpeed');

                        // 初始化设置状态
                        autoRotateCheckbox.checked = true;
                        rotateSpeedInput.disabled = false;

                        // 自动旋转切换
                        autoRotateCheckbox.addEventListener('change', (e) => {
                            const newOption = {
                                globe: {
                                    viewControl: {
                                        autoRotate: e.target.checked
                                    }
                                }
                            };
                            myChart.setOption(newOption);
                            rotateSpeedInput.disabled = !e.target.checked;
                        });

                        // 旋转速度调节
                        rotateSpeedInput.addEventListener('input', (e) => {
                            const newOption = {
                                globe: {
                                    viewControl: {
                                        autoRotateSpeed: e.target.value
                                    }
                                }
                            };
                            myChart.setOption(newOption);
                        });

                        // 添加tooltip延迟消失的变量
                        let tooltipTimeout = null;
                        let currentTooltip = null;
                        let isOverTooltip = false;

                        // 创建一个全局的鼠标事件监听器
                        document.addEventListener('mouseover', function(e) {
                            // 检查是否移动到了tooltip上
                            let target = e.target;
                            while (target) {
                                if (target.classList && target.classList.contains('echarts-tooltip')) {
                                    isOverTooltip = true;
                                    if (tooltipTimeout) {
                                        clearTimeout(tooltipTimeout);
                                        tooltipTimeout = null;
                                    }
                                    return;
                                }
                                target = target.parentElement;
                            }
                        }, true);

                        document.addEventListener('mouseout', function(e) {
                            // 检查是否从tooltip移出
                            let target = e.target;
                            while (target) {
                                if (target.classList && target.classList.contains('echarts-tooltip')) {
                                    let rect = target.getBoundingClientRect();
                                    // 检查鼠标是否真的离开了tooltip区域
                                    if (e.clientX < rect.left || e.clientX >= rect.right ||
                                        e.clientY < rect.top || e.clientY >= rect.bottom) {
                                        isOverTooltip = false;
                                        myChart.dispatchAction({
                                            type: 'hideTip'
                                        });
                                    }
                                    return;
                                }
                                target = target.parentElement;
                            }
                        }, true);

                        // 添加鼠标事件处理
                        myChart.getZr().on('globalout', () => {
                            if (!isOverTooltip) {
                                tooltipTimeout = setTimeout(() => {
                                    myChart.dispatchAction({
                                        type: 'hideTip'
                                    });
                                }, 200); // 增加延迟时间到200ms
                            }
                        });
                    },
                    error: function (xhr, status, error) {
                        showError('加载数据失败，请检查网络连接或重新登录');
                        console.error('Error fetching data:', error);
                    }
                });
            }

            // 生成连线数据
            function generateLines(points) {
                const lines = [];
                if (points.length < 2) return lines;

                // 按时间排序
                points.sort((a, b) => new Date(a[5]) - new Date(b[5]));

                for (let i = 0; i < points.length - 1; i++) {
                    lines.push({
                        coords: [
                            [points[i][0], points[i][1]],
                            [points[i + 1][0], points[i + 1][1]]
                        ]
                    });
                }
                return lines;
            }

            // 初始加载
            fetchDataAndRender();

            // 监听时间范围选择变化
            document.querySelectorAll('input[name="timeRange"]').forEach(radio => {
                radio.addEventListener('change', (e) => {
                    // 更新按钮样式
                    document.querySelectorAll('.time-range-btn').forEach(btn => {
                        if (btn.querySelector('input').checked) {
                            btn.style.background = 'rgba(255, 255, 255, 0.2)';
                            btn.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                            btn.querySelector('span').style.color = '#fff';
                        } else {
                            btn.style.background = 'rgba(255, 255, 255, 0.1)';
                            btn.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                            btn.querySelector('span').style.color = 'rgba(255, 255, 255, 0.7)';
                        }
                    });
                    // 重新获取数据
                    fetchDataAndRender();
                });
            });

            // 初始化按样式
            document.querySelector('input[name="timeRange"]:checked')
                .closest('.time-range-btn').style.background = 'rgba(255, 255, 255, 0.2)';
            document.querySelector('input[name="timeRange"]:checked')
                .closest('.time-range-btn').style.borderColor = 'rgba(255, 255, 255, 0.3)';
            document.querySelector('input[name="timeRange"]:checked')
                .closest('.time-range-btn').querySelector('span').style.color = '#fff';

            // 自适应窗大小
            window.addEventListener('resize', function () {
                myChart.resize();
            });
        });
    </script>
</body>

</html>