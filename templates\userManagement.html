{% extends "main.html" %}

{% block content %}
<style>
    /* 主容器样式 - 灰色背景 */
    .user-management-container {
        background-color: #f5f7fa;
        min-height: calc(100vh - 120px);
        padding: 10px 20px 20px 20px; /* 顶部10px间距 */
    }

    #userGrid {
        height: calc(100vh - 200px) !important;
        min-height: 400px !important;
    }

    .page-header {
        background: white;
        padding: 20px 24px;
        margin-bottom: 16px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .page-title {
        font-size: 20px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }

    .add-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .add-btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    /* 现代化模态窗口样式 */
    .modern-modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border: none;
        width: 500px;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            opacity: 0;
            transform: translateY(-50px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }

    .modal-header {
        padding: 24px 24px 0 24px;
        border-bottom: none;
    }

    .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin: 0;
    }

    .modal-body {
        padding: 24px;
    }

    .form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .form-grid .form-group.full-width {
        grid-column: 1 / -1;
    }

    .form-group {
        margin-bottom: 0;
    }

    .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #374151;
        font-size: 14px;
    }

    .form-input {
        width: 100%;
        padding: 10px 12px;
        border: 1.5px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
        background-color: #fff;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-input.error {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .form-input.checking {
        border-color: #f59e0b;
        box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
    }

    .form-input.success {
        border-color: #10b981;
        box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
    }

    .form-input:disabled {
        background-color: #f3f4f6 !important;
        cursor: not-allowed !important;
        color: #6b7280;
    }

    .form-select {
        width: 100%;
        padding: 10px 12px;
        border: 1.5px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        background-color: #fff;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .form-select:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-help {
        font-size: 12px;
        color: #6b7280;
        margin-top: 4px;
    }

    .form-error {
        font-size: 12px;
        color: #ef4444;
        margin-top: 4px;
        display: none;
    }

    .modal-footer {
        padding: 16px 24px 24px 24px;
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }

    .btn {
        padding: 10px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        border: none;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    .btn-secondary {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
    }

    .btn-secondary:hover {
        background: #e5e7eb;
    }

    .close-btn {
        position: absolute;
        right: 16px;
        top: 16px;
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: #6b7280;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .close-btn:hover {
        background: #f3f4f6;
        color: #374151;
    }

    /* 现代化确认对话框 */
    .confirm-modal {
        display: none;
        position: fixed;
        z-index: 1001;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
    }

    .confirm-content {
        background-color: #fefefe;
        margin: 15% auto;
        padding: 0;
        border: none;
        width: 450px;
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        animation: modalSlideIn 0.3s ease-out;
    }

    .confirm-header {
        padding: 24px 24px 16px 24px;
        text-align: center;
    }

    .confirm-icon {
        width: 48px;
        height: 48px;
        margin: 0 auto 16px;
        background: #fee2e2;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #dc2626;
        font-size: 24px;
    }

    .confirm-title {
        font-size: 18px;
        font-weight: 600;
        color: #1f2937;
        margin: 0 0 8px 0;
    }

    .confirm-message {
        color: #6b7280;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 16px;
    }

    .confirm-input-group {
        text-align: left;
        margin-top: 16px;
    }

    .confirm-input-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #374151;
        margin-bottom: 8px;
    }

    .confirm-input {
        width: 100%;
        padding: 10px 12px;
        border: 1.5px solid #d1d5db;
        border-radius: 6px;
        font-size: 14px;
        transition: all 0.2s ease;
    }

    .confirm-input:focus {
        outline: none;
        border-color: #dc2626;
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
    }

    .confirm-input.error {
        border-color: #ef4444;
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
    }

    .confirm-error {
        color: #ef4444;
        font-size: 12px;
        margin-top: 4px;
        display: none;
    }

    .confirm-footer {
        padding: 16px 24px 24px 24px;
        display: flex;
        justify-content: center;
        gap: 12px;
    }

    .btn-danger {
        background: #dc2626;
        color: white;
    }

    .btn-danger:hover:not(:disabled) {
        background: #b91c1c;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
    }

    .btn-danger:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    /* 现代化通知样式 */
    .notification {
        position: fixed;
        top: 80px; /* 调整位置避免被导航栏遮挡 */
        right: 20px;
        z-index: 9999; /* 提高z-index确保在最上层 */
        min-width: 300px;
        max-width: 400px;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: flex-start;
        gap: 12px;
        animation: notificationSlideIn 0.3s ease-out;
    }

    @keyframes notificationSlideIn {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    .notification.success {
        background: #f0fdf4;
        border: 1px solid #bbf7d0;
        color: #166534;
    }

    .notification.error {
        background: #fef2f2;
        border: 1px solid #fecaca;
        color: #dc2626;
    }

    .notification.warning {
        background: #fffbeb;
        border: 1px solid #fed7aa;
        color: #d97706;
    }

    .notification-icon {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        margin-top: 2px;
    }

    .notification-content {
        flex: 1;
    }

    .notification-title {
        font-weight: 600;
        margin-bottom: 4px;
    }

    .notification-message {
        font-size: 14px;
        opacity: 0.8;
    }

    .notification-close {
        background: none;
        border: none;
        cursor: pointer;
        opacity: 0.5;
        transition: opacity 0.2s ease;
    }

    .notification-close:hover {
        opacity: 1;
    }

    /* 状态标签样式 */
    .status-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-badge.active {
        background: #dcfce7;
        color: #166534;
    }

    .status-badge.inactive {
        background: #fee2e2;
        color: #dc2626;
    }

    .role-badge {
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .role-badge.admin {
        background: #ddd6fe;
        color: #7c3aed;
    }

    .role-badge.user {
        background: #dbeafe;
        color: #2563eb;
    }

    .role-badge.viewer {
        background: #f3f4f6;
        color: #6b7280;
    }
</style>

<div class="user-management-container">
    <div class="page-header">
        <h1 class="page-title">用户列表</h1>
        <button id="addUserBtn" class="add-btn">
            <i class="fas fa-plus"></i>
            添加用户
        </button>
    </div>

    <div id="userGrid" class="ag-theme-quartz"></div>
</div>

<!-- 现代化用户表单模态窗口 -->
<div id="userModal" class="modern-modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title" id="modalTitle">添加用户</h2>
            <button class="close-btn" id="closeModal">&times;</button>
        </div>
        <div class="modal-body">
            <form id="userForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label class="form-label" for="user_id">登录账号 *</label>
                        <input type="text" id="user_id" name="user_id" class="form-input" required>
                        <div class="form-error" id="user_id_error">请输入登录账号</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="user_name">用户名称 *</label>
                        <input type="text" id="user_name" name="user_name" class="form-input" required>
                        <div class="form-error" id="user_name_error">请输入用户名称</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="password">密码 <span id="password_required">*</span></label>
                        <input type="password" id="password" name="password" class="form-input">
                        <div class="form-help" id="password_help">编辑时留空表示不修改密码</div>
                        <div class="form-error" id="password_error">请输入密码</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="role">角色 *</label>
                        <select id="role" name="role" class="form-select" required>
                            <option value="user">普通用户</option>
                            <option value="admin">管理员</option>
                            <option value="viewer">访客</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="email">邮箱</label>
                        <input type="email" id="email" name="email" class="form-input">
                        <div class="form-error" id="email_error">请输入有效的邮箱地址</div>
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="phone">电话</label>
                        <input type="tel" id="phone" name="phone" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="department">部门</label>
                        <input type="text" id="department" name="department" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="status">状态 *</label>
                        <select id="status" name="status" class="form-select" required>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label class="form-label" for="remark">备注</label>
                        <input type="text" id="remark" name="remark" class="form-input">
                    </div>
                </div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" id="cancelBtn">
                <i class="fas fa-times"></i>
                取消
            </button>
            <button type="button" class="btn btn-primary" id="saveBtn">
                <i class="fas fa-save"></i>
                保存
            </button>
        </div>
    </div>
</div>

<!-- 现代化确认对话框 -->
<div id="confirmModal" class="confirm-modal">
    <div class="confirm-content">
        <div class="confirm-header">
            <div class="confirm-icon">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h3 class="confirm-title">确认删除用户</h3>
            <p class="confirm-message">
                此操作将永久删除用户 <strong id="deleteUserName"></strong>，此操作不可撤销。
            </p>
            <div class="confirm-input-group">
                <label class="confirm-input-label">
                    请输入用户名 "<span id="confirmUserName"></span>" 以确认删除：
                </label>
                <input type="text" id="confirmInput" class="confirm-input" placeholder="请输入用户名">
                <div class="confirm-error" id="confirmInputError">用户名不匹配，请重新输入</div>
            </div>
        </div>
        <div class="confirm-footer">
            <button type="button" class="btn btn-secondary" id="confirmCancel">
                <i class="fas fa-times"></i>
                取消
            </button>
            <button type="button" class="btn btn-danger" id="confirmDelete" disabled>
                <i class="fas fa-trash"></i>
                确认删除
            </button>
        </div>
    </div>
</div>

<script>
    let isEditMode = false;
    let deleteUserId = null;
    let deleteUserData = null;
    let userIdCheckTimeout = null; // 用于防抖的定时器

    // 检查用户名可用性
    function checkUserIdAvailability(userId, callback) {
        if (!userId.trim()) {
            callback(false, '请输入登录账号');
            return;
        }

        $.ajax({
            url: '/api/users/check-availability',
            method: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ user_id: userId }),
            success: function(response) {
                callback(response.available, response.message);
            },
            error: function() {
                callback(false, '检查用户名时发生错误');
            }
        });
    }

    // 表单验证函数
    function validateForm() {
        let isValid = true;
        
        // 清除之前的错误状态
        document.querySelectorAll('.form-input').forEach(input => {
            input.classList.remove('error', 'checking', 'success');
        });
        document.querySelectorAll('.form-error').forEach(error => {
            error.style.display = 'none';
        });

        // 验证登录账号
        const userId = document.getElementById('user_id').value.trim();
        if (!userId) {
            showFieldError('user_id', '请输入登录账号');
            isValid = false;
        }

        // 验证用户名称
        const userName = document.getElementById('user_name').value.trim();
        if (!userName) {
            showFieldError('user_name', '请输入用户名称');
            isValid = false;
        }

        // 验证密码（新增时必填）
        const password = document.getElementById('password').value;
        if (!isEditMode && !password) {
            showFieldError('password', '请输入密码');
            isValid = false;
        }

        // 验证邮箱格式（如果填写了）
        const email = document.getElementById('email').value.trim();
        if (email && !isValidEmail(email)) {
            showFieldError('email', '请输入有效的邮箱地址');
            isValid = false;
        }

        return isValid;
    }

    // 异步验证表单（包括用户名重复检查）
    function validateFormAsync(callback) {
        if (!validateForm()) {
            callback(false);
            return;
        }

        const userId = document.getElementById('user_id').value.trim();
        
        // 如果是编辑模式，跳过用户名重复检查
        if (isEditMode) {
            callback(true);
            return;
        }

        // 新增模式下检查用户名是否重复
        checkUserIdAvailability(userId, function(available, message) {
            if (!available) {
                showFieldError('user_id', message);
                callback(false);
            } else {
                callback(true);
            }
        });
    }

    function showFieldError(fieldId, message) {
        const field = document.getElementById(fieldId);
        const error = document.getElementById(fieldId + '_error');
        field.classList.add('error');
        error.textContent = message;
        error.style.display = 'block';
    }

    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // 现代化通知系统
    function showNotification(type, title, message) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        
        const iconMap = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle'
        };
        
        notification.innerHTML = `
            <div class="notification-icon">
                <i class="${iconMap[type]}"></i>
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        document.body.appendChild(notification);
        
        // 自动关闭
        setTimeout(() => {
            notification.style.animation = 'notificationSlideIn 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 4000);
        
        // 点击关闭
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.style.animation = 'notificationSlideIn 0.3s ease-out reverse';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        });
    }

    // 现代化确认对话框
    function showConfirmDialog(userData, callback) {
        deleteUserData = userData;
        const confirmModal = document.getElementById('confirmModal');
        const confirmInput = document.getElementById('confirmInput');
        const confirmButton = document.getElementById('confirmDelete');
        const confirmError = document.getElementById('confirmInputError');
        
        // 设置用户信息
        document.getElementById('deleteUserName').textContent = userData.user_name;
        document.getElementById('confirmUserName').textContent = userData.user_name;
        
        // 重置输入框状态
        confirmInput.value = '';
        confirmInput.classList.remove('error');
        confirmError.style.display = 'none';
        confirmButton.disabled = true;
        
        confirmModal.style.display = 'block';
        
        // 输入框实时验证
        confirmInput.oninput = function() {
            const inputValue = this.value.trim();
            const isValid = inputValue === userData.user_name;
            
            if (inputValue === '') {
                // 空输入时重置状态
                this.classList.remove('error');
                confirmError.style.display = 'none';
                confirmButton.disabled = true;
            } else if (isValid) {
                // 输入正确
                this.classList.remove('error');
                confirmError.style.display = 'none';
                confirmButton.disabled = false;
            } else {
                // 输入错误
                this.classList.add('error');
                confirmError.style.display = 'block';
                confirmButton.disabled = true;
            }
        };
        
        // 取消按钮
        document.getElementById('confirmCancel').onclick = () => {
            confirmModal.style.display = 'none';
            confirmInput.oninput = null; // 清除事件监听
        };
        
        // 确认删除按钮
        document.getElementById('confirmDelete').onclick = () => {
            const inputValue = confirmInput.value.trim();
            if (inputValue === userData.user_name) {
                confirmModal.style.display = 'none';
                confirmInput.oninput = null; // 清除事件监听
                callback();
            }
        };
        
        // 点击外部关闭
        confirmModal.onclick = (e) => {
            if (e.target === confirmModal) {
                confirmModal.style.display = 'none';
                confirmInput.oninput = null; // 清除事件监听
            }
        };
        
        // 聚焦到输入框
        setTimeout(() => {
            confirmInput.focus();
        }, 100);
    }

    // AG Grid 中文语言包定义
    const AG_GRID_LOCALE_ZH = {
        page: "页",
        more: "更多",
        to: "到",
        of: "共",
        next: "下一页",
        last: "最后一页",
        first: "第一页",
        previous: "上一页",
        loadingOoo: "加载中...",
        selectAll: "全选",
        searchOoo: "搜索...",
        blanks: "空白",
        filterOoo: "过滤...",
        applyFilter: "应用筛选...",
        equals: "等于",
        notEqual: "不等于",
        lessThan: "小于",
        greaterThan: "大于",
        lessThanOrEqual: "小于或等于",
        greaterThanOrEqual: "大于或等于",
        inRange: "在范围内",
        contains: "包含",
        notContains: "不包含",
        startsWith: "开始于",
        endsWith: "结束于",
        noRowsToShow: "没有可显示的数据",
        pageSize: "每页条数",
        pageSizeSelectorLabel: "每页条数:",
        ariaPageSizeSelector: "每页条数选择器"
    };

    $(document).ready(function() {
        // AG Grid 配置
        const gridOptions = {
            columnDefs: [
                {
                    headerName: "#",
                    valueGetter: "node.rowIndex + 1",
                    width: 60,
                    filter: false
                },
                {
                    headerName: "登录账号",
                    field: "user_id",
                    flex: 1
                },
                {
                    headerName: "用户名",
                    field: "user_name",
                    flex: 1
                },
                {
                    headerName: "邮箱",
                    field: "email",
                    flex: 1
                },
                {
                    headerName: "电话",
                    field: "phone",
                    flex: 1
                },
                {
                    headerName: "角色",
                    field: "role",
                    flex: 1,
                    cellRenderer: params => {
                        const roleMap = {
                            'admin': '<span class="role-badge admin">管理员</span>',
                            'user': '<span class="role-badge user">普通用户</span>',
                            'viewer': '<span class="role-badge viewer">访客</span>'
                        };
                        return roleMap[params.value] || params.value;
                    }
                },
                {
                    headerName: "部门",
                    field: "department",
                    flex: 1
                },
                {
                    headerName: "状态",
                    field: "status",
                    flex: 1,
                    cellRenderer: params => {
                        return params.value === 1 
                            ? '<span class="status-badge active">启用</span>'
                            : '<span class="status-badge inactive">禁用</span>';
                    }
                },
                {
                    headerName: "操作",
                    field: "actions",
                    flex: 1,
                    sortable: false,
                    filter: false,
                    cellRenderer: params => {
                        const buttons = [];
                        buttons.push(`
                            <button class="ui mini primary button edit-btn" data-id="${params.data.id}" style="margin-right: 5px;">
                                <i class="edit icon"></i>编辑
                            </button>
                        `);
                        
                        if (params.data.role !== 'admin') {
                            buttons.push(`
                                <button class="ui mini negative button delete-btn" data-id="${params.data.id}">
                                    <i class="trash icon"></i>删除
                                </button>
                            `);
                        }
                        
                        return buttons.join('');
                    }
                }
            ],
            defaultColDef: {
                sortable: true,
                filter: true,
                resizable: true
            },
            rowData: [],
            pagination: true,
            paginationPageSize: 10,  // 每页显示10条数据
            paginationPageSizeSelector: [10, 20, 50, 100],
            localeText: AG_GRID_LOCALE_ZH
        };

        // 初始化表格
        new agGrid.Grid(document.querySelector('#userGrid'), gridOptions);

        // 模态窗口控制
        const modal = document.getElementById('userModal');
        const addBtn = document.getElementById('addUserBtn');
        const closeBtn = document.getElementById('closeModal');
        const cancelBtn = document.getElementById('cancelBtn');
        const saveBtn = document.getElementById('saveBtn');

        function openModal(title = '添加用户', editMode = false) {
            isEditMode = editMode;
            document.getElementById('modalTitle').textContent = title;
            
            // 根据模式调整密码字段
            const passwordRequired = document.getElementById('password_required');
            const passwordHelp = document.getElementById('password_help');
            const passwordField = document.getElementById('password');
            const userIdField = document.getElementById('user_id');
            
            if (editMode) {
                passwordRequired.style.display = 'none';
                passwordHelp.style.display = 'block';
                passwordField.removeAttribute('required');
                // 编辑模式下禁用用户ID字段
                userIdField.disabled = true;
                userIdField.style.backgroundColor = '#f3f4f6';
                userIdField.style.cursor = 'not-allowed';
            } else {
                passwordRequired.style.display = 'inline';
                passwordHelp.style.display = 'none';
                passwordField.setAttribute('required', 'required');
                // 新增模式下启用用户ID字段
                userIdField.disabled = false;
                userIdField.style.backgroundColor = '#fff';
                userIdField.style.cursor = 'text';
            }
            
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
            document.getElementById('userForm').reset();
            
            // 清除用户名检查定时器
            if (userIdCheckTimeout) {
                clearTimeout(userIdCheckTimeout);
                userIdCheckTimeout = null;
            }
            
            // 清除错误状态
            document.querySelectorAll('.form-input').forEach(input => {
                input.classList.remove('error', 'checking', 'success');
            });
            document.querySelectorAll('.form-error').forEach(error => {
                error.style.display = 'none';
            });
        }

        addBtn.addEventListener('click', () => {
            openModal('添加用户', false);
        });

        closeBtn.addEventListener('click', closeModal);
        cancelBtn.addEventListener('click', closeModal);

        // 实时用户名检查（仅在新增模式下）
        document.getElementById('user_id').addEventListener('input', function() {
            if (isEditMode) return; // 编辑模式下不检查
            
            const userId = this.value.trim();
            const errorElement = document.getElementById('user_id_error');
            
            // 清除之前的定时器
            if (userIdCheckTimeout) {
                clearTimeout(userIdCheckTimeout);
            }
            
            // 清除所有状态
            this.classList.remove('error', 'checking', 'success');
            errorElement.style.display = 'none';
            
            if (!userId) {
                return; // 空值时不检查
            }
            
            // 显示检查状态
            this.classList.add('checking');
            
            // 防抖：500ms后执行检查
            userIdCheckTimeout = setTimeout(() => {
                checkUserIdAvailability(userId, function(available, message) {
                    const field = document.getElementById('user_id');
                    const error = document.getElementById('user_id_error');
                    
                    // 清除检查状态
                    field.classList.remove('checking');
                    
                    if (!available) {
                        field.classList.add('error');
                        error.textContent = message;
                        error.style.display = 'block';
                    } else {
                        field.classList.add('success');
                        error.style.display = 'none';
                    }
                });
            }, 500);
        });

        // 点击模态窗口外部关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                closeModal();
            }
        });

        // 表格内按钮点击事件
        document.querySelector('#userGrid').addEventListener('click', function(event) {
            const button = event.target.closest('button');
            if (!button) return;

            const id = button.getAttribute('data-id');
            if (button.classList.contains('edit-btn')) {
                editUser(id);
            } else if (button.classList.contains('delete-btn')) {
                // 从表格数据中查找用户信息
                let userData = null;
                gridOptions.api.forEachNode(function(node) {
                    if (node.data.id == id) {
                        userData = node.data;
                    }
                });
                
                if (userData) {
                    deleteUserId = id;
                    showConfirmDialog(userData, () => deleteUser(deleteUserId));
                } else {
                    showNotification('error', '错误', '无法获取用户信息');
                }
            }
        });

        // 保存用户
        saveBtn.addEventListener('click', function() {
            // 使用异步验证
            validateFormAsync(function(isValid) {
                if (!isValid) {
                    return;
                }

                const formData = {
                    user_id: $("#user_id").val(),
                    user_name: $("#user_name").val(),
                    password: $("#password").val(),
                    email: $("#email").val(),
                    phone: $("#phone").val(),
                    role: $("#role").val(),
                    department: $("#department").val(),
                    status: parseInt($("#status").val()),
                    remark: $("#remark").val()
                };

                $.ajax({
                    url: '/api/users',
                    method: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function(response) {
                        if (response.success) {
                            closeModal();
                            loadUsers();
                            showNotification('success', '操作成功', response.message);
                        } else {
                            showNotification('error', '操作失败', response.message);
                        }
                    },
                    error: function() {
                        showNotification('error', '操作失败', '网络错误，请稍后重试');
                    }
                });
            });
        });

        // 加载用户数据
        function loadUsers() {
            $.ajax({
                url: '/api/users',
                method: 'GET',
                success: function(response) {
                    gridOptions.api.setRowData(response.users);
                },
                error: function() {
                    showNotification('error', '加载失败', '无法加载用户数据');
                }
            });
        }

        // 编辑用户
        function editUser(id) {
            $.ajax({
                url: `/api/users/${id}`,
                method: 'GET',
                success: function(response) {
                    const user = response.user;
                    $("#user_id").val(user.user_id);
                    $("#user_name").val(user.user_name);
                    $("#email").val(user.email);
                    $("#phone").val(user.phone);
                    $("#role").val(user.role);
                    $("#department").val(user.department);
                    $("#status").val(user.status);
                    $("#remark").val(user.remark);
                    
                    openModal('编辑用户', true);
                },
                error: function() {
                    showNotification('error', '加载失败', '无法加载用户信息');
                }
            });
        }

        // 删除用户
        function deleteUser(id) {
            $.ajax({
                url: `/api/users/${id}`,
                method: 'DELETE',
                success: function(response) {
                    if (response.success) {
                        loadUsers();
                        showNotification('success', '删除成功', response.message);
                    } else {
                        showNotification('error', '删除失败', response.message);
                    }
                },
                error: function() {
                    showNotification('error', '删除失败', '网络错误，请稍后重试');
                }
            });
        }

        // 初始加载用户数据
        loadUsers();
    });
</script>
{% endblock %} 