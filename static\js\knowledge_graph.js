document.addEventListener('DOMContentLoaded', function() {
    let myChart = null;
    let graphData = null;
    let progressEventSource = null; // Keep a reference to EventSource
    const loading = document.getElementById('loading');
    const errorMessage = document.getElementById('error-message');
    
    // 定义颜色方案
    const colors = [
        '#5470c6', '#91cc75', '#fac858', '#ee6666',
        '#73c0de', '#3ba272', '#fc8452', '#9a60b4'
    ];
    
    // 布局类型
    let currentLayout = 'force';

    // 显示加载中
    function showLoading() {
        loading.style.display = 'flex';
        errorMessage.style.display = 'none';
    }

    // 隐藏加载中
    function hideLoading() {
        loading.style.display = 'none';
    }

    // 显示错误信息
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';
        hideLoading();
    }
    
    // 初始化图表
    function initChart() {
        if (myChart !== null) {
            myChart.dispose();
        }
        myChart = echarts.init(document.getElementById('graphContainer'));
        
        // 窗口大小变化时，重新调整图表大小
        window.addEventListener('resize', function() {
            if (myChart) {
                myChart.resize();
            }
        });
        
        return myChart;
    }
    
    // 渲染知识图谱
    function renderGraph(data) {
        if (!myChart) {
            myChart = initChart();
        }
        
        // 处理节点数据
        const nodes = data.nodes.map((node, index) => ({
            id: node.id,
            name: node.name,
            value: 1, // 默认大小
            symbolSize: 30, // 节点大小
            itemStyle: {
                color: colors[index % colors.length]
            },
            attributes: node.attributes || {}
        }));
        
        // 处理连接数据
        const links = data.links.map(link => ({
            source: link.source,
            target: link.target,
            value: link.confidence,
            name: link.type,
            lineStyle: {
                width: 1 + link.confidence * 3,
                opacity: 0.7
            },
            label: {
                show: true,
                formatter: link.type,
                fontSize: 10
            },
            evidence: link.evidence
        }));
        
        // 配置图表选项
        const option = {
            backgroundColor: '#F5F5F5',
            title: {
                show: false
            },
            tooltip: {
                confine: true,
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderColor: '#ccc',
                borderWidth: 1,
                padding: [8, 12],
                textStyle: {
                    color: '#333',
                    fontSize: 12,
                    lineHeight: 20
                },
                formatter: function(params) {
                    if (params.dataType === 'node') {
                        // 节点提示信息
                        let attributesHtml = '';
                        if (params.data.attributes) {
                            for (const [key, value] of Object.entries(params.data.attributes)) {
                                attributesHtml += `<div style="margin: 5px 0">
                                    <span style="color: #666">${key}：</span>${value}
                                </div>`;
                            }
                        }
                        
                        return `<div style="min-width: 150px; max-width: 300px;">
                            <div style="font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #eee; padding-bottom: 5px; word-break: break-all;">
                                ${params.data.name}
                            </div>
                            ${attributesHtml}
                        </div>`;
                    } else {
                        // 连接提示信息
                        return `<div style="min-width: 150px; max-width: 300px;">
                            <div style="font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #eee; padding-bottom: 5px; word-break: break-all;">
                                ${params.data.name}
                            </div>
                            <div style="margin: 5px 0">
                                <span style="color: #666">置信度：</span>${(params.data.value * 100).toFixed(1)}%
                            </div>
                            <div style="margin: 5px 0">
                                <span style="color: #666">证据：</span>
                                <div style="margin-top: 3px; white-space: pre-line; word-break: break-all;">${params.data.evidence}</div>
                            </div>
                        </div>`;
                    }
                }
            },
            graphic: [{
                type: 'text',
                left: 20,
                top: 20,
                style: {
                    text: `共 ${nodes.length} 个疾病节点, ${links.length} 个关联关系`,
                    fontSize: 12,
                    fill: '#666',
                    width: 300,
                    overflow: 'break'
                }
            }],
            series: [{
                type: 'graph',
                layout: currentLayout,
                force: {
                    repulsion: 100,
                    gravity: 0.1,
                    edgeLength: 80,
                    layoutAnimation: true
                },
                circular: {
                    rotateLabel: true
                },
                data: nodes,
                links: links,
                categories: nodes.map(node => ({ name: node.name })),
                roam: true,
                label: {
                    show: true,
                    position: 'right',
                    formatter: '{b}',
                    fontSize: 12,
                    fontWeight: 'normal'
                },
                lineStyle: {
                    color: 'source',
                    curveness: 0.3
                },
                emphasis: {
                    focus: 'adjacency',
                    lineStyle: {
                        width: 4
                    }
                }
            }]
        };
        
        // 设置图表选项
        myChart.setOption(option);
        
        // 点击事件处理
        myChart.on('click', function(params) {
            if (params.dataType === 'node') {
                showNodeDetails(params.data);
            } else {
                showEdgeDetails(params.data);
            }
        });
        
        hideLoading();
    }

    // 显示节点详情
    function showNodeDetails(node) {
        // 隐藏初始消息
        document.querySelector('.initial-message').style.display = 'none';
        document.querySelector('.detail-content').style.display = 'block';
        
        // 设置关系类型为空
        document.getElementById('relationshipType').textContent = '节点详情';
        
        // 设置疾病信息
        document.getElementById('disease1').textContent = node.name;
        document.getElementById('disease2').textContent = '';
        
        // 设置详细描述
        let description = '';
        if (node.attributes) {
            for (const [key, value] of Object.entries(node.attributes)) {
                description += `${key}: ${value}<br>`;
            }
        }
        document.getElementById('detailDescription').innerHTML = description || '无详细信息';
    }

    // 显示边详情
    function showEdgeDetails(edge) {
        // 隐藏初始消息
        document.querySelector('.initial-message').style.display = 'none';
        document.querySelector('.detail-content').style.display = 'block';
        
        // 设置关系类型
        document.getElementById('relationshipType').textContent = edge.name;
        
        // 获取源节点和目标节点
        const sourceNode = graphData.nodes.find(n => n.id === edge.source);
        const targetNode = graphData.nodes.find(n => n.id === edge.target);
        
        // 设置疾病信息
        document.getElementById('disease1').textContent = sourceNode ? sourceNode.name : edge.source;
        document.getElementById('disease2').textContent = targetNode ? targetNode.name : edge.target;
        
        // 设置详细描述
        const confidence = (edge.value * 100).toFixed(1);
        const description = `
            <strong>置信度:</strong> ${confidence}%<br>
            <strong>证据:</strong> ${edge.evidence}
        `;
        document.getElementById('detailDescription').innerHTML = description;
    }

    // 刷新图谱数据
    async function refreshGraph() {
        const refreshButton = document.getElementById('refreshGraph');
        const progressContainer = document.getElementById('refreshProgressContainer');
        const progressBar = document.getElementById('refreshProgressBar');
        const progressStatus = document.getElementById('refreshProgressStatus');

        // 防止多次刷新
        if (refreshButton.disabled) {
            return;
        }

        refreshButton.disabled = true;
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';
        progressBar.textContent = '0%';
        progressBar.classList.remove('bg-success', 'bg-danger');
        progressStatus.textContent = '正在启动刷新过程...';

        // 关闭已存在的EventSource
        if (progressEventSource) {
            progressEventSource.close();
        }

        progressEventSource = new EventSource('/api/knowledge-graph/progress');

        progressEventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                progressBar.style.width = data.progress + '%';
                progressBar.textContent = data.progress + '%';
                progressStatus.textContent = data.status;

                if (data.progress >= 100) {
                    progressStatus.textContent = data.status; // 最终状态
                    progressBar.classList.add('bg-success'); 
                    if (progressEventSource) {
                        progressEventSource.close();
                        progressEventSource = null;
                    }
                    refreshButton.disabled = false;
                    // 可选：延迟隐藏进度条
                    setTimeout(() => {
                        progressContainer.style.display = 'none';
                    }, 3000);
                }
            } catch (e) {
                console.error('Error parsing progress event data:', e, event.data);
                progressStatus.textContent = '处理进度更新时出错。';
                progressBar.classList.add('bg-danger');
                if (progressEventSource) {
                    progressEventSource.close();
                    progressEventSource = null;
                }
                refreshButton.disabled = false;
            }
        };

        progressEventSource.onerror = function(error) {
            console.error('EventSource failed:', error);
            progressStatus.textContent = '与服务器的进度连接失败。请重试。';
            progressBar.classList.add('bg-danger');
            if (progressEventSource) {
                progressEventSource.close();
                progressEventSource = null;
            }
            refreshButton.disabled = false;
        };

        try {
            // 发送POST请求刷新数据
            const response = await fetch('/api/knowledge-graph', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({}),
            });

            const result = await response.json();

            if (result.success) {
                // 刷新成功后重新加载数据
                await loadGraphData();
                progressStatus.textContent = "数据已成功更新并重新加载。";
            } else {
                progressStatus.textContent = "更新失败: " + result.message;
                progressBar.classList.add('bg-danger');
            }
        } catch (error) {
            console.error('刷新数据出错:', error);
            progressStatus.textContent = "刷新数据出错，请查看控制台获取详细信息。";
            progressBar.classList.add('bg-danger');
            refreshButton.disabled = false;
        }
        // EventSource将由'if (data.progress >= 100)'块关闭
        // 或者如果POST本身失败，则由上面的catch块关闭
    }

    // 加载图谱数据
    async function loadGraphData() {
        showLoading();
        errorMessage.style.display = 'none';
        
        try {
            const response = await fetch('/api/knowledge-graph');
            const data = await response.json();

            if (data.success) {
                graphData = data.data; // 保存数据以供后续使用
                renderGraph(graphData);
            } else {
                showError('加载数据失败：' + data.message);
            }
        } catch (error) {
            console.error('加载数据出错：', error);
            showError('加载数据出错，请查看控制台获取详细信息');
        }
    }

    // 绑定按钮事件
    document.getElementById('refreshGraph').addEventListener('click', refreshGraph);

    // 切换布局
    document.getElementById('changeLayout').addEventListener('click', function() {
        if (!myChart || !graphData) return;
        
        // 在力导向和环形布局之间切换
        currentLayout = currentLayout === 'force' ? 'circular' : 'force';
        
        const option = myChart.getOption();
        option.series[0].layout = currentLayout;
        myChart.setOption(option);
    });
    
    // 重置视图
    document.getElementById('resetZoom').addEventListener('click', function() {
        if (myChart) {
            myChart.dispatchAction({
                type: 'restore'
            });
        }
    });

    // --- 筛选与搜索功能实现 ---
    let currentFilter = {
        disease: '', // 疾病名称
        relationTypes: [], // 关系类型
        confidenceMin: 0.0, // 置信度最小值
        confidenceMax: 1.0  // 置信度最大值
    };

    // 监听搜索框
    document.getElementById('diseaseSearch').addEventListener('input', function(e) {
        currentFilter.disease = e.target.value.trim();
        applyGraphFilter();
    });

    // 监听关系类型多选
    $('#relationTypeFilter').dropdown({
        onChange: function(value, text, $choice) {
            // value 可能为字符串或数组
            if (Array.isArray(value)) {
                currentFilter.relationTypes = value.filter(v => v !== '');
            } else if (value) {
                currentFilter.relationTypes = [value].filter(v => v !== '');
            } else {
                currentFilter.relationTypes = [];
            }
            applyGraphFilter();
        }
    });

    // 初始化jQuery UI范围滑块
    $(function() {
        $("#confidenceRangeSlider").slider({
            range: true,
            min: 0,
            max: 1,
            step: 0.01,
            values: [0, 1],
            slide: function(event, ui) {
                // 更新滑块值显示
                currentFilter.confidenceMin = ui.values[0];
                currentFilter.confidenceMax = ui.values[1];
                
                // 更新显示的数值
                $("#confidenceMinValue").text(ui.values[0].toFixed(2));
                $("#confidenceMaxValue").text(ui.values[1].toFixed(2));
                
                // 应用筛选
                applyGraphFilter();
            }
        });
        
        // 初始化显示的数值
        $("#confidenceMinValue").text($("#confidenceRangeSlider").slider("values", 0).toFixed(2));
        $("#confidenceMaxValue").text($("#confidenceRangeSlider").slider("values", 1).toFixed(2));
    });

    // 核心筛选逻辑
    function applyGraphFilter() {
        if (!graphData) return;
        let filteredNodes = graphData.nodes;
        let filteredLinks = graphData.links;

        // 1. 疾病名称搜索（模糊匹配）
        if (currentFilter.disease) {
            const kw = currentFilter.disease.toLowerCase();
            filteredNodes = filteredNodes.filter(n => n.name.toLowerCase().includes(kw));
            // 只保留与这些节点直接相关的边
            const nodeIds = new Set(filteredNodes.map(n => n.id));
            filteredLinks = filteredLinks.filter(l => nodeIds.has(l.source) || nodeIds.has(l.target));
            // 反向补全节点（边涉及的节点也要显示）
            const relatedIds = new Set([...filteredLinks.map(l => l.source), ...filteredLinks.map(l => l.target)]);
            filteredNodes = graphData.nodes.filter(n => nodeIds.has(n.id) || relatedIds.has(n.id));
        }

        // 2. 关系类型过滤
        if (currentFilter.relationTypes.length > 0) {
            filteredLinks = filteredLinks.filter(l => {
                // 类型支持 strong/medium/weak/positive/negative
                let match = false;
                if (currentFilter.relationTypes.includes('strong') && l.type === '强相关') match = true;
                if (currentFilter.relationTypes.includes('medium') && l.type === '中相关') match = true;
                if (currentFilter.relationTypes.includes('weak') && l.type === '弱相关') match = true;
                if (currentFilter.relationTypes.includes('positive') && l.type === '正相关') match = true;
                if (currentFilter.relationTypes.includes('negative') && l.type === '负相关') match = true;
                return match;
            });
            // 只保留涉及这些边的节点
            const edgeIds = new Set([...filteredLinks.map(l => l.source), ...filteredLinks.map(l => l.target)]);
            filteredNodes = filteredNodes.filter(n => edgeIds.has(n.id));
        }

        // 3. 置信度范围
        filteredLinks = filteredLinks.filter(l => l.confidence >= currentFilter.confidenceMin && l.confidence <= currentFilter.confidenceMax);
        // 只保留涉及这些边的节点
        const edgeIds2 = new Set([...filteredLinks.map(l => l.source), ...filteredLinks.map(l => l.target)]);
        filteredNodes = filteredNodes.filter(n => edgeIds2.has(n.id));

        // 渲染筛选结果
        renderGraph({ nodes: filteredNodes, links: filteredLinks });
    }

    // --- 初始化语义UI下拉 ---
    $(function() {
        $('#relationTypeFilter').dropdown();
    });

    // --- 覆盖原始数据加载，加载后自动应用筛选 ---
    const originRenderGraph = renderGraph;
    renderGraph = function(data) {
        originRenderGraph(data);
    };
    const originLoadGraphData = loadGraphData;
    loadGraphData = async function() {
        await originLoadGraphData();
        applyGraphFilter(); // 数据加载后自动筛选
    };

    // 初始加载数据
    loadGraphData();
});
