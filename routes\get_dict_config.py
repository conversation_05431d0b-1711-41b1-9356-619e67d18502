from flask import jsonify
from model.models import Country, Disease

def handler():
    try:
        # 从数据库获取国家和疾病信息
        countries = Country.query.all()
        diseases = Disease.query.all()
        
        # 转换为字典格式
        config = {
            'countries': [
                {
                    'data_value': country.data_value,
                    'name': country.name,
                    'capital_latitude': country.capital_latitude,
                    'capital_longitude': country.capital_longitude,
                    'population': country.population
                } for country in countries
            ],
            'diseases': [
                {
                    'data_value': disease.data_value,
                    'name': disease.name
                } for disease in diseases
            ]
        }
        
        return jsonify(config)
    except Exception as e:
        return jsonify({'error': str(e)}), 500 