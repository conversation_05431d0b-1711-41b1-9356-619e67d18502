import json
import os
from langdetect import detect
from openai import OpenAI
from urllib.parse import urlparse
from newspaper import Article
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
import threading,queue
import time,jwt,requests
import dashscope
from http import HTTPStatus

def process_data(selected_data):
    # 获取上一级目录的路径
    current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    file_name = 'ai_generate.txt'
    # 结合目录和文件名得到文件的完整路径
    file_path = os.path.join(current_directory, 'static', file_name)
    
    # 确保 static 目录存在
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    
    #检查文件是否存在，如果存在则删除
    if os.path.exists(file_path):
        os.remove(file_path)

    ai_text=''

    try:
        # 使用队列来保存线程的结果
        result_queue = queue.Queue()

        threads = []
        for data in selected_data:
            config_dict = {
                'title': data['title'],
                'date': data['publishedAt'],
                'url': data['url']
            }
            # 创建新线程
            thread = threading.Thread(target=threaded_call_ai, args=(config_dict, result_queue))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 从队列中获取所有线程的结果并写入文件
        with open(file_path, 'a', encoding='utf-8') as file:
            while not result_queue.empty():
                ai_text = result_queue.get()
                file.write(ai_text)

        return {"status": "Done", "file_name": file_name}
        #return "Done"
    except Exception as e:
        print(f"Failed to process data: {e}")
        return {"status": "Failed", "file_name": None}
    
def threaded_call_ai(config_dict, result_queue):
    result_text = call_ai(config_dict)
    result_queue.put(result_text)  # 使用队列存储线程的结果

def call_ai(config_dict):
    result_text=''
    config = load_api_config()
    aiModel=config['ai_model']
    newstitle=config_dict['title']
    newsdate=config_dict['date'] 
    url=config_dict['url'] 
    newContent=getNewsContent(url)
    news_message = f"{newstitle}\n{newsdate}\n{url}\n{newContent}"
    aiModelName=''
    try:
        if aiModel == 'gpt-3.5-turbo-1106':
            news_message = extract_news_content_chatgpt(news_message)
            aiModelName='OpenAI-ChatGPT-3.5'
        elif aiModel == 'gpt-4-0125-preview':
            news_message = extract_news_content_chatgpt(news_message)
            aiModelName='OpenAI-ChatGPT-4.0'
        elif aiModel == 'gpt-4o':
            news_message = extract_news_content_chatgpt(news_message)
            aiModelName='OpenAI-ChatGPT-4o'
        elif aiModel == 'gpt-4o-mini':
            news_message = extract_news_content_chatgpt(news_message)
            aiModelName='OpenAI-ChatGPT-4o mini'    
        elif aiModel == 'claude-3-5-sonnet-20240620':
            news_message = extract_news_content_chatgpt(news_message)
            aiModelName='Anthropic-Claude3.5'
        elif aiModel == 'gemini-pro':
            news_message = extract_news_content_chatgpt(news_message)
            aiModelName='Google-Gemini'
        elif aiModel == 'gemma':
            news_message = extract_news_content_openrouter(news_message)
            aiModelName='Google-Gemma'
        elif aiModel == 'sensenova':
            news_message = extract_news_content_sensenova(news_message)
            aiModelName='商汤-SenseNova'
        elif aiModel == 'qwen':
            news_message = extract_news_content_qwen(news_message)
            aiModelName='阿里-通义千问'
    except Exception as e:
        return f"Error processing news with AI ({aiModel}): {e}\n\n{'*' * 50}\n\n"
    if not news_message:
        news_message = "Error: No content to display"
    result_text = f"AI({aiModelName})摘取信息：\n{news_message}\n\n{'*' * 50}\n\n"
    return result_text

def extract_news_content_chatgpt(news_content):
    try:
        config = load_api_config()
        client = OpenAI(base_url=config['api_endpoint'],api_key=config['api_key']) 
        response = client.chat.completions.create(
            model=config['ai_model'],
            messages=[
                {"role": "system", "content": config['prompt_text']},
                {"role": "user", "content": news_content}
            ]
        )
        extracted_content = response.choices[0].message.content
        return extracted_content
    except Exception as e:
        print("Error occurred: ", e)

def extract_news_content_openrouter(news_content):
    try:
        config = load_api_config()
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key="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",
        )
        completion = client.chat.completions.create(
            extra_headers={
                "HTTP-Referer": "localhost",  # Optional, for including your app on openrouter.ai rankings.
                "X-Title": "AI Write",  # Optional. Shows in rankings on openrouter.ai.
            },
            model="google/gemma-7b-it:free",
            #model="gryphe/mythomist-7b:free",
            #model="mistralai/mistral-7b-instruct:free",
            messages=[
                {"role": "system", "content": config['prompt_text']},
                {"role": "user", "content": news_content},
            ],
        )
        extracted_content = completion.choices[0].message.content
        return extracted_content
    except Exception as e:
        print("Error occurred: ", e)

def extract_news_content_sensenova(news_content):
    try:
        # Load prompt text from local json config
        config = load_api_config()
                
        # Set your Access Key ID and Access Key Secret
        ak = "2bsi5wiRAUnrjjHj73wrDqsHzMr"
        sk = "lP4ynC5mXpSVFGyNmNEcXjBdQdqCHhKC"

        # Concatenate chat dialogue content
        messages = config['prompt_text'] + news_content

        # Generate JWT Token
        def encode_jwt_token(ak, sk):
            headers = {
                "alg": "HS256",
                "typ": "JWT"
            }
            payload = {
                "iss": ak,
                "exp": int(time.time()) + 1800,  # Token expires in 30 minutes
                "nbf": int(time.time()) - 5  # Token is valid 5 seconds before now
            }
            token = jwt.encode(payload, sk, algorithm="HS256", headers=headers)
            return token

        # Generate Authorization Token
        authorization = encode_jwt_token(ak, sk)

        # Set API endpoint and headers
        api_url = "https://api.sensenova.cn/v1/llm/chat-completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {authorization}"
        }

        # Build the request body
        payload = {
            "model": "SenseChat-128K",  # Model ID
            "messages": [
                {
                    "role": "user",
                    "content": messages  # Inquiry content
                }
            ],
            #"max_new_tokens": 10240,  # Expected maximum number of tokens to generate
            "n": 1,  # Number of replies to generate
            "repetition_penalty": 1.05,  # Repetition penalty coefficient
            "temperature": 0.8,  # Sampling temperature parameter
            "top_p": 0.7  # Nucleus sampling parameter
        }

        # Send POST request
        response = requests.post(api_url, headers=headers, data=json.dumps(payload))

        # Process API response
        if response.status_code == 200:
            response_data = response.json()
            # Extract and return content from the response
            for choice in response_data.get('data', {}).get('choices', []):
                return choice.get('message')  # Return the reply content
        else:
            # Handle any errors from the API response
            return "Error:", response.status_code, response.text

    except Exception as e:
        # Handle any exceptions in the process
        print("Error occurred: ", e)
        return None

def extract_news_content_qwen(news_content):
    try:
        config = load_api_config()
        prompt_text=config['prompt_text']
        dashscope.api_key = 'sk-7f77ffd1a1d84ef4bad8175d6bad2981'
        messages = [{'role': 'system', 'content': prompt_text},
                {'role': 'user', 'content': news_content}]

        response = dashscope.Generation.call(
            dashscope.Generation.Models.qwen_max,
            messages=messages,
            result_format='message',  # set the result to be "message" format.
        )
        # Check if response status is OK
        if response.status_code == HTTPStatus.OK:
            # Extract the content value from the response
            content_value = response.output['choices'][0]['message']['content']
            extracted_content = content_value
            return extracted_content
        else:
            # Raise an exception with the error details
            error_details = 'Request id: {}, Status code: {}, error code: {}, error message: {}'.format(
                response.request_id, response.status_code,
                response.code, response.message
            )
            raise Exception(error_details)
            
    except Exception as e:
        # You can handle the exception as needed, for example log it or re-raise it
        raise Exception("An error occurred while extracting news content: {}".format(e))

def getNewsContent(url):
    try:
        default_language='en'
        news = Article(url, language=default_language)
        news.download()
        news.parse()

        # 检查 title 是否为 None 或者空字符串
        if news.title is not None and news.title != "":
            default_language = detect(news.title)

        supported_languages = {
            'ar': 'ar', 'be': 'be', 'bg': 'bg', 'bn': 'bn', 'cs': 'cs', 
            'da': 'da', 'de': 'de', 'el': 'el', 'en': 'en', 'es': 'es', 
            'et': 'et', 'fi': 'fi', 'fr': 'fr', 'he': 'he', 'hi': 'hi', 
            'hr': 'hr', 'hu': 'hu', 'id': 'id', 'it': 'it', 'ja': 'ja', 
            'ko': 'ko', 'lt': 'lt', 'lv': 'lv', 'mk': 'mk', 'nl': 'nl', 
            'no': 'no', 'pl': 'pl', 'pt': 'pt', 'ro': 'ro', 'ru': 'ru', 
            'sk': 'sk', 'sl': 'sl', 'sr': 'sr', 'sv': 'sv', 'sw': 'sw', 
            'ta': 'ta', 'th': 'th', 'tr': 'tr', 'uk': 'uk', 'vi': 'vi', 
            'zh-cn': 'zh'
        }
        
        if default_language in supported_languages:
            default_language = supported_languages[default_language]
            news = Article(url, language=default_language)
            news.download()
            news.parse()

        # 如果newspaper3k能够获取到内容，直接返回
        if news.text:
            return news.text

        # 如果newspaper3k无法获取内容，使用get_news.py中的方法
        from routes.get_news import fetch_content
        content = fetch_content(url)
        if content and not content.startswith('Failed to fetch content'):
            return content
        else:
            return f"无法获取新闻内容: {content}"
            
    except Exception as e:
        print(f"General error in getNewsContent: {str(e)}")
        return f"获取新闻内容时发生错误: {str(e)}"

def load_api_config():
    config_details = {}
    # 获取上一级目录的路径
    current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    config_path = os.path.join(current_directory, 'api_config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8-sig') as json_file:
            config = json.load(json_file)
            config_details['api_endpoint'] = config.get('api_endpoint', '')
            config_details['api_key'] = config.get('api_key', '')
            config_details['ai_model'] = config.get('ai_model', '')
            config_details['prompt_text'] = config.get('prompt_text', '')
    return config_details