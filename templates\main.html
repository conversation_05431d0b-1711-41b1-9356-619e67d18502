<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <!-- Required meta tags -->
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>AI智能系统</title>

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://unpkg.com/ionicons@5.5.0/dist/css/ionicons.min.css">
  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet"
    href="https://unpkg.com/tempusdominus-bootstrap-4@5.39.0/build/css/tempusdominus-bootstrap-4.min.css">
  <!-- iCheck -->
  <link rel="stylesheet" href="https://unpkg.com/icheck-bootstrap@3.0.1/icheck-bootstrap.min.css">
  <!-- JQVMap -->
  <link rel="stylesheet" href="https://unpkg.com/jqvmap@1.5.1/dist/jqvmap.min.css">
  <!-- Theme style -->
  <link rel="stylesheet" href="https://unpkg.com/admin-lte@3.2.0/dist/css/adminlte.min.css">
  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="https://unpkg.com/overlayscrollbars@1.13.3/css/OverlayScrollbars.min.css">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="https://unpkg.com/daterangepicker@3.1.0/daterangepicker.css">
  <!-- summernote -->
  <link rel="stylesheet" href="https://unpkg.com/summernote@0.8.20/dist/summernote-bs4.min.css">

  <!-- Legacy CSS for existing functionality -->
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/jquery-ui.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/quill.snow.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/flatpickr.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/app.css') }}">

  <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">

  <!-- 首先加载jQuery - 必须在所有其他脚本之前 -->
  <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>

  <style>
    /* 自定义样式 */
    .content-wrapper {
      background-color: #f4f6f9;
    }

    .welcome-dashboard {
      padding: 20px;
      background: #f5f7fa;
      min-height: calc(100vh - 250px);
      /* 进一步调整高度计算，确保页脚可见 */
    }

    .welcome-header {
      background: white;
      border-radius: 12px;
      padding: 25px 30px;
      margin-bottom: 25px;
      margin-top: 0;
      /* 移除顶部间距，让红色框紧贴灰色背景 */
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8ecf0;
    }

    .user-info-card {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .user-avatar {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 1.5rem;
      font-weight: bold;
    }

    .user-details h2 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 1.8rem;
      font-weight: 600;
    }

    .user-details .subtitle {
      color: #7f8c8d;
      margin: 0 0 5px 0;
      font-size: 1rem;
    }

    .user-details .login-info {
      color: #95a5a6;
      font-size: 0.9rem;
      margin: 0;
    }

    .section-title {
      color: #2c3e50;
      font-size: 1.4rem;
      font-weight: 600;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .section-title i {
      color: #3498db;
    }

    .quick-actions {
      margin-bottom: 30px;
    }

    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 20px;
    }

    .action-card {
      background: white;
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8ecf0;
      transition: all 0.3s ease;
      text-align: center;
    }

    .action-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .action-icon {
      width: 60px;
      height: 60px;
      border-radius: 12px;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.8rem;
      color: white;
    }

    .action-card.primary .action-icon {
      background: linear-gradient(135deg, #3498db, #2980b9);
    }

    .action-card.success .action-icon {
      background: linear-gradient(135deg, #2ecc71, #27ae60);
    }

    .action-card.warning .action-icon {
      background: linear-gradient(135deg, #f39c12, #e67e22);
    }

    .action-card.danger .action-icon {
      background: linear-gradient(135deg, #e74c3c, #c0392b);
    }

    .action-title {
      font-size: 1.2rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    .action-description {
      color: #7f8c8d;
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: 20px;
    }

    .action-btn {
      background: #3498db;
      color: white;
      border: none;
      padding: 10px 25px;
      border-radius: 8px;
      font-size: 0.9rem;
      font-weight: 500;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
      transition: all 0.3s ease;
    }

    .action-btn:hover {
      background: #2980b9;
      color: white;
      text-decoration: none;
      transform: translateY(-1px);
    }

    .system-modules {
      margin-bottom: 30px;
    }

    .module-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 25px;
    }

    .module-card {
      background: white;
      border-radius: 12px;
      padding: 25px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid #e8ecf0;
      transition: all 0.3s ease;
    }

    .module-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
    }

    .module-header {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #ecf0f1;
    }

    .module-header i {
      font-size: 1.5rem;
      color: #e74c3c;
    }

    .module-header h5 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: #2c3e50;
    }

    .module-description {
      color: #7f8c8d;
      margin-bottom: 20px;
      line-height: 1.6;
    }

    .module-links {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    .module-link {
      display: inline-flex;
      align-items: center;
      gap: 6px;
      padding: 6px 12px;
      background: #f8f9fa;
      color: #495057;
      text-decoration: none;
      border-radius: 6px;
      font-size: 0.85rem;
      transition: all 0.3s ease;
      border: 1px solid #e9ecef;
    }

    .module-link:hover {
      background: #3498db;
      color: white;
      text-decoration: none;
      border-color: #3498db;
    }

    .navbar-badge {
      font-size: 0.75rem;
    }

    .sidebar-dark-primary .nav-sidebar>.nav-item>.nav-link.active {
      background-color: #007bff;
      color: #fff;
    }

    /* 修复navbar用户菜单对齐 */
    .navbar .nav-item.dropdown .nav-link {
      padding: 0.5rem 0.75rem !important;
      display: flex !important;
      align-items: center !important;
    }

    .navbar .nav-item.dropdown .nav-link img {
      vertical-align: middle;
    }

    .navbar .nav-item.dropdown .nav-link span {
      line-height: 1;
      white-space: nowrap;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
      .user-info-card {
        flex-direction: column;
        text-align: center;
      }

      .quick-actions-grid {
        grid-template-columns: 1fr;
      }

      .welcome-dashboard {
        padding: 15px;
      }

      .action-grid {
        grid-template-columns: 1fr;
      }

      .module-grid {
        grid-template-columns: 1fr;
      }

      .user-info-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
      }
    }

    /* 移除Content Header后的布局调整 */
    .content-wrapper {
      padding-top: 0;
      margin-top: 0;
    }

    .content {
      padding-top: 0;
      /* 移除顶部padding */
      padding-bottom: 0;
      /* 移除底部padding */
    }

    /* 确保欢迎界面紧贴顶部 */
    .welcome-dashboard {
      margin-top: 0;
      padding-top: 15px;
      /* 减少顶部padding */
      padding-bottom: 15px;
      /* 减少底部padding */
    }
  </style>
</head>

<body class="hold-transition sidebar-mini layout-fixed">
  <div class="wrapper">

    <!-- Preloader -->
    <div class="preloader flex-column justify-content-center align-items-center">
      <img class="animation__shake" src="{{ url_for('static', filename='images/logo2.png') }}" alt="AdminLTELogo"
        height="60" width="60">
    </div>

    <!-- Navbar -->
    <nav class="main-header navbar navbar-expand navbar-white navbar-light">
      <!-- Left navbar links -->
      <ul class="navbar-nav">
        <li class="nav-item">
          <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
        </li>
        <li class="nav-item d-none d-sm-inline-block">
          <a href="/" class="nav-link">首页</a>
        </li>
      </ul>

      <!-- Right navbar links -->
      <ul class="navbar-nav ml-auto">
        <!-- Navbar Search 
      <li class="nav-item">
        <a class="nav-link" data-widget="navbar-search" href="#" role="button">
          <i class="fas fa-search"></i>
        </a>
        <div class="navbar-search-block">
          <form class="form-inline">
            <div class="input-group input-group-sm">
              <input class="form-control form-control-navbar" type="search" placeholder="搜索" aria-label="Search">
              <div class="input-group-append">
                <button class="btn btn-navbar" type="submit">
                  <i class="fas fa-search"></i>
                </button>
                <button class="btn btn-navbar" type="button" data-widget="navbar-search">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </form>
        </div>
      </li>
      --!>

      <!-- Messages Dropdown Menu -->
        <li class="nav-item dropdown">
          <a class="nav-link" data-toggle="dropdown" href="#">
            <i class="far fa-comments"></i>
            <!-- 消息数量气泡已移除 -->
            <!-- <span class="badge badge-danger navbar-badge">1</span> -->
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <a href="#" class="dropdown-item">
              <div class="media">
                <div class="media-body">
                  <h3 class="dropdown-item-title">
                    系统消息
                    <span class="float-right text-sm text-muted"><i class="fas fa-star"></i></span>
                  </h3>
                  <p class="text-sm">欢迎使用AI智能系统</p>
                  <p class="text-sm text-muted"><i class="far fa-clock mr-1"></i> 刚刚</p>
                </div>
              </div>
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item dropdown-footer">查看所有消息</a>
          </div>
        </li>

        <!-- Notifications Dropdown Menu -->
        <li class="nav-item dropdown">
          <a class="nav-link" data-toggle="dropdown" href="#">
            <i class="far fa-bell"></i>
            <!-- 通知数量气泡已移除 -->
            <!-- <span class="badge badge-warning navbar-badge">1</span> -->
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <!-- 通知数量文本已移除 -->
            <!-- <span class="dropdown-item dropdown-header">1 条通知</span> -->
            <span class="dropdown-item dropdown-header">通知</span>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item" id="viewChangelog">
              <i class="fas fa-info mr-2"></i> AI智能系统 v1.1.0
              <span class="float-right text-muted text-sm">更新说明</span>
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item dropdown-footer">查看所有通知</a>
          </div>
        </li>
        <!-- Dark Mode Toggle Button (moved before user menu) -->
        <li class="nav-item">
          <a class="nav-link" id="darkModeToggle" href="#" role="button">
            <i class="fas fa-moon"></i>
          </a>
        </li>
        <!-- User Menu -->
        <li class="nav-item dropdown">
          <a class="nav-link d-flex align-items-center" data-toggle="dropdown" href="#">
            <img src="{{ url_for('static', filename='images/user_zyd.png') }}" alt="User Avatar"
              class="img-size-32 mr-2 img-circle">
            <span class="d-none d-md-inline">{{ user_name }}</span>
          </a>
          <div class="dropdown-menu dropdown-menu-lg dropdown-menu-right">
            <span class="dropdown-item dropdown-header">{{ user_name }}</span>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item">
              <i class="fas fa-clock mr-2"></i> 登录时间：{{ login_date }}
            </a>
            <div class="dropdown-divider"></div>
            <a href="#" class="dropdown-item" data-logout-link="1">
              <i class="fas fa-sign-out-alt mr-2"></i> 退出登录
            </a>
          </div>
        </li>

        <li class="nav-item">
          <a class="nav-link" data-widget="fullscreen" href="#" role="button">
            <i class="fas fa-expand-arrows-alt"></i>
          </a>
        </li>
      </ul>
    </nav>
    <!-- /.navbar -->

    <!-- Main Sidebar Container -->
    <aside class="main-sidebar sidebar-dark-primary elevation-4">
      <!-- Brand Logo -->
      <a href="/" class="brand-link">
        <img src="{{ url_for('static', filename='images/logo2.png') }}" alt="AdminLTE Logo"
          class="brand-image img-circle elevation-3" style="opacity: .8">
        <span class="brand-text font-weight-light">AI智能系统</span>
      </a>

      <!-- Sidebar -->
      <div class="sidebar">
        <!-- Sidebar user panel (optional) 
      <div class="user-panel mt-3 pb-3 mb-3 d-flex">
        <div class="image">
          <img src="{{ url_for('static', filename='images/user_zyd.png') }}" class="img-circle elevation-2" alt="User Image">
        </div>
        <div class="info">
          <a href="#" class="d-block">{{ user_name }}</a>
        </div>
      </div>
      --!>

      <!-- SidebarSearch Form 
      <div class="form-inline">
        <div class="input-group" data-widget="sidebar-search">
          <input class="form-control form-control-sidebar" type="search" placeholder="搜索功能" aria-label="Search">
          <div class="input-group-append">
            <button class="btn btn-sidebar">
              <i class="fas fa-search fa-fw"></i>
            </button>
          </div>
        </div>
      </div>
      --!>
      <!-- Sidebar Menu -->
        <nav class="mt-2">
          <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
            <!-- Add icons to the links using the .nav-icon class with font-awesome or any other icon font library -->

            <li class="nav-item">
              <a href="/newsSearch" class="nav-link">
                <i class="nav-icon fas fa-newspaper"></i>
                <p>新闻检索</p>
              </a>
            </li>

            <li class="nav-item">
              <a href="/urlAI" class="nav-link">
                <i class="nav-icon fas fa-pencil-alt"></i>
                <p>手动生成</p>
              </a>
            </li>

            <li class="nav-item">
              <a href="/urlAsk" target="_blank" class="nav-link">
                <i class="nav-icon fas fa-search"></i>
                <p>AI搜索</p>
              </a>
            </li>

            <li class="nav-item">
              <a href="/chatBot" target="_blank" class="nav-link">
                <i class="nav-icon fas fa-robot"></i>
                <p>AI对话</p>
              </a>
            </li>

            <li class="nav-item">
              <a href="/newsNow" target="_blank" class="nav-link">
                <i class="nav-icon fas fa-fire"></i>
                <p>实时热门新闻</p>
              </a>
            </li>

            <li class="nav-item">
              <a href="#" class="nav-link">
                <i class="nav-icon fas fa-biohazard"></i>
                <p>
                  传染病分析
                  <i class="fas fa-angle-left right"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="/globe" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>3D地球</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/disease_network" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>传染病关系图</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/knowledge-graph" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>疾病知识图谱</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/urlFigure" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>传染病图表</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/disease_prediction" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>传染病预测(全球)</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/disease_prediction_china" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>传染病预测(中国)</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/disease_prediction_shanghai" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>传染病预测(上海)</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/global_risk_assessment" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>全球传染病风险评估</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/cdc_risk_reports_china" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>重点传染病风险评估报告</p>
                  </a>
                </li>
                {% if current_user.user_id == 'ssc' %}
                <li class="nav-item">
                  <a href="/cdc_news_china" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>中国疾控中心数据</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="/cdc_news_shanghai" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>上海市卫健委数据</p>
                  </a>
                </li>
                {% endif %}
              </ul>
            </li>

            <li class="nav-item">
              <a href="#" class="nav-link">
                <i class="nav-icon fas fa-cogs"></i>
                <p>
                  系统配置
                  <i class="fas fa-angle-left right"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="#" class="nav-link" id="settingsLink">
                    <i class="far fa-circle nav-icon"></i>
                    <p>API & 提示词</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="https://api.pumpkinaigc.online/query" target="_blank" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>API使用量查询</p>
                  </a>
                </li>
                {% if current_user.role == 'admin' %}
                <li class="nav-item">
                  <a href="/userManagement" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>用户管理</p>
                  </a>
                </li>
                {% endif %}
              </ul>
            </li>

            <li class="nav-item">
              <a href="#" class="nav-link">
                <i class="nav-icon fas fa-user"></i>
                <p>
                  用户信息
                  <i class="fas fa-angle-left right"></i>
                </p>
              </a>
              <ul class="nav nav-treeview">
                <li class="nav-item">
                  <a href="#" class="nav-link">
                    <i class="far fa-circle nav-icon"></i>
                    <p>修改密码</p>
                  </a>
                </li>
                <li class="nav-item">
                  <a href="#" class="nav-link" data-logout-link="1">
                    <i class="far fa-circle nav-icon"></i>
                    <p>退出</p>
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </nav>
        <!-- /.sidebar-menu -->
      </div>
      <!-- /.sidebar -->
    </aside>

    <!-- Content Wrapper. Contains page content -->
    <div class="content-wrapper">
      <!-- Content Header区域已完全移除 -->

      <!-- Main content -->
      <section class="content">
        <div class="container-fluid">
          <!-- 页面内容区域 - 子页面将在这里显示 -->
          {% block content %}
          <!-- 现代化仪表板欢迎界面 -->
          <div class="welcome-dashboard">
            <!-- 用户信息头部 -->
            <div class="welcome-header">
              <div class="user-info-card">
                <div class="user-avatar">
                  AI<!-- {{ user_name[0] if user_name else 'U' }}-->
                </div>
                <div class="user-details">
                  <h2>早安，{{ user_name }}，今天又是充满活力的一天！</h2>
                  <p class="subtitle">AI智能系统 · 数据分析可视化 · 一站式解决方案</p>
                  <p class="login-info">登录时间：{{ login_date }}</p>
                </div>
              </div>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
              <h3 class="section-title">
                <i class="fas fa-bolt"></i>
                快速开始
              </h3>
              <div class="action-grid">
                <div class="action-card primary">
                  <div class="action-icon">
                    <i class="fas fa-newspaper"></i>
                  </div>
                  <h5 class="action-title">新闻检索</h5>
                  <p class="action-description">智能搜索新闻资讯，支持多源数据检索和双语搜索</p>
                  <a href="/newsSearch" class="action-btn">
                    <i class="fas fa-arrow-right"></i> 立即使用
                  </a>
                </div>

                <div class="action-card success">
                  <div class="action-icon">
                    <i class="fas fa-pencil-alt"></i>
                  </div>
                  <h5 class="action-title">AI生成</h5>
                  <p class="action-description">手动输入内容，让AI为您创作优质文章和内容</p>
                  <a href="/urlAI" class="action-btn">
                    <i class="fas fa-arrow-right"></i> 开始创作
                  </a>
                </div>

                <div class="action-card warning">
                  <div class="action-icon">
                    <i class="fas fa-search"></i>
                  </div>
                  <h5 class="action-title">AI搜索</h5>
                  <p class="action-description">智能问答系统，快速获得准确信息和专业解答</p>
                  <a href="/urlAsk" target="_blank" class="action-btn">
                    <i class="fas fa-arrow-right"></i> 智能问答
                  </a>
                </div>

                <div class="action-card danger">
                  <div class="action-icon">
                    <i class="fas fa-robot"></i>
                  </div>
                  <h5 class="action-title">AI对话</h5>
                  <p class="action-description">与AI助手进行自然语言对话交流和深度讨论</p>
                  <a href="/chatBot" target="_blank" class="action-btn">
                    <i class="fas fa-arrow-right"></i> 开始对话
                  </a>
                </div>
              </div>
            </div>

            <!-- 系统功能模块 -->
            <div class="system-modules">
              <h3 class="section-title">
                <i class="fas fa-th-large"></i>
                系统功能
              </h3>
              <div class="module-grid">
                <div class="module-card">
                  <div class="module-header">
                    <i class="fas fa-biohazard"></i>
                    <h5>传染病分析系统</h5>
                  </div>
                  <p class="module-description">专业的传染病数据分析和可视化平台，提供全球疾病预测和趋势分析</p>
                  <div class="module-links">
                    <a href="/globe" target="_blank" class="module-link">
                      <i class="mdi mdi-earth"></i> 3D地球
                    </a>
                    <a href="/disease_network" target="_blank" class="module-link">
                      <i class="mdi mdi-graph"></i> 传染病关系图
                    </a>
                    <a href="/knowledge-graph" target="_blank" class="module-link">
                      <i class="mdi mdi-brain"></i> 疾病知识图谱
                    </a>
                    <a href="/urlFigure" target="_blank" class="module-link">
                      <i class="mdi mdi-chart-bar"></i> 传染病图表
                    </a>
                    <a href="/disease_prediction" target="_blank" class="module-link">
                      <i class="mdi mdi-chart-line"></i> 传染病预测(全球)
                    </a>
                    <a href="/disease_prediction_china" target="_blank" class="module-link">
                      <i class="mdi mdi-map-marker"></i> 传染病预测(中国)
                    </a>
                    <a href="/disease_prediction_shanghai" target="_blank" class="module-link">
                      <i class="mdi mdi-city"></i> 传染病预测(上海)
                    </a>
                    <a href="/global_risk_assessment" target="_blank" class="module-link">
                      <i class="mdi mdi-city"></i> 全球传染病事件风险评估
                    </a>
                  </div>
                </div>

                <div class="module-card">
                  <div class="module-header">
                    <i class="fas fa-fire"></i>
                    <h5>实时资讯中心</h5>
                  </div>
                  <p class="module-description">获取最新热门新闻和实时资讯动态，掌握时事热点</p>
                  <div class="module-links">
                    <a href="/newsNow" target="_blank" class="module-link">
                      <i class="mdi mdi-newspaper"></i> 热门新闻
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {% endblock %}
        </div>
      </section>
      <!-- /.content -->
    </div>
    <!-- /.content-wrapper -->

    <footer class="main-footer">
      <strong>Copyright &copy; 2024 <a href="#">shutcm&ssc</a>.</strong>
      All rights reserved.
      <div class="float-right d-none d-sm-inline-block">
        <b>Version</b> 1.1.0
      </div>
    </footer>

    <!-- Control Sidebar -->
    <aside class="control-sidebar control-sidebar-dark">
      <!-- Control sidebar content goes here -->
    </aside>
    <!-- /.control-sidebar -->
  </div>
  <!-- ./wrapper -->

  <!-- Legacy JavaScript for existing functionality - 保持原有顺序 -->
  <script src="{{ url_for('static', filename='js/jquery-ui.js') }}"></script>
  <script src="{{ url_for('static', filename='js/quill.js') }}"></script>
  <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/flatpickr.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/flatpickr_zh.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>

  <!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
  <script>
    $.widget.bridge('uibutton', $.ui.button)
  </script>

  <!-- Bootstrap 4 -->
  <script src="https://unpkg.com/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

  <!-- moment.js MUST be loaded before tempusdominus -->
  <script src="https://unpkg.com/moment@2.30.1/moment.js"></script>

  <!-- Tempusdominus Bootstrap 4 - 需要moment.js -->
  <script src="https://unpkg.com/tempusdominus-bootstrap-4@5.39.0/build/js/tempusdominus-bootstrap-4.min.js"></script>

  <!-- ChartJS -->
  <script src="https://unpkg.com/chart.js@3.9.1/dist/chart.min.js"></script>

  <!-- Sparkline -->
  <script src="https://unpkg.com/jquery.sparkline@2.4.0/jquery.sparkline.min.js"></script>

  <!-- JQVMap -->
  <script src="https://unpkg.com/jqvmap@1.5.1/dist/jquery.vmap.min.js"></script>
  <script src="https://unpkg.com/jqvmap@1.5.1/dist/maps/jquery.vmap.usa.js"></script>

  <!-- jQuery Knob Chart -->
  <script src="https://unpkg.com/jquery-knob@1.2.13/dist/jquery.knob.min.js"></script>

  <!-- daterangepicker -->
  <script src="https://unpkg.com/daterangepicker@3.1.0/daterangepicker.js"></script>

  <!-- Summernote -->
  <script src="https://unpkg.com/summernote@0.8.20/dist/summernote-bs4.min.js"></script>

  <!-- overlayScrollbars -->
  <script src="https://unpkg.com/overlayscrollbars@1.13.3/js/jquery.overlayScrollbars.min.js"></script>

  <!-- AdminLTE App -->
  <script src="https://unpkg.com/admin-lte@3.2.0/dist/js/adminlte.js"></script>

  <!-- 框架级别的JavaScript代码 -->
  <script>
    // Dark Mode functionality
    document.addEventListener('DOMContentLoaded', function () {
      const darkModeToggle = document.getElementById('darkModeToggle');
      const sidebar = document.querySelector('.main-sidebar');
      const icon = darkModeToggle.querySelector('i');

      // Check for saved dark mode preference
      const isDarkMode = localStorage.getItem('darkMode') === 'true';

      // Apply saved preference
      if (isDarkMode) {
        sidebar.classList.remove('sidebar-dark-primary');
        sidebar.classList.add('sidebar-light-primary');
        icon.classList.remove('fa-moon');
        icon.classList.add('fa-sun');
      }

      darkModeToggle.addEventListener('click', function (e) {
        e.preventDefault();

        // Toggle sidebar classes
        if (sidebar.classList.contains('sidebar-dark-primary')) {
          sidebar.classList.remove('sidebar-dark-primary');
          sidebar.classList.add('sidebar-light-primary');
          icon.classList.remove('fa-moon');
          icon.classList.add('fa-sun');
          localStorage.setItem('darkMode', 'true');
        } else {
          sidebar.classList.remove('sidebar-light-primary');
          sidebar.classList.add('sidebar-dark-primary');
          icon.classList.remove('fa-sun');
          icon.classList.add('fa-moon');
          localStorage.setItem('darkMode', 'false');
        }
      });
    });

    // 检查会话状态的函数
    function checkSession() {
      $.ajax({
        url: '/check_session',
        type: 'GET',
        dataType: 'json',
        success: function (data) {
          if (data.session === false) {
            // 设置消息到sessionStorage
            sessionStorage.setItem('flashMessage', JSON.stringify({
              message: data.message,
              category: data.category
            }));
            window.location.href = "/login";
          }
        },
        error: function (error) {
          console.error('Session check failed:', error);
        }
      });
    }

    $(document).ready(function () {
      // 页面加载完成后执行的代码

      // 恢复菜单折叠状态
      const isSidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
      if (isSidebarCollapsed) {
        $('body').addClass('sidebar-collapse');
      }

      // 监听菜单折叠按钮点击事件
      $('[data-widget="pushmenu"]').on('click', function () {
        const isCollapsed = $('body').hasClass('sidebar-collapse');
        localStorage.setItem('sidebarCollapsed', !isCollapsed);
      });

      // 设置链接处理 - 如果当前页面是新闻检索页面，触发配置事件
      $('#settingsLink').click(function (e) {
        e.preventDefault();
        // 检查当前页面是否有generateConfig按钮
        if (typeof window.triggerGenerateConfig === 'function') {
          window.triggerGenerateConfig();
        } else {
          alert('请先进入新闻检索页面再进行配置');
        }
      });

      // 初始化更新说明模态窗口
      $("#updateInfoModal").dialog({
        autoOpen: false,
        height: 700,
        width: 500,
        modal: true,
        buttons: {
          "关闭": function () {
            $(this).dialog("close");
          }
        }
      });

      // 绑定点击事件到查看更新日志的链接
      $("#viewChangelog").click(function () {
        $("#updateInfoModal").dialog("open");
      });
    });

    document.addEventListener('DOMContentLoaded', function () {
      // 等待 DOM 完全加载后再绑定事件监听器
      document.querySelectorAll('a[data-logout-link]').forEach(function (link) {
        link.addEventListener('click', function (event) {
          event.preventDefault(); // 防止默认的链接行为
          var logoutLinkId = this.getAttribute('data-logout-link'); // 获取数据属性值
          window.location.href = '/logout?linkId=' + logoutLinkId; // 导航到 '/logout' 路由，并传递数据属性值
        });
      });
    });

    //读取更新日志操作-start
    function loadUpdateLog() {
      fetch('/static/update_log.txt') // 使用文件的正确路径
        .then(response => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.text();
        })
        .then(text => {
          // 将换行符转换为 <br> 标签
          const formattedText = text.replace(/\r\n|\r|\n/g, '<br>');
          document.querySelector('.update-modal-content').innerHTML = formattedText;
          // 确保 .update-modal-content 的样式为 'white-space: pre-wrap' 以保持格式，包括空格和换行
          document.querySelector('.update-modal-content').style.whiteSpace = 'pre-wrap';
        })
        .catch(error => {
          console.error('Failed to fetch update log:', error);
        });
    }

    document.getElementById('viewChangelog').onclick = function () {
      loadUpdateLog();
    };
    //读取更新日志操作-end
  </script>

  <!-- 更新说明模态窗口HTML -->
  <div id="updateInfoModal" title="更新日志" style="display:none;">
    <div class="update-modal-content" style="max-height: 700px; ">
    </div>
  </div>
</body>

</html>