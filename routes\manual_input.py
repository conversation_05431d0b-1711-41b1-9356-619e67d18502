from flask import jsonify, request
from io import StringIO
import pandas as pd
from routes.graph_generator import generate_graph

def handler():
    data = request.get_json()
    manual_data = data.get('data', '')

    if not manual_data.strip():
        return jsonify({'status': 'error', 'message': '没有输入数据'})

    try:
        # 将数据转换为 DataFrame
        csv_data = StringIO(manual_data)
        df = pd.read_csv(csv_data, sep=r'[\t,]', engine='python')

        # 检查必要的列是否存在
        required_columns = ['日期', '国家', '传染病名', '新增病例', '死亡人数', '治愈人数']
        if not all(col in df.columns for col in required_columns):
            return jsonify({'status': 'error', 'message': '数据格式不正确，缺少必要的列'})

        # 重命名列为统一格式
        df = df[required_columns]
        df.columns = ['time', 'cities', 'disaster', 'growth', 'deaths', 'cures']

        # 转换日期格式
        df['time'] = pd.to_datetime(df['time'], format='%Y/%m/%d')

        # 排序
        df = df.sort_values('time')

        # 将日期格式化为字符串，格式为 'YYYY/MM/DD'
        df['time'] = df['time'].dt.strftime('%Y/%m/%d')

        # 将 DataFrame 转换为字典列表
        data_set = df.to_dict(orient='records')

        # 生成图表
        graph_image = generate_graph(data_set)

        return jsonify({'status': 'success', 'graph_image': graph_image, 'data_set': data_set})

    except Exception as e:
        print(e)
        return jsonify({'status': 'error', 'message': '数据处理错误，请检查数据格式'}) 