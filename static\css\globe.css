html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    background-color: #000;
}

#container {
    height: 100%;
    width: 100%;
}

.loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 18px;
    text-align: center;
}

.error-message {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: red;
    font-size: 16px;
    text-align: center;
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 5px;
    max-width: 80%;
}
