import requests
from bs4 import BeautifulSoup
from flask import jsonify

def fetch_cdc_news():
    url = "https://www.chinacdc.cn/jksj/jksj01/"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.encoding = 'utf-8'  # Set encoding to UTF-8
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find all news items
        news_items = soup.select('ul.xw_list li')
        news_data = []
        
        for item in news_items:
            # Extract title and link
            title_elem = item.select_one('a')
            if not title_elem:
                continue
            
            # 获取日期元素    
            date_span = title_elem.select_one('span')
            date = date_span.get_text(strip=True) if date_span else ''
            
            # 获取标题文本（移除日期部分）
            title = title_elem.get_text(strip=True)
            
            if date:
                title = title.replace(date, '').strip()
            
            # Extract URL
            base_url = "https://www.chinacdc.cn/jksj/jksj01"
            relative_url = title_elem.get('href', '')
            if relative_url.startswith('./'):
                relative_url = relative_url[2:]
            full_url = f"{base_url}/{relative_url}"
            
            # Extract summary
            summary = item.select_one('p.zy').get_text(strip=True) if item.select_one('p.zy') else ''
            
            news_data.append({
                'title': title.strip(),  # 确保再次去除任何可能的空格
                'date': date,
                'summary': summary,
                'url': full_url
            })
        
        return jsonify(news_data)
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def handler():
    return fetch_cdc_news() 