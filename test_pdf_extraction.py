#!/usr/bin/env python3
"""
测试PDF提取功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.pdf_extractor import extract_pdf_text, validate_pdf_url

def test_pdf_extraction():
    """测试PDF提取功能"""
    test_url = "https://www.chinacdc.cn/jksj/jksj02/202507/U020250731478621084646.pdf"
    
    print("=" * 60)
    print("测试PDF提取功能")
    print("=" * 60)
    
    # 1. 验证URL
    print(f"1. 验证URL: {test_url}")
    is_valid, error_msg = validate_pdf_url(test_url)
    if not is_valid:
        print(f"   URL验证失败: {error_msg}")
        return
    print("   URL验证通过")
    
    # 2. 提取PDF文本
    print("\n2. 开始提取PDF文本...")
    try:
        result = extract_pdf_text(test_url)
        
        if result.startswith("错误："):
            print(f"   提取失败: {result}")
        else:
            print(f"   提取成功!")
            print(f"   文本长度: {len(result)} 字符")
            print(f"   前300字符预览:")
            print("   " + "-" * 50)
            print("   " + result[:300].replace('\n', '\n   '))
            print("   " + "-" * 50)
            
            # 检查是否包含关键词
            keywords = ["风险评估", "突发公共卫生事件", "传染病", "疾病预防控制中心"]
            found_keywords = [kw for kw in keywords if kw in result]
            if found_keywords:
                print(f"   发现关键词: {', '.join(found_keywords)}")
            else:
                print("   未发现预期关键词，可能提取有问题")
                
    except Exception as e:
        print(f"   提取过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_pdf_extraction()
