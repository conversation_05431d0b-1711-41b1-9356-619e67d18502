from flask import jsonify
import json
import os

def handler():
    try:
        current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(current_directory, 'api_config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8-sig') as f:
                config = json.load(f)
            return jsonify(config), 200
        else:
            return jsonify({}), 200
    except IOError as e:
        return jsonify({'status': 'error', 'message': '配置文件读取失败.'}), 500 