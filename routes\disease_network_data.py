from flask import jsonify, request
from model.models import db, Disease, Disaster, Country
from datetime import datetime, timedelta
import traceback
import math

def handler():
    try:
        # Check if full data is requested
        full_data = request.args.get('full', 'false').lower() == 'true'
        time_range = request.args.get('timeRange', 'month')  # 默认为月
        disease_filter = request.args.get('disease', '')  # 获取疾病过滤参数
        
        # 计算时间范围
        end_date = datetime.now()
        if full_data:
            # If full data is requested, set a large time range (e.g., 5 years)
            start_date = end_date - timedelta(days=5*365)
        elif time_range == 'week':
            start_date = end_date - timedelta(days=7)
        elif time_range == 'month':
            start_date = end_date - timedelta(days=30)
        elif time_range == 'three_months':
            start_date = end_date - timedelta(days=90)
        elif time_range == 'half_year':
            start_date = end_date - timedelta(days=180)
        else:  # year
            start_date = end_date - timedelta(days=365)
        
        # 查询数据
        query = Disaster.query.filter(
            Disaster.time >= start_date,
            Disaster.time <= end_date,
            Disaster.growth.isnot(None)
        )
        
        # 如果指定了疾病，添加过滤条件
        if disease_filter:
            query = query.filter(Disaster.disaster == disease_filter)
            
        outbreak_data = query.all()
        
        # 从数据库获取国家信息
        countries = Country.query.all()
        country_map = {country.data_value: country for country in countries}
        
        # 构建节点和边的数据
        nodes = {}
        edges = {}
        
        # 处理每条记录
        for record in outbreak_data:
            country_info = country_map.get(record.cities)
            if not country_info:
                continue
                
            country_name = country_info.name
            disease = record.disaster
            
            # 添加国家节点
            if country_name not in nodes:
                nodes[country_name] = {
                    'name': country_name,
                    'value': 0,
                    'symbolSize': 10,
                    'diseases': {},  # 修改为字典，记录每个疾病的感染数
                    'x': float(country_info.capital_longitude) * 2,
                    'y': float(country_info.capital_latitude) * 2
                }
            
            # 更新节点信息
            nodes[country_name]['value'] += float(record.growth or 0)
            
            # 记录每个疾病的感染数
            if disease not in nodes[country_name]['diseases']:
                nodes[country_name]['diseases'][disease] = 0
            nodes[country_name]['diseases'][disease] += float(record.growth or 0)
            
            nodes[country_name]['symbolSize'] = min(50, 10 + math.log(nodes[country_name]['value'] + 1, 2))
        
        # 处理边
        for record in outbreak_data:
            if not country_map.get(record.cities):
                continue
                
            country_name = country_map[record.cities].name
            disease = record.disaster
            
            # 查找同一时间段内相同疾病的其他国家，建立连接
            similar_records = [r for r in outbreak_data 
                             if r.disaster == disease 
                             and r.cities != record.cities
                             and abs((r.time - record.time).days) <= 7]
            
            for similar_record in similar_records:
                other_country = country_map.get(similar_record.cities)
                if not other_country:
                    continue
                    
                other_country_name = other_country.name
                edge_key = tuple(sorted([country_name, other_country_name]))
                
                if edge_key not in edges:
                    edges[edge_key] = {
                        'source': edge_key[0],
                        'target': edge_key[1],
                        'value': 0,
                        'diseases': set()
                    }
                
                edges[edge_key]['value'] += 1
                edges[edge_key]['diseases'].add(disease)
        
        # 转换数据格式
        nodes_list = []
        for node_data in nodes.values():
            # 将疾病字典转换为带数量的列表
            node_data['diseases'] = [
                f"{disease}({int(count)})" 
                for disease, count in node_data['diseases'].items()
            ]
            nodes_list.append(node_data)
        
        edges_list = []
        for edge_data in edges.values():
            edge_data['diseases'] = list(edge_data['diseases'])
            if edge_data['value'] >= 2:
                edges_list.append(edge_data)
        
        response_data = {
            'nodes': nodes_list,
            'edges': edges_list
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in disease_network_data: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500 

# New endpoint to handle date range filtering
def filter_handler():
    try:
        # Get date range from request parameters
        start_date_str = request.args.get('startDate')
        end_date_str = request.args.get('endDate')
        disease_filter = request.args.get('disease', '')
        
        if not start_date_str or not end_date_str:
            return jsonify({'error': 'startDate and endDate are required'}), 400
            
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
            
            # Add one day to end_date to include the end date in the range
            end_date = end_date + timedelta(days=1)
        except ValueError:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
        
        # 查询数据
        query = Disaster.query.filter(
            Disaster.time >= start_date,
            Disaster.time <= end_date,
            Disaster.growth.isnot(None)
        )
        
        # 如果指定了疾病，添加过滤条件
        if disease_filter:
            query = query.filter(Disaster.disaster == disease_filter)
            
        outbreak_data = query.all()
        
        # 从数据库获取国家信息
        countries = Country.query.all()
        country_map = {country.data_value: country for country in countries}
        
        # 构建节点和边的数据
        nodes = {}
        edges = {}
        
        # 处理每条记录
        for record in outbreak_data:
            country_info = country_map.get(record.cities)
            if not country_info:
                continue
                
            country_name = country_info.name
            disease = record.disaster
            
            # 添加国家节点
            if country_name not in nodes:
                nodes[country_name] = {
                    'name': country_name,
                    'value': 0,
                    'symbolSize': 10,
                    'diseases': {},  # 修改为字典，记录每个疾病的感染数
                    'x': float(country_info.capital_longitude) * 2,
                    'y': float(country_info.capital_latitude) * 2
                }
            
            # 更新节点信息
            nodes[country_name]['value'] += float(record.growth or 0)
            
            # 记录每个疾病的感染数
            if disease not in nodes[country_name]['diseases']:
                nodes[country_name]['diseases'][disease] = 0
            nodes[country_name]['diseases'][disease] += float(record.growth or 0)
            
            nodes[country_name]['symbolSize'] = min(50, 10 + math.log(nodes[country_name]['value'] + 1, 2))
        
        # 处理边
        for record in outbreak_data:
            if not country_map.get(record.cities):
                continue
                
            country_name = country_map[record.cities].name
            disease = record.disaster
            
            # 查找同一时间段内相同疾病的其他国家，建立连接
            similar_records = [r for r in outbreak_data 
                             if r.disaster == disease 
                             and r.cities != record.cities
                             and abs((r.time - record.time).days) <= 7]
            
            for similar_record in similar_records:
                other_country = country_map.get(similar_record.cities)
                if not other_country:
                    continue
                    
                other_country_name = other_country.name
                edge_key = tuple(sorted([country_name, other_country_name]))
                
                if edge_key not in edges:
                    edges[edge_key] = {
                        'source': edge_key[0],
                        'target': edge_key[1],
                        'value': 0,
                        'diseases': set()
                    }
                
                edges[edge_key]['value'] += 1
                edges[edge_key]['diseases'].add(disease)
        
        # 转换数据格式
        nodes_list = []
        for node_data in nodes.values():
            # 将疾病字典转换为带数量的列表
            node_data['diseases'] = [
                f"{disease}({int(count)})" 
                for disease, count in node_data['diseases'].items()
            ]
            nodes_list.append(node_data)
        
        edges_list = []
        for edge_data in edges.values():
            edge_data['diseases'] = list(edge_data['diseases'])
            if edge_data['value'] >= 2:
                edges_list.append(edge_data)
        
        response_data = {
            'nodes': nodes_list,
            'edges': edges_list
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in disease_network_filter: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500 