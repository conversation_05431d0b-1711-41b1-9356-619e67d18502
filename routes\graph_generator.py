import pandas as pd
import matplotlib.pyplot as plt
from matplotlib import font_manager as fm
import numpy as np
import io
import base64

def generate_graph(data_records):
    # 将返回的字典列表转换为 DataFrame
    data_frame = pd.DataFrame(data_records)

    # 确保时间列是 datetime 类型
    data_frame['time'] = pd.to_datetime(data_frame['time'])

    # 按照时间排序
    data_frame = data_frame.sort_values('time')

    # 计算每日新增感染、治愈和死亡人数，确保不出现负值
    data_frame['新增感染人数'] = data_frame['growth'].diff().fillna(0).clip(lower=0)
    data_frame['新增治愈人数'] = data_frame['cures'].diff().fillna(0).clip(lower=0)
    data_frame['新增死亡人数'] = data_frame['deaths'].diff().fillna(0).clip(lower=0)

    # 计算累计感染、治愈和死亡人数
    data_frame['累计感染人数'] = data_frame['growth'].cumsum()
    data_frame['累计治愈人数'] = data_frame['cures'].cumsum()
    data_frame['累计死亡人数'] = data_frame['deaths'].cumsum()

    # 设置字体为微软雅黑
    my_font = fm.FontProperties(fname='C:/Windows/Fonts/msyh.ttc')

    # 绘制趋势图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 12))

    # 每日新增趋势
    ax1.plot(data_frame['time'], data_frame['新增感染人数'], label='新增感染人数', color='blue')
    ax1.plot(data_frame['time'], data_frame['新增治愈人数'], label='新增治愈人数', color='green')
    ax1.plot(data_frame['time'], data_frame['新增死亡人数'], label='新增死亡人数', color='red')
    ax1.set_xlabel('日期', fontproperties=my_font)
    ax1.set_ylabel('人数', fontproperties=my_font)
    ax1.set_title('每日新增人数趋势', fontproperties=my_font)
    ax1.legend(prop=my_font)

    # 累计趋势
    ax2.plot(data_frame['time'], data_frame['累计感染人数'], label='累计感染人数', color='blue')
    ax2.plot(data_frame['time'], data_frame['累计治愈人数'], label='累计治愈人数', color='green')
    ax2.plot(data_frame['time'], data_frame['累计死亡人数'], label='累计死亡人数', color='red')
    ax2.set_xlabel('日期', fontproperties=my_font)
    ax2.set_ylabel('人数', fontproperties=my_font)
    ax2.set_title('累计人数趋势', fontproperties=my_font)
    ax2.legend(prop=my_font)

    plt.tight_layout()

    # 将图表保存到字节流
    buf = io.BytesIO()
    plt.savefig(buf, format='png')
    plt.close(fig)
    buf.seek(0)
    
    # 将字节流转换为 Base64 编码
    image_base64 = base64.b64encode(buf.read()).decode('utf-8')
    
    return f"data:image/png;base64,{image_base64}"