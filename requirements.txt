# Python Version: 3.10.6
# This project is developed and tested with Python 3.10.6
# It's recommended to use the same version or Python 3.10.x for compatibility

dashscope==1.14.1
#dateparser==1.1.1 #googleSearch模块使用到
deep_translator==1.11.4
duckduckgo_search==5.3.1b1
Flask==3.1.0
Flask_Login==0.6.3
flask_sqlalchemy==3.1.1
geoip2==4.8.0
gnews==0.3.6
GoogleNews==1.6.15
langdetect==1.0.9
lxml==5.1.0
matplotlib==3.9.3
newspaper3k==0.2.8
numpy==1.26.0
openai==1.57.0
pandas==1.5.3
PyJWT==2.10.1
PyMySQL==1.1.0
pytz==2024.1
requests>=2.26.0,<3.0.0
scipy==1.14.1
selenium==4.27.1
SQLAlchemy==2.0.23
Werkzeug==3.1.3
webdriver_manager==4.0.1
beautifulsoup4>=4.9.3,<5.0.0  # 修改为兼容版本范围
statsmodels>=0.13.5
openpyxl>=3.0.10  # For .xlsx files
xlrd>=2.0.1  # For .xls files