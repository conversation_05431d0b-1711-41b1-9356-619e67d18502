from gnews import GNews
from flask import jsonify
from datetime import datetime
import time
import dateparser
from GoogleNews import GoogleNews

def search_google_news(keyword, start_date, end_date):
    
     #实例化GoogleNews对象
    googlenews = GoogleNews(lang='zh', region='CN')
    # 设置日期范围
    googlenews.set_time_range(start_date, end_date)

    # 开始搜索
    googlenews.search(keyword)
    all_results = []
    pages = 4  # 定义要抓取的页数

    for page in range(1, pages + 1):
        print(f"Getting page {page}...")
        time.sleep(1)  # 简短延时，防止过快请求被认为是自动化流量
        if page > 1:  # 第一页已经在上面的search()中完成了
            googlenews.get_page(page)
        results = googlenews.results(sort=True)  # 排序结果
        all_results.extend(results)

    # 检查是否有有效结果，并且对结果进行格式化
    if all_results:
        formatted_data = {
            'data': [{
                'checkbox': '',  
                'title': news.get('title', ''),
                'publishedAt': news.get('date', 'Unknown Date'),
                'description': news.get('desc', ''),
                'source': news.get('media'),
                'url': news.get('link',  'Unknown Date')
            } for news in all_results]  
        }
    else:
        formatted_data = {'data': []}

    return jsonify(formatted_data)
