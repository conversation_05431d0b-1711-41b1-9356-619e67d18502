from flask import jsonify, request
import json
import os

def save_config_handler():
    requestData = request.json
    if all(key in requestData for key in ['api_endpoint', 'api_key', 'ai_model', 'prompt_text']):
        configToSave = {
            'api_endpoint': requestData['api_endpoint'],
            'api_key': requestData['api_key'],
            'ai_model': requestData['ai_model'],
            'prompt_text': requestData['prompt_text'],
        }
        try:
            current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(current_directory, 'api_config.json')
            
            with open(config_path, 'w', encoding='utf-8-sig') as f:
                json.dump(configToSave, f, ensure_ascii=False, indent=4)
            return jsonify({'status': 'success', 'message': '配置文件已成功保存.'}), 200
        except IOError as e:
            return jsonify({'status': 'error', 'message': '配置文件保存失败: ' + str(e)}), 500
    else:
        return jsonify({'status': 'error', 'message': 'Invalid configuration data received.'}), 400

def get_config_handler():
    try:
        current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(current_directory, 'api_config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8-sig') as f:
                config = json.load(f)
            return jsonify(config), 200
        else:
            return jsonify({}), 200
    except IOError as e:
        return jsonify({'status': 'error', 'message': '配置文件读取失败.'}), 500 