<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <title>疾病知识图谱</title>
    <!-- 引入必要的CSS -->
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="stylesheet" href="../static/majestic/css/materialdesignicons.min.css">
    <link rel="stylesheet" href="../static/majestic/css/vendor.bundle.base.css">
    <link rel="stylesheet" href="../static/majestic/css/style.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/knowledge_graph.css') }}">
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
    <style>
        /* 顶部主标题样式 */
        .main-title {
            font-size: 28px;
            font-weight: 600;
            color: #333;
            margin: 5px 0 15px 0;
            padding-left: 15px;
        }
        
        /* 标题下方的水平线 */
        .title-divider {
            height: 2px;
            background-color: #e0e0e0;
            margin: 0 15px 20px 15px;
            border: none;
        }
        
        /* 自定义滑块样式 */
        .ui-slider {
            background: #d3d3d3 !important; /* 未选中的灰色背景 */
            border: none !important;
            height: 8px !important;
            border-radius: 4px !important;
        }
        
        .ui-slider .ui-slider-range {
            background: #7a9eb1 !important; /* 莫兰迪蓝色范围 */
            border-radius: 4px !important;
        }
        
        .ui-slider .ui-slider-handle {
            border: 2px solid #7a9eb1 !important;
            background: #ffffff !important;
            border-radius: 50% !important;
            width: 16px !important;
            height: 16px !important;
            margin-top: -1px !important;
            cursor: pointer !important;
            outline: none !important;
        }
        
        .card {
            border-radius: 15px;
            overflow: hidden;
            height: calc(100vh - 80px);
            margin-bottom: 0;
        }
        .card-header {
            border-top-left-radius: 15px;
            border-top-right-radius: 15px;
            padding: 10px 15px;
        }
        .card-body {
            padding: 0;
            position: relative;
            height: calc(100% - 50px);
        }
        #graphContainer {
            position: absolute;
            height: calc(100% - 30px);
            width: calc(100% - 30px);
            top: 15px;
            left: 15px;
            right: 15px;
            bottom: 15px;
            border-radius: 10px;
            overflow: visible;
            background-color: #fff;
        }
        .detail-section {
            margin-bottom: 15px;
            padding: 10px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .detail-section h6 {
            margin-bottom: 10px;
            color: #333;
        }
        body {
            overflow-y: hidden;
        }
        .container-fluid {
            padding: 15px;
        }
        .row {
            margin: 0;
        }
        .col-md-9, .col-md-3 {
            padding: 0 10px;
        }
        #refreshProgressBar {
            font-weight: 500;
            font-size: 14px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            min-width: 2em;
            transition: width 0.3s ease;
        }
        
        .progress {
            height: 24px;
            background-color: #e9ecef;
            border-radius: 6px;
            margin: 10px 0;
        }

        .progress-bar {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #4e73df;
            color: #fff;
            padding: 0 8px;
        }

        #refreshProgressStatus {
            color: #666;
            font-size: 13px;
            margin-top: 5px;
            font-weight: 500;
        }
        
        .loading-overlay {
            display: flex;
            align-items: center;
            justify-content: center;
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            z-index: 1000;
        }
        
        .error-message {
            display: none;
            color: #dc3545;
            padding: 15px;
            text-align: center;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <h5 class="main-title">疾病知识图谱</h5>
        <hr class="title-divider">
        
        <div class="row">
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title">图谱</h5>
                        <div class="graph-controls">
                            <button class="ui blue labeled icon button" id="refreshGraph">
                                <i class="sync icon"></i>
                                刷新数据
                            </button>
                            <button class="ui teal labeled icon button" id="changeLayout">
                                <i class="object group icon"></i>
                                切换布局
                            </button>
                            <button class="ui grey labeled icon button" id="resetZoom">
                                <i class="expand icon"></i>
                                重置视图
                            </button>
                        </div>
                        <div id="refreshProgressContainer" style="display: none; margin-top: 10px;">
                            <div class="progress">
                                <div id="refreshProgressBar" class="progress-bar progress-bar-striped progress-bar-animated" 
                                    role="progressbar" style="width: 0%;" 
                                    aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                            </div>
                            <small id="refreshProgressStatus" class="form-text text-muted">正在等待任务开始...</small>
                        </div>
                    </div>
                    
                    <!-- 过滤与搜索区域（可折叠） -->
                    <div class="ui segment" style="margin: 0; padding: 10px 18px 5px 18px; border-left: 0; border-right: 0; border-radius: 0;">
                        <div class="filter-header" style="cursor: pointer;" id="filterToggle">
                            <h5 style="display: flex; justify-content: space-between; align-items: center; margin: 0;">
                                过滤选项
                                <i class="angle down icon" id="toggleIcon"></i>
                            </h5>
                        </div>
                        <div class="filter-content" id="filterContent" style="display: none; margin-top: 15px;">
                            <form class="ui form" id="graphFilterForm">
                                <div class="fields">
                                    <!-- 疾病搜索框 -->
                                    <div class="eight wide field">
                                        <label for="diseaseSearch">搜索疾病</label>
                                        <div class="ui icon input" style="width:100%">
                                            <input type="text" id="diseaseSearch" placeholder="输入疾病名称...">
                                            <i class="search icon"></i>
                                        </div>
                                    </div>
                                    <!-- 置信度阈值范围滑块 -->
                                    <div class="eight wide field">
                                        <label>置信度范围 <span id="confidenceMinValue" style="font-weight:600;color:#4183c4;">0.0</span> - <span id="confidenceMaxValue" style="font-weight:600;color:#4183c4;">1.0</span></label>
                                        <div class="ui-slider-container" style="margin-top:15px;width:85%;">
                                            <div id="confidenceRangeSlider"></div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div id="graphContainer">
                            <div id="loading" class="loading-overlay">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <div id="error-message" class="error-message"></div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">详细信息</h5>
                        <button type="button" class="btn btn-sm btn-link text-primary" id="helpButton" data-toggle="modal" data-target="#helpModal" title="了解关系逻辑">
                            <i class="mdi mdi-help-circle-outline" style="font-size: 20px;"></i>
                        </button>
                    </div>
                    <div class="card-body" id="detailPanel">
                        <div class="text-center text-muted initial-message">
                            <p>点击节点或连线查看详细信息</p>
                        </div>
                        <div class="detail-content" style="display: none;">
                            <div class="detail-section">
                                <h6>关系类型</h6>
                                <p id="relationshipType"></p>
                            </div>
                            <div class="detail-section">
                                <h6>相关疾病</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <p><strong>疾病 1:</strong></p>
                                        <p id="disease1"></p>
                                    </div>
                                    <div class="col-6">
                                        <p><strong>疾病 2:</strong></p>
                                        <p id="disease2"></p>
                                    </div>
                                </div>
                            </div>
                            <div class="detail-section">
                                <h6>详细描述</h6>
                                <p id="detailDescription"></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 帮助模态框 -->
    <div class="modal fade" id="helpModal" tabindex="-1" role="dialog" aria-labelledby="helpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="helpModalLabel">知识图谱关系逻辑说明</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <h6>关系计算方法</h6>
                    <p>本知识图谱中的疾病关系是通过分析疾病时间序列数据的相关性计算得出的：</p>
                    <ul>
                        <li><strong>数据来源</strong>：全球疾病数据库中的历史数据</li>
                        <li><strong>计算方法</strong>：皮尔逊相关系数（Pearson correlation）</li>
                        <li><strong>数据处理</strong>：按周或按月聚合处理，减少数据噪音</li>
                        <li><strong>统计显著性</strong>：使用P值（p < 0.05）确保相关性在统计上显著</li>
                    </ul>
                    
                    <h6>关系类型说明</h6>
                    <p>关系类型由两部分组成：强度和方向</p>
                    <ul>
                        <li><strong>强相关</strong>：|相关系数| > 0.7</li>
                        <li><strong>中相关</strong>：0.5 < |相关系数| ≤ 0.7</li>
                        <li><strong>弱相关</strong>：0.3 < |相关系数| ≤ 0.5</li>
                        <li><strong>正相关</strong>：两种疾病的发病率变化趋势一致</li>
                        <li><strong>负相关</strong>：两种疾病的发病率变化趋势相反</li>
                    </ul>
                    
                    <h6>时间滞后效应</h6>
                    <p>系统还会检测疾病之间可能存在的时间滞后效应，即一种疾病的变化在时间上领先于另一种疾病的变化。当存在显著的时间滞后相关性时，会在证据描述中特别标注。</p>
                    
                    <div class="alert alert-info">
                        <strong>注意</strong>：相关性不等同于因果关系。两种疾病之间的相关性可能是由共同的第三方因素导致的，或者是巧合。
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的JS -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <script src="../static/majestic/js/vendor.bundle.base.js"></script>
    <script src="../static/majestic/js/hoverable-collapse.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/knowledge_graph.js') }}"></script>
    
    <script>
        // 确保模态框正常工作并设置过滤器折叠功能
        $(document).ready(function() {
            $('#helpButton').tooltip();
            
            // 过滤器折叠功能
            $('#filterToggle').on('click', function() {
                $('#filterContent').slideToggle(300);
                $('#toggleIcon').toggleClass('down up');
            });
        });
    </script>
</body>
</html>
