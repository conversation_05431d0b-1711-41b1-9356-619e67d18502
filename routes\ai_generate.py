from flask import jsonify, request, url_for
import routes.call_chatgpt as call_chatgpt

def handler():
    # 获取请求数据
    data = request.json
    selected_items = data.get('selectedItems')

    if not selected_items:
        return jsonify({'status': 'error', 'message': '没有接收到数据'}), 400

    result = call_chatgpt.process_data(selected_items)
    # 返回成功的响应
    if result['status'] == "Done":
        file_url = url_for('static', filename=result['file_name'], _external=True)
        return jsonify({'status': 'success', 'message': 'AI生成数据已成功完成!', 'fileUrl': file_url})
    
    # 如果处理失败，返回错误信息
    return jsonify({'status': 'error', 'message': result['status']}), 500 