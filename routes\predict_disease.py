from flask import jsonify, request
from model.models import Country
from routes.disease_prediction import DiseasePrediction
import traceback

def handler():
    try:
        country = request.form.get('country')
        disease = request.form.get('disease')
        
        # 检查是否提供了必要参数
        if not country or not disease:
            return jsonify({
                "error": "请选择具体的国家和传染病",
                "status": "invalid_params"
            }), 400
        
        # 从数据库获取国家人口数据
        if country == 'all':
            country_data = None
            population = 1000000
        else:
            country_data = Country.query.filter_by(data_value=country).first()
            population = country_data.population if country_data else 1000000
            
        # Initialize disease prediction model
        predictor = DiseasePrediction()
        prediction_results, status, historical_data = predictor.get_prediction_for_location(country, disease)
        
        if status != "success" or not prediction_results:
            error_messages = {
                "insufficient_data": "历史数据不足，预测需要至少10条历史数据记录。为获得更准确的预测，建议使用14条以上的数据。",
                "prediction_failed": "预测过程中发生错误，请稍后重试。",
            }
            return jsonify({
                "error": error_messages.get(status, "预测失败"),
                "status": status
            }), 400
            
        # Get trend components
        trend, seasonality = predictor.get_trend_components(prediction_results)
        
        # 确保第一天的预测值不为0
        predicted_growth = prediction_results['predicted_growth']
        if predicted_growth and predicted_growth[0] == 0 and len(predicted_growth) > 1:
            # 如果第一天预测值为0，使用第二天的值或平均值
            if len(predicted_growth) > 1:
                predicted_growth[0] = predicted_growth[1] * 0.8  # 使用第二天的80%作为第一天的值
            else:
                predicted_growth[0] = 1  # 至少设置一个非零值
                
        # 将修改后的预测值更新到prediction_results
        prediction_results['predicted_growth'] = predicted_growth
        
        # 确保由疾病预测模型生成的预测数据在所有图表中一致
        growth_rates = []
        if seasonality and 'daily_changes' in seasonality:
            growth_rates = seasonality['daily_changes']
            # 确保第一天的增长率不为0
            if len(growth_rates) > 0 and growth_rates[0] == 0 and len(growth_rates) > 1:
                growth_rates[0] = growth_rates[1] * 0.8
        else:
            growth_rates = prediction_results['predicted_growth']
            
        # 确保 phases 和 growth_rates 长度一致
        phases = []
        if seasonality and 'phases' in seasonality:
            phases = seasonality['phases']
            # 如果长度不一致，调整phases长度与growth_rates一致
            if len(phases) != len(growth_rates):
                if len(phases) < len(growth_rates):
                    # 如果phases过短，根据增长率补充
                    for i in range(len(phases), len(growth_rates)):
                        if growth_rates[i] > 0:
                            phases.append('增长期')
                        elif growth_rates[i] < 0:
                            phases.append('下降期')
                        else:
                            phases.append('平稳期')
                else:
                    # 如果phases过长，截取
                    phases = phases[:len(growth_rates)]
        else:
            # 如果没有phases数据，根据增长率生成
            for rate in growth_rates:
                if rate > 0:
                    phases.append('增长期')
                elif rate < 0:
                    phases.append('下降期')
                else:
                    phases.append('平稳期')
                    
        response_data = {
            "dates": prediction_results['dates'],
            "predictions": prediction_results['predicted_growth'],
            "seir": {
                "susceptible": prediction_results['susceptible'],
                "exposed": prediction_results['exposed'],
                "infected": prediction_results['infected'],
                "recovered": prediction_results['recovered']
            },
            "cumulative": [sum(prediction_results['predicted_growth'][:i+1]) for i in range(len(prediction_results['predicted_growth']))],
            "phases": phases,
            "growth_rates": growth_rates,
            "historical_data": historical_data
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in predict_disease: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e), "status": "server_error"}), 500 