import requests
import concurrent.futures

def fetch_content(url):
    reader_url = f'https://r.jina.ai/{url}'
    response = requests.get(reader_url)
    if response.status_code == 200:
        return response.text
    else:
        return f"Failed to fetch content for {url}: {response.status_code}"

def fetch_news(urls):
    news_contents = []
    with concurrent.futures.ThreadPoolExecutor() as executor:
        futures = [executor.submit(fetch_content, url) for url in urls]
        for future in concurrent.futures.as_completed(futures):
            news_contents.append(future.result())
    return news_contents
