"""
CDC风险评估报告AI分析器
用于分析中国疾控中心发布的突发公共卫生事件风险评估报告
"""

# AI分析提示词模板
CDC_ANALYSIS_PROMPT = """
你是一位专业的公共卫生专家，请对以下中国疾控中心发布的突发公共卫生事件风险评估报告进行深度分析。

请严格按照以下JSON格式输出分析结果，不要添加任何其他文字：

{
  "report_info": {
    "title": "报告标题",
    "period": "评估时期（如：2025年7月）",
    "summary": "报告摘要（100-150字）"
  },
  "risk_assessment": {
    "overall_trend": "整体风险趋势描述",
    "key_concerns": [
      {
        "disease": "疾病/事件名称",
        "risk_level": "风险等级（重点关注/一般关注/特别关注）",
        "description": "风险描述",
        "affected_regions": "影响地区",
        "recommendations": "防控建议"
      }
    ]
  },
  "infectious_diseases": {
    "high_risk": [
      {
        "name": "疾病名称",
        "current_status": "当前疫情状况",
        "risk_factors": "风险因素",
        "prevention_measures": "预防措施"
      }
    ],
    "moderate_risk": [
      {
        "name": "疾病名称",
        "current_status": "当前疫情状况",
        "risk_factors": "风险因素",
        "prevention_measures": "预防措施"
      }
    ]
  },
  "public_health_events": {
    "food_safety": {
      "risk_description": "食品安全风险描述",
      "main_hazards": "主要危险因素",
      "prevention_advice": "预防建议"
    },
    "environmental_health": {
      "climate_risks": "气候相关健康风险",
      "natural_disasters": "自然灾害风险",
      "prevention_measures": "预防措施"
    }
  },
  "regional_analysis": {
    "high_risk_regions": [
      {
        "region": "地区名称",
        "risk_factors": "风险因素",
        "specific_diseases": "特定疾病风险"
      }
    ],
    "general_recommendations": "总体建议"
  },
  "seasonal_factors": {
    "current_season_risks": "当前季节特有风险",
    "weather_impact": "天气因素影响",
    "seasonal_prevention": "季节性预防措施"
  },
  "monitoring_priorities": [
    {
      "priority": "监测重点",
      "target_population": "目标人群",
      "monitoring_indicators": "监测指标"
    }
  ],
  "key_recommendations": [
    "关键建议1",
    "关键建议2",
    "关键建议3"
  ]
}

请基于报告内容填写以上所有字段，确保信息准确、完整。如果某个字段在报告中没有明确信息，请填写"报告中未明确提及"。

以下是需要分析的报告内容：

"""

def get_analysis_prompt():
    """
    获取CDC报告分析的提示词
    
    Returns:
        str: 完整的提示词
    """
    return CDC_ANALYSIS_PROMPT

def format_analysis_result(ai_response):
    """
    格式化AI分析结果为前端展示格式
    
    Args:
        ai_response (str): AI返回的JSON格式分析结果
        
    Returns:
        dict: 格式化后的结果
    """
    try:
        import json
        
        # 尝试解析JSON
        analysis_data = json.loads(ai_response)
        
        # 构建前端展示格式
        formatted_result = {
            "success": True,
            "data": {
                "report_info": analysis_data.get("report_info", {}),
                "risk_summary": {
                    "overall_trend": analysis_data.get("risk_assessment", {}).get("overall_trend", ""),
                    "key_concerns_count": len(analysis_data.get("risk_assessment", {}).get("key_concerns", [])),
                    "high_risk_diseases_count": len(analysis_data.get("infectious_diseases", {}).get("high_risk", [])),
                    "moderate_risk_diseases_count": len(analysis_data.get("infectious_diseases", {}).get("moderate_risk", []))
                },
                "detailed_analysis": analysis_data
            }
        }
        
        return formatted_result
        
    except json.JSONDecodeError as e:
        return {
            "success": False,
            "error": f"AI返回结果格式错误: {str(e)}",
            "raw_response": ai_response
        }
    except Exception as e:
        return {
            "success": False,
            "error": f"处理分析结果时发生错误: {str(e)}",
            "raw_response": ai_response
        }

def generate_html_report(analysis_data):
    """
    生成HTML格式的分析报告
    
    Args:
        analysis_data (dict): 分析数据
        
    Returns:
        str: HTML格式的报告
    """
    if not analysis_data.get("success"):
        return f"""
        <div class="ui negative message">
            <div class="header">分析失败</div>
            <p>{analysis_data.get('error', '未知错误')}</p>
        </div>
        """
    
    data = analysis_data["data"]["detailed_analysis"]
    report_info = data.get("report_info", {})
    risk_assessment = data.get("risk_assessment", {})
    infectious_diseases = data.get("infectious_diseases", {})
    public_health_events = data.get("public_health_events", {})
    
    html = f"""
    <div class="ui container" style="padding: 20px;">
        <!-- 报告基本信息 -->
        <div class="ui blue segment">
            <h2 class="ui header">
                <i class="file alternate outline icon"></i>
                <div class="content">
                    {report_info.get('title', '风险评估报告')}
                    <div class="sub header">{report_info.get('period', '')}</div>
                </div>
            </h2>
            <div class="ui info message">
                <div class="header">报告摘要</div>
                <p>{report_info.get('summary', '')}</p>
            </div>
        </div>

        <!-- 整体风险趋势 -->
        <div class="ui segment">
            <h3 class="ui header">
                <i class="chart line icon"></i>
                整体风险趋势
            </h3>
            <p>{risk_assessment.get('overall_trend', '')}</p>
        </div>

        <!-- 重点关注事件 -->
        <div class="ui segment">
            <h3 class="ui header">
                <i class="warning sign icon"></i>
                重点关注事件
            </h3>
            <div class="ui cards">
    """
    
    # 添加重点关注事件卡片
    for concern in risk_assessment.get('key_concerns', []):
        risk_color = {
            '重点关注': 'red',
            '一般关注': 'orange', 
            '特别关注': 'purple'
        }.get(concern.get('risk_level', ''), 'grey')
        
        html += f"""
                <div class="ui {risk_color} card">
                    <div class="content">
                        <div class="header">{concern.get('disease', '')}</div>
                        <div class="meta">
                            <span class="ui {risk_color} label">{concern.get('risk_level', '')}</span>
                        </div>
                        <div class="description">
                            <p><strong>风险描述：</strong>{concern.get('description', '')}</p>
                            <p><strong>影响地区：</strong>{concern.get('affected_regions', '')}</p>
                            <p><strong>防控建议：</strong>{concern.get('recommendations', '')}</p>
                        </div>
                    </div>
                </div>
        """
    
    html += """
            </div>
        </div>

        <!-- 传染病风险分析 -->
        <div class="ui segment">
            <h3 class="ui header">
                <i class="virus icon"></i>
                传染病风险分析
            </h3>
            
            <div class="ui two column grid">
                <div class="column">
                    <h4 class="ui red header">高风险传染病</h4>
                    <div class="ui relaxed list">
    """
    
    # 添加高风险传染病
    for disease in infectious_diseases.get('high_risk', []):
        html += f"""
                        <div class="item">
                            <i class="virus icon"></i>
                            <div class="content">
                                <div class="header">{disease.get('name', '')}</div>
                                <div class="description">
                                    <p><strong>当前状况：</strong>{disease.get('current_status', '')}</p>
                                    <p><strong>风险因素：</strong>{disease.get('risk_factors', '')}</p>
                                    <p><strong>预防措施：</strong>{disease.get('prevention_measures', '')}</p>
                                </div>
                            </div>
                        </div>
        """
    
    html += """
                    </div>
                </div>
                
                <div class="column">
                    <h4 class="ui orange header">中等风险传染病</h4>
                    <div class="ui relaxed list">
    """
    
    # 添加中等风险传染病
    for disease in infectious_diseases.get('moderate_risk', []):
        html += f"""
                        <div class="item">
                            <i class="bug icon"></i>
                            <div class="content">
                                <div class="header">{disease.get('name', '')}</div>
                                <div class="description">
                                    <p><strong>当前状况：</strong>{disease.get('current_status', '')}</p>
                                    <p><strong>风险因素：</strong>{disease.get('risk_factors', '')}</p>
                                    <p><strong>预防措施：</strong>{disease.get('prevention_measures', '')}</p>
                                </div>
                            </div>
                        </div>
        """
    
    html += """
                    </div>
                </div>
            </div>
        </div>

        <!-- 关键建议 -->
        <div class="ui green segment">
            <h3 class="ui header">
                <i class="lightbulb outline icon"></i>
                关键建议
            </h3>
            <div class="ui bulleted list">
    """
    
    # 添加关键建议
    for recommendation in data.get('key_recommendations', []):
        html += f"""
                <div class="item">{recommendation}</div>
        """
    
    html += """
            </div>
        </div>
    </div>
    """
    
    return html
