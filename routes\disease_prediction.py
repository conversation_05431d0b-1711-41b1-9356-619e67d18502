import numpy as np
from scipy.integrate import odeint
from datetime import datetime, timedelta
import pandas as pd
import pymysql
from db.db_config import USERNAME, PASSWORD, HOST, PORT, DATABASE
import json
import traceback
from model.models import Country, db

class SEIRModel:
    def __init__(self, beta, sigma, gamma, N, dynamic_beta=False):
        """
        初始化SEIR模型
        beta: 传染率
        sigma: 潜伏期转化率 (1/潜伏期天数)
        gamma: 恢复率 (1/感染持续天数)
        N: 总人口
        dynamic_beta: 是否使用动态传染率
        """
        self.beta_max = beta  # 最大传染率
        self.beta = beta      # 初始传染率
        self.sigma = sigma
        self.gamma = gamma
        self.N = N
        self.dynamic_beta = dynamic_beta
        self.trend = 'unknown'  # 趋势：'rising', 'falling', 'unknown'
        self.recent_growth_rate = None  # 最近的增长率
        self.recent_trend_slope = None  # 最近趋势的斜率

    def set_trend(self, trend):
        """设置疫情趋势"""
        self.trend = trend
        
    def set_recent_data(self, growth_rate, trend_slope):
        """设置最近的数据趋势参数"""
        self.recent_growth_rate = growth_rate
        self.recent_trend_slope = trend_slope

    def dynamic_beta_function(self, t, max_t):
        """动态传染率函数"""
        if not self.dynamic_beta:
            return self.beta
        
        # 基于最近趋势的更精确动态传染率调整
        if self.recent_trend_slope is not None:
            # 如果有最近趋势数据，使用它来调整传染率
            decay_factor = np.exp(-0.1 * t)  # 趋势影响随时间衰减
            trend_influence = 1.0 + self.recent_trend_slope * decay_factor
            
            # 限制调整范围，避免过度调整
            trend_influence = max(0.5, min(1.5, trend_influence))
            return self.beta * trend_influence
            
        # 如果没有最近趋势数据，使用原来的方法
        if self.trend == 'rising':
            # 如果是上升趋势，先增后减
            peak_time = max_t / 3  # 假设在三分之一的时间点达到峰值
            if t < peak_time:
                # 上升期，传染率增加
                return self.beta * (1 + 0.3 * (t / peak_time))
            else:
                # 下降期，传染率降低
                return self.beta * (1 + 0.3 * (1 - (t - peak_time) / (max_t - peak_time)))
        elif self.trend == 'falling':
            # 如果是下降趋势，传染率逐渐降低
            return self.beta * (1 - 0.3 * (t / max_t))
        else:
            # 未知趋势，使用固定传染率
            return self.beta

    def model(self, y, t_scalar):
        """模型的微分方程组"""
        S, E, I, R = y
        
        # 使用动态传染率
        current_beta = self.dynamic_beta_function(t_scalar, 30)  # 假设最大时间为30天
        
        # 根据当前感染人数调整传染率，模拟社交距离和防控措施
        if I / self.N > 0.05:  # 当感染人数超过总人口5%时
            current_beta *= 0.85  # 模拟加强防控措施的效果
        
        dSdt = -current_beta * S * I / self.N
        dEdt = current_beta * S * I / self.N - self.sigma * E
        dIdt = self.sigma * E - self.gamma * I
        dRdt = self.gamma * I
        return dSdt, dEdt, dIdt, dRdt

    def predict(self, S0, E0, I0, R0, t):
        """预测未来趋势"""
        y0 = S0, E0, I0, R0
        ret = odeint(self.model, y0, t)
        return ret.T

class DiseasePrediction:
    def __init__(self):
        # 加载疾病参数配置
        self.disease_params = {
            'covid-19': {'incubation': 5, 'duration': 14, 'beta': 0.3},
            'influenza': {'incubation': 2, 'duration': 7, 'beta': 0.5},
            'default': {'incubation': 4, 'duration': 10, 'beta': 0.4}
        }
        
        # 从数据库获取国家人口数据
        self.population_data = self._load_population_data()

    def _load_population_data(self):
        """从数据库加载人口数据"""
        population_data = {'default': 1000000}  # 设置默认值
        try:
            # 查询所有国家的人口数据
            countries = Country.query.all()
            for country in countries:
                if country.population:
                    population_data[country.data_value] = country.population
        except Exception as e:
            print(f"Error loading population data from database: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
        return population_data

    def get_data_from_db(self):
        """从数据库获取数据"""
        try:
            connection = pymysql.connect(
                host=HOST,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE,
                port=int(PORT)
            )
            
            query = """
                SELECT time, cities, disaster, growth, deaths, cures 
                FROM t_disaster 
                WHERE growth IS NOT NULL
                ORDER BY time
            """
            
            # 使用cursor直接获取数据
            with connection.cursor() as cursor:
                cursor.execute(query)
                columns = [col[0] for col in cursor.description]
                data = cursor.fetchall()
                
            # 创建DataFrame
            df = pd.DataFrame(list(data), columns=columns)
            
            # 确保time列是datetime类型
            df['time'] = pd.to_datetime(df['time'])
            
            connection.close()
            return df
            
        except Exception as e:
            print(f"Error in get_data_from_db: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def prepare_data(self, df, city, disease):
        """准备预测所需的数据"""
        try:
            # 处理全部国家或全部传染病的情况
            if city == 'all' and disease != 'all':
                # 全部国家，特定疾病
                city_data = df[df['disaster'] == disease].copy()
            elif city != 'all' and disease == 'all':
                # 特定国家，全部疾病
                city_data = df[df['cities'] == city].copy()
            else:
                # 特定国家和特定疾病
                city_data = df[(df['cities'] == city) & (df['disaster'] == disease)].copy()
            
            if len(city_data) < 10:
                print(f"Insufficient data for {city} and {disease}")
                return None
            
            # 计算累计数据
            city_data = city_data.sort_values('time')
            city_data['total_cases'] = city_data['growth'].cumsum()
            city_data['total_deaths'] = city_data['deaths'].cumsum()
            city_data['total_cures'] = city_data['cures'].cumsum()
            
            return city_data
            
        except Exception as e:
            print(f"Error in prepare_data: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def get_model_parameters(self, disease, city_data):
        """获取模型参数"""
        disease_key = disease.lower()
        params = self.disease_params.get(disease_key, self.disease_params['default'])
        
        # 判断疫情趋势
        trend = self._determine_trend(city_data)
        params['trend'] = trend
        
        # 从数据估计传染率beta（如果有足够数据）
        if len(city_data) > 14:
            # 处理异常值，使用中位数而非平均值
            growth_values = city_data['growth'].values
            # 移除超过Q3+1.5*IQR的异常值
            q1 = np.percentile(growth_values, 25)
            q3 = np.percentile(growth_values, 75)
            iqr = q3 - q1
            upper_bound = q3 + 1.5 * iqr
            filtered_growth = growth_values[growth_values <= upper_bound]
            
            if len(filtered_growth) > 0:
                # 使用中位数而非平均值，减少异常值影响
                growth_rate = np.median(filtered_growth)
                if growth_rate > 0:
                    # 根据趋势调整传染率
                    if trend == 'rising':
                        # 如果是上升趋势，增大传染率
                        base_beta = min(max(growth_rate / 150, 0.1), 0.6)
                    else:
                        # 如果是下降趋势，降低传染率
                        base_beta = min(max(growth_rate / 200, 0.05), 0.5)
                    
                    params['beta'] = base_beta
        
        return params
        
    def _determine_trend(self, city_data):
        """判断疫情趋势"""
        if len(city_data) < 7:
            return 'unknown'
            
        # 获取最近7天的数据
        recent_data = city_data.iloc[-7:]
        
        # 计算增长率的变化
        growth_changes = np.diff(recent_data['growth'].values)
        
        # 如果最近的增长率变化主要是正的，说明是上升趋势
        positive_changes = sum(growth_changes > 0)
        negative_changes = sum(growth_changes < 0)
        
        if positive_changes > negative_changes:
            return 'rising'
        else:
            return 'falling'
        
        return params

    def get_prediction_for_location(self, city, disease, periods=30):
        """获取特定地点的疾病预测"""
        try:
            # 获取数据
            df = self.get_data_from_db()
            city_data = self.prepare_data(df, city, disease)
            
            if city_data is None:
                return None, "insufficient_data", None
            
            # 获取模型参数
            params = self.get_model_parameters(disease, city_data)
            N = self.population_data.get(city, self.population_data['default'])
            
            # 分析最近的趋势以改进预测
            recent_window = min(14, len(city_data))
            recent_data = city_data.iloc[-recent_window:]
            
            # 计算近期增长率的平均值和趋势
            recent_growth = recent_data['growth'].values
            growth_trend = np.polyfit(range(len(recent_growth)), recent_growth, 1)[0]
            avg_recent_growth = np.mean(recent_growth[-3:])  # 最近3天的平均值
            
            # 计算标准化的趋势斜率，用于模型调整
            normalized_trend = growth_trend / (np.mean(recent_growth) if np.mean(recent_growth) != 0 else 1)
            
            # 根据最近趋势调整传染率
            trend_factor = 1.0
            if growth_trend < 0:
                # 如果是下降趋势，减小传染率
                trend_factor = max(0.7, 1.0 + normalized_trend)  # 限制最小值
                params['beta'] *= trend_factor
            
            # 初始化SEIR模型，启用动态传染率
            model = SEIRModel(
                beta=params['beta'],
                sigma=1/params['incubation'],
                gamma=1/params['duration'],
                N=N,
                dynamic_beta=True
            )
            
            # 设置疫情趋势
            model.set_trend(params.get('trend', 'unknown'))
            
            # 设置最近的数据趋势参数
            model.set_recent_data(avg_recent_growth, normalized_trend)
            
            # 设置初始值
            latest_data = city_data.iloc[-1]
            I0 = max(latest_data['total_cases'] - latest_data['total_cures'] - latest_data['total_deaths'], 0)
            R0 = latest_data['total_cures'] + latest_data['total_deaths']
            E0 = I0 * 0.5  # 估计潜伏人数
            S0 = N - E0 - I0 - R0
            
            # 预测时间点
            t = np.linspace(0, periods, periods)
            
            # 进行预测
            S, E, I, R = model.predict(S0, E0, I0, R0, t)
            
            # 计算每日新增
            daily_new_cases = np.diff(I + R, prepend=I[0] + R[0])
            
            # 根据历史数据的最近趋势调整预测值
            # 确保预测的第一天与历史数据的最后一天平滑过渡
            if len(recent_growth) > 0:
                # 使用最近3天的平均值作为预测的起点
                daily_new_cases[0] = avg_recent_growth
                
                # 应用平滑过渡，确保预测曲线与历史数据曲线连续
                transition_days = min(7, len(daily_new_cases))
                for i in range(1, transition_days):
                    weight = (transition_days - i) / transition_days
                    daily_new_cases[i] = weight * avg_recent_growth + (1 - weight) * daily_new_cases[i]
                    
                # 如果是下降趋势，确保预测值不会突然增加
                if growth_trend < 0:
                    for i in range(1, len(daily_new_cases)):
                        # 限制增长幅度，确保下降趋势的连续性
                        max_increase = 0.05 * daily_new_cases[i-1]  # 允许最多5%的增长
                        if daily_new_cases[i] > daily_new_cases[i-1] + max_increase:
                            daily_new_cases[i] = daily_new_cases[i-1] + max_increase
            
            # 合理性检查：限制预测的感染人数不超过总人口的一个合理比例
            max_reasonable_infected_ratio = 0.2  # 最大合理感染比例，即总人口的20%
            max_infected = np.max(I)
            infected_ratio = max_infected / N
            
            if infected_ratio > max_reasonable_infected_ratio:
                # 如果预测的感染人数超过合理范围，进行调整
                adjustment_factor = max_reasonable_infected_ratio / infected_ratio
                I = I * adjustment_factor
                R = R * adjustment_factor
                daily_new_cases = daily_new_cases * adjustment_factor
                print(f"Warning: Adjusted prediction for {city} and {disease}, original infected ratio: {infected_ratio:.2%}")
            
            # 准备返回结果
            # 获取历史数据的最后一天日期
            last_date = city_data['time'].iloc[-1]
            
            # 使用当前日期作为参考点
            current_date = datetime.now()
            # 将当前日期调整为当天开始（去除时分秒）
            current_date = datetime(current_date.year, current_date.month, current_date.day)
            
            # 判断最后一天历史数据是否是今天
            last_date_day = last_date.replace(tzinfo=None).replace(hour=0, minute=0, second=0, microsecond=0)
            is_last_date_today = (last_date_day == current_date)
            
            # 如果最后一天是今天，则从明天开始预测，否则从今天开始
            prediction_start_date = current_date + timedelta(days=1) if is_last_date_today else current_date
            
            # 计算需要预测的天数
            days_to_predict = periods
            
            # 重新计算预测，使用最新的初始数据
            t = np.linspace(0, days_to_predict, days_to_predict)
            S, E, I, R = model.predict(S0, E0, I0, R0, t)
            daily_new_cases = np.diff(I + R, prepend=I[0] + R[0])
            
            # 生成预测日期列表，从预测起始日期开始
            prediction_dates = [(prediction_start_date + timedelta(days=int(x))).strftime('%Y-%m-%d') for x in range(days_to_predict)]
            
            # 确保第一天的预测值不为0
            if len(daily_new_cases) > 0 and daily_new_cases[0] == 0:
                # 如果第一天的预测值为0，使用后续数据的平均值或固定值
                if len(daily_new_cases) > 1:
                    # 使用第二天的值作为基准
                    daily_new_cases[0] = daily_new_cases[1] * 0.8
                else:
                    # 如果只有一天数据，设置一个非零的默认值
                    daily_new_cases[0] = 1
            
            prediction_results = {
                'dates': prediction_dates,
                'predicted_growth': daily_new_cases.tolist(),
                'lower_bound': (daily_new_cases * 0.8).tolist(),  # 简单的置信区间
                'upper_bound': (daily_new_cases * 1.2).tolist(),
                'susceptible': S.tolist(),
                'exposed': E.tolist(),
                'infected': I.tolist(),
                'recovered': R.tolist(),
                'population': N,  # 添加总人口信息，便于前端显示
                'infected_ratio': (np.max(I) / N).tolist(),  # 添加最大感染比例
                'prediction_start_date': prediction_start_date.strftime('%Y-%m-%d')  # 添加预测起始日期
            }
            
            # 准备历史数据（最后14条记录）
            historical_data = {}
            if city_data is not None and len(city_data) > 0:
                # 获取最后14条记录
                last_records = city_data.iloc[-14:] if len(city_data) >= 14 else city_data
                
                # 准备历史数据
                historical_data = {
                    'dates': last_records['time'].dt.strftime('%Y-%m-%d').tolist(),
                    'growth': last_records['growth'].tolist(),
                    'cumulative': last_records['total_cases'].tolist()
                }
            
            return prediction_results, "success", historical_data
            
        except Exception as e:
            print(f"Error in get_prediction_for_location: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, "prediction_failed", None

    def get_trend_components(self, prediction_results):
        """获取趋势组件"""
        try:
            if not prediction_results:
                return None, None
                
            # 计算总体趋势（感染者+康复者）
            trend = {
                'trend': [i + r for i, r in zip(prediction_results['infected'], prediction_results['recovered'])],
                'dates': prediction_results['dates']
            }
            
            # 计算阶段性变化
            total_cases = np.array(prediction_results['infected']) + np.array(prediction_results['recovered'])
            daily_changes = np.diff(total_cases, prepend=total_cases[0])
            
            seasonality = {
                'daily_changes': daily_changes.tolist(),
                'phases': self._identify_epidemic_phases(daily_changes)
            }
            
            return trend, seasonality
            
        except Exception as e:
            print(f"Error in get_trend_components: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, None

    def _identify_epidemic_phases(self, daily_changes):
        """识别疫情的不同阶段"""
        phases = []
        n = len(daily_changes)
        window = 5
        
        for i in range(0, n - window + 1):
            avg_change = np.mean(daily_changes[i:i+window])
            if avg_change > 0:
                phases.append('增长期')
            elif avg_change < 0:
                phases.append('下降期')
            else:
                phases.append('平稳期')
        
        # 补齐剩余天数
        phases.extend(['未知'] * (n - len(phases)))
        return phases
