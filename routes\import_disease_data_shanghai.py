from flask import jsonify, request
from model.models import db, DisasterShanghai
from datetime import datetime
import traceback

def handler():
    """处理疾病数据导入的路由"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '没有接收到数据'}), 400
            
        # 获取第一条数据的日期作为判断依据
        date_str = data[0]['date']
        force = request.args.get('force', 'false').lower() == 'true'
        
        try:
            # 转换日期字符串为datetime对象
            date = datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                'success': False,
                'message': f'无效的日期格式: {date_str}'
            }), 400

        # 检查是否存在数据
        existing_count = DisasterShanghai.query.filter(
            db.func.date(DisasterShanghai.time) == date.date()
        ).count()

        if existing_count > 0 and not force:
            return jsonify({
                'success': False, 
                'message': 'exists',
                'date': date_str
            })
            
        try:
            # 开始事务
            if force:
                # 如果是强制导入，先删除已有数据
                DisasterShanghai.query.filter(
                    db.func.date(DisasterShanghai.time) == date.date()
                ).delete()

            # 准备批量插入的数据
            current_time = datetime.now()
            new_records = []
            
            for item in data:
                # Directly get local_hukou and nonlocal_hukou, defaulting to 0 if missing.
                # The upstream disease_data_shanghai.py should provide these as integers.
                local_hukou_val = item.get('local_hukou', 0)
                nonlocal_hukou_val = item.get('nonlocal_hukou', 0)
                total_val = item.get('total', 0) # Get the total value
                
                # Ensure values are integers, as .get might return None if key exists with None value (though unlikely from our parser)
                # or if the default 0 was used. int() is idempotent for integers.
                try:
                    local_hukou_val = int(local_hukou_val) if local_hukou_val is not None else 0
                except (ValueError, TypeError):
                    local_hukou_val = 0

                try:
                    nonlocal_hukou_val = int(nonlocal_hukou_val) if nonlocal_hukou_val is not None else 0
                except (ValueError, TypeError):
                    nonlocal_hukou_val = 0
                
                try:
                    total_val = int(total_val) if total_val is not None else 0
                except (ValueError, TypeError):
                    total_val = 0

                new_records.append(DisasterShanghai(
                    time=date, # 'date' is the common date for the batch, parsed from data[0]['date']
                    disaster=item['diseaseName'],
                    disaster_type='',  # 可以根据需要设置疾病类别
                    sub_disaster=item['subDiseaseName'],
                    cities=item['location'],
                    source_url=item['sourceUrl'],
                    local_hukou=local_hukou_val,
                    nonlocal_hukou=nonlocal_hukou_val,
                    total_hukou=total_val,  # Add total_hukou field
                    update_time=current_time
                ))
            
            # 批量插入数据
            if new_records:
                db.session.bulk_save_objects(new_records)
                db.session.commit()
                
                return jsonify({
                    'success': True,
                    'message': '数据导入成功',
                    'imported_count': len(new_records)
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '没有数据需要导入'
                })
                
        except Exception as e:
            db.session.rollback()
            raise e
            
    except Exception as e:
        print(f"Error in import handler: {str(e)}")
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'导入过程发生错误: {str(e)}'
        }), 500 