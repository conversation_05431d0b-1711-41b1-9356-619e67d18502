/* Custom CSS for filters and search */

#customSearchBox {
    height: 38px;
    /* Adjust the height to match the filter boxes */
    padding: 6px 12px;
    /* Provide sufficient padding */
    margin: 0;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;

}

.ui.buttons .ui.primary.button {
    height: 38px !important;
}

.filter-wrapper {
    display: flex;
    flex-wrap: nowrap;
    /* 防止子元素折行 */
    align-items: center;
    /* 垂直居中对齐 */
    justify-content: flex-start;
    /* 水平开始位置对齐 */
    /* 根据需要调整下面的宽度和间距 */
    max-width: 100%;
    gap: 10px;
    /* 子元素之间的间距 */
}

.filter-label {
    display: inline-block;
    margin-right: 5px;
    line-height: 38px;
    vertical-align: top;
}

.filter-box {
    height: 38px;
    padding: 6px 12px;
    margin-right: 5px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    vertical-align: top;
}

#customSearchBox {
    width: calc(30%);
    /* Adjust the width */
}

#startDate,
#endDate,
#dateRange {
    width: 250px;
}

#dateRange {
    text-align: center;
}

#searchButton1:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

#generateButton,
#resetButton,
#generateManualButton,
#manualButton,
#generateConfig,
#aiSearchButton,
#add-button {
    height: 38px;
}

/* 进度条模态窗口样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
}

.progress {
    position: relative;
    height: 4px;
    display: block;
    width: 100%;
    background-color: #ace;
    border-radius: 2px;
    margin: 0.5rem 0 1rem 0;
    overflow: hidden;
}

.indeterminate {
    background-color: #2a2;
}

.indeterminate:before {
    content: '';
    position: absolute;
    background-color: inherit;
    top: 0;
    left: 50%;
    bottom: 0;
    will-change: left, right;
    animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
}


@keyframes indeterminate {
    0% {
        left: -35%;
        right: 100%;
    }

    60% {
        left: 100%;
        right: -90%;
    }

    100% {
        left: 100%;
        right: -90%;
    }
}

#navbar {
    background-color: #f7f7f7;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    /* 在这里设置导航栏下方的间距 */
}

#branding {
    display: flex;
    align-items: center;
    font-size: 16px;
    /* 根据需要调整字体大小 */
    font-weight: bold;
    color: #333;
}

#user-info {
    display: flex;
    align-items: center;
}

#username {
    font-weight: bold;
    color: #555;
    font-size: 14px;
}

#logoutButton {
    background-color: #d9534f;
    color: white;
    border: none;
    padding: 4px 10px;
    font-size: 14px;
    line-height: 1.2;
    cursor: pointer;
    border-radius: 3px;
}

#logoutButton:hover {
    background-color: #c9302c;
}

.ag-header-cell-label {
    justify-content: center !important;
    font-weight: bold !important;
    font-size: 15px !important;
}

.cell-text-left {
    display: flex;
    justify-content: left;
    align-items: center;
    /* 默认就是左对齐，但这里写出来以便清晰表示 */
}

.cell-text-left-bold {
    display: flex;
    justify-content: left;
    align-items: center;
    font-weight: bold !important;
    /* 默认就是左对齐，但这里写出来以便清晰表示 */
}



.cell-text-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 设置偶数行的背景颜色 */
.ag-row-even {
    background-color: #f9f9f9;
    /* 浅灰色 */
}

/* 设置奇数行的背景颜色 */
.ag-row-odd {
    background-color: #ffffff;
    /* 白色 */
}

.ag-theme-quartz .ag-cell-focus {
    border: none !important;
}

/* 开关样式开始*/
.radio-inputs {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    border-radius: 0.3rem;
    background-color: #EEE;
    box-sizing: border-box;
    box-shadow: 0 0 0px 1px rgba(0, 0, 0, 0.06);
    padding: 0.13rem;
    width: 150px;
    height: 38px;
    font-size: 14px;
    margin-right: 4px;
}

.radio-inputs .radio {
    flex: 1 1 auto;
    text-align: center;
}

.radio-inputs .radio input {
    display: none;
}

.radio-inputs .radio .name {
    display: flex;
    cursor: pointer;
    align-items: center;
    justify-content: center;
    border-radius: 0.3rem;
    border: none;
    padding: .5rem 0;
    color: rgba(51, 65, 85, 1);
    transition: all .15s ease-in-out;
}

.radio-inputs .radio input:checked+.name {
    background-color: #fff;
    font-weight: 600;
}

/* 开关样式结束*/

/* 确保全屏模态窗口占据整个视口 */
.ui.fullscreen.modal {
    width: 95% !important;
    height: 90% !important;
    margin: 50px 40px !important;

}