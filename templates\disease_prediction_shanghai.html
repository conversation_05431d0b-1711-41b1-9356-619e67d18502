<!DOCTYPE html>
<html>

<head>
    <title>上海传染病预测分析</title>
    <link rel="stylesheet" href="//unpkg.com/@arco-design/web-react@2.65.0/dist/css/arco.css">
    <link rel="stylesheet" href="//unpkg.com/@fortawesome/fontawesome-free@6.4.2/css/all.min.css">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="//unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="//unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="//unpkg.com/@arco-design/web-react@2.65.0/dist/arco.min.js"></script>
    <script src="{{ url_for('static', filename='js/moment.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <style>
        :root {
            --primary-color: rgb(var(--primary-6));
            --border-radius: 8px;
            --card-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        body {
            background-color: var(--color-fill-2);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .container {
            width: 98%;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: 24px;
            padding: 16px 24px;
            background: linear-gradient(to right, var(--primary-color), #4080ff);
            border-radius: var(--border-radius);
            color: white;
            box-shadow: var(--card-shadow);
        }

        .page-title {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .page-title i {
            font-size: 28px;
        }

        .content-card {
            background: var(--color-bg-2);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
            overflow: hidden;
            width: 100%;
        }

        .controls {
            margin-bottom: 24px;
            padding: 24px;
            background: var(--color-bg-2);
            border-radius: var(--border-radius);
            box-shadow: var(--card-shadow);
        }

        .controls-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--color-text-1);
            margin: 0 0 20px 0;
            padding-bottom: 16px;
            border-bottom: 1px solid var(--color-border);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .controls-title i {
            font-size: 24px;
            color: var(--primary-color);
        }

        .filter-wrapper {
            display: flex;
            gap: 24px;
            align-items: center;
            padding: 16px 0;
        }

        .disease-date-wrapper {
            display: flex;
            align-items: center;
            gap: 16px;
            background: var(--color-bg-2);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 8px 16px;
            height: 40px;
            width: 100%;
        }

        .filter-item {
            display: flex;
            align-items: center;
            width: auto;
            min-width: 180px;
        }

        .date-range-wrapper {
            width: auto;
            min-width: 280px;
        }

        .prediction-period {
            display: flex;
            align-items: center;
            gap: 16px;
            width: auto;
            min-width: 160px;
        }

        .chart-wrapper {
            background: var(--color-bg-2);
            border: 1px solid var(--color-border);
            border-radius: var(--border-radius);
            padding: 5px;
            margin-top: 20px;
            position: relative;
        }

        .chart-wrapper .help-icon {
            position: absolute;
            right: 10px;
            top: 10px;
            z-index: 1;
        }

        .chart-wrapper .help-icon i {
            font-size: 20px;
            color: #1d2129;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .chart-wrapper .help-icon i:hover {
            opacity: 1;
        }

        .chart-container {
            padding: 0;
            height: 600px;
            width: 100%;
        }

        .generate-button {
            display: flex;
            align-items: center;
            width: auto;
        }

        #dateRangePicker .arco-picker {
            width: 100% !important;
        }

        .prediction-period .arco-slider {
            width: 160px;
        }

        /* 优化滑块样式 */
        .arco-slider {
            width: 160px !important;
            margin: 0;
            padding: 0;
        }

        .arco-slider-wrapper {
            margin: 0;
        }

        .arco-slider-marks-text {
            font-size: 12px;
            color: var(--color-text-3);
        }

        /* 全屏加载动画样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background-color: rgba(255, 255, 255, 0.9);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading-overlay.active {
            display: flex;
        }

        .loading-content {
            text-align: center;
        }

        .loading-text {
            margin-top: 16px;
            color: var(--color-text-1);
            font-size: 16px;
        }
    </style>
</head>

<body>
    <!-- 添加加载动画覆盖层 -->
    <div class="loading-overlay">
        <div class="loading-content">
            <div id="loadingSpinner"></div>
            <div class="loading-text">正在生成预测结果...</div>
        </div>
    </div>

    <div class="container">
        <main class="content-card">
            <div class="controls">
                <h2 class="controls-title">
                    <i class="arco-icon-line-chart"></i>
                    上海传染病预测分析
                </h2>
                <div class="filter-wrapper">
                    <div class="disease-date-wrapper">
                        <div class="filter-item" id="diseaseSelect"></div>
                        <div class="date-range-wrapper" id="dateRangePicker"></div>
                        <div class="prediction-period">
                            <div id="predictionInput"></div>
                        </div>
                        <div class="filter-item" id="indexSelect"></div>
                        <div class="generate-button" id="generateButton"></div>
                    </div>
                </div>
            </div>
            <div class="chart-wrapper">
                <div class="help-icon">
                    <i class="fa-regular fa-circle-question" title="图表说明：实线表示历史数据，虚线表示预测数据，浅色区域表示预测的95%置信区间"></i>
                </div>
                <div class="chart-container"></div>
            </div>
        </main>
    </div>

    <script>
        const { Select, DatePicker, InputNumber, Button, Message, Tag, Tooltip, Drawer, Spin } = arco;

        // 全局变量用于存储选中的指标
        window.selectedIndices = ['local_hukou', 'nonlocal_hukou'];

        // 渲染帮助抽屉
        function renderHelpDrawer() {
            const DrawerContent = () => {
                const style = {
                    section: {
                        marginBottom: '24px'
                    },
                    title: {
                        fontSize: '16px',
                        fontWeight: 'bold',
                        marginBottom: '12px',
                        color: 'var(--color-text-1)'
                    },
                    text: {
                        fontSize: '14px',
                        lineHeight: '1.6',
                        color: 'var(--color-text-2)',
                        marginBottom: '8px'
                    },
                    formula: {
                        backgroundColor: 'var(--color-fill-2)',
                        padding: '12px',
                        borderRadius: '4px',
                        fontFamily: 'monospace',
                        marginBottom: '12px'
                    },
                    list: {
                        paddingLeft: '20px',
                        marginBottom: '12px'
                    },
                    listItem: {
                        marginBottom: '8px',
                        color: 'var(--color-text-2)'
                    }
                };

                return React.createElement('div', null, [
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '预测方法概述'),
                        React.createElement('div', { style: style.text },
                            '本模块使用',
                            React.createElement('strong', null, 'SARIMA'),
                            '（季节性自回归综合移动平均）和',
                            React.createElement('strong', null, 'SEIR'),
                            '（易感-暴露-感染-康复）两种模型进行传染病趋势预测。'
                        ),
                        React.createElement('div', { style: { ...style.text, marginTop: '12px' } }, [
                            React.createElement('strong', null, 'SARIMA'),
                            '模型特别适合处理具有季节性特征的时间序列数据，并结合了节假日因素和疾病特征参数进行预测；'
                        ]),
                        React.createElement('div', { style: { ...style.text, marginTop: '12px' } }, [
                            React.createElement('strong', null, 'SEIR'),
                            '模型则基于疾病传播动力学，能够模拟疾病在人群中的传播过程。系统会综合两种模型的结果，权衡各自优势，生成最终预测。'
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '节假日权重'),
                        React.createElement('div', { style: style.text }, '模型考虑了不同月份的节假日对疾病传播的影响：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, '春节期间（1-2月）：权重1.2-1.5，流动频繁'),
                            React.createElement('li', { style: style.listItem }, '清明节（4月）：权重1.1，短期出行增加'),
                            React.createElement('li', { style: style.listItem }, '劳动节（5月）：权重1.2，人员流动密集'),
                            React.createElement('li', { style: style.listItem }, '暑假期间（7-8月）：权重1.1-1.2，学生放假、旅游高峰'),
                            React.createElement('li', { style: style.listItem }, '国庆节（10月）：权重1.3，长假流动大'),
                            React.createElement('li', { style: style.listItem }, '元旦前后（12-1月）：权重1.1-1.2，跨年活动增多')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '疾病特征参数'),
                        React.createElement('div', { style: style.text }, '模型针对每种疾病的特定特征进行参数调整：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, '潜伏期（Incubation Period）：不同疾病的潜伏期从1天到数月不等'),
                            React.createElement('li', { style: style.listItem }, '基本传染数（R0）：表示一个感染者平均传染给其他人的数量'),
                            React.createElement('li', { style: style.listItem }, '季节性模式：每个月的传播概率权重，反映疾病的季节性特征'),
                            React.createElement('li', { style: style.listItem }, '传播类型：包括呼吸道传播、接触传播、媒介传播等')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, 'SARIMA模型组成'),
                        React.createElement('div', { style: style.text }, 'SARIMA(p,d,q)(P,D,Q)s 模型包含以下组件：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, 'p：非季节性自回归阶数'),
                            React.createElement('li', { style: style.listItem }, 'd：非季节性差分阶数'),
                            React.createElement('li', { style: style.listItem }, 'q：非季节性移动平均阶数'),
                            React.createElement('li', { style: style.listItem }, 'P：季节性自回归阶数'),
                            React.createElement('li', { style: style.listItem }, 'D：季节性差分阶数'),
                            React.createElement('li', { style: style.listItem }, 'Q：季节性移动平均阶数'),
                            React.createElement('li', { style: style.listItem }, 's：季节周期长度')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, 'SEIR模型组成'),
                        React.createElement('div', { style: style.text }, 'SEIR模型是一种经典的传染病动力学模型，将人群分为四个隔室：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, 'S（Susceptible）：易感人群，尚未感染但可能被感染的人群'),
                            React.createElement('li', { style: style.listItem }, 'E（Exposed）：暴露人群，已被感染但尚未表现出症状或具有传染性的人群'),
                            React.createElement('li', { style: style.listItem }, 'I（Infectious）：感染人群，已被感染且具有传染性的人群'),
                            React.createElement('li', { style: style.listItem }, 'R（Recovered）：康复人群，已从感染中康复并获得免疫力的人群')
                        ]),
                        React.createElement('div', { style: style.text }, '该模型参数包括：'),
                        React.createElement('ul', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, 'β：传染率，表示感染者与易感者有效接触的概率'),
                            React.createElement('li', { style: style.listItem }, 'σ：暴露率，表示从暴露状态转变为感染状态的速率（1/潜伏期）'),
                            React.createElement('li', { style: style.listItem }, 'γ：恢复率，表示从感染状态转变为恢复状态的速率（1/感染持续时间）')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '预测流程'),
                        React.createElement('ol', { style: style.list }, [
                            React.createElement('li', { style: style.listItem }, '数据预处理：对原始数据进行季节性分解和差分处理'),
                            React.createElement('li', { style: style.listItem }, 'SARIMA建模：使用AIC/BIC准则自动选择最优SARIMA模型参数，并结合节假日权重和疾病特征参数'),
                            React.createElement('li', { style: style.listItem }, 'SEIR建模：基于疾病特性参数和历史数据趋势计算SEIR模型参数，模拟疾病传播动态过程'),
                            React.createElement('li', { style: style.listItem }, '模型整合：采用加权平均方法整合SARIMA和SEIR两种模型的预测结果，SARIMA占60%权重，SEIR占40%权重'),
                            React.createElement('li', { style: style.listItem }, '置信区间计算：生成最终预测的95%置信区间，反映预测的不确定性')
                        ])
                    ]),
                    React.createElement('div', { style: style.section }, [
                        React.createElement('div', { style: style.title }, '置信区间说明'),
                        React.createElement('div', { style: style.text },
                            '预测结果包含95%置信区间，表示在给定历史数据的条件下，未来观测值有95%的概率落在该区间内。置信区间基于SARIMA统计模型和SEIR动力学模型的整合结果计算，能够同时考虑统计趋势和疾病传播动力学特性。预测期越远，不确定性越大，区间也越宽。'
                        )
                    ])
                ]);
            };

            const drawerRoot = document.createElement('div');
            drawerRoot.id = 'helpDrawerRoot';
            document.body.appendChild(drawerRoot);

            let visible = false;
            const toggleDrawer = () => {
                visible = !visible;
                render();
            };

            const render = () => {
                ReactDOM.render(
                    React.createElement(Drawer, {
                        visible: visible,
                        width: 600,
                        title: '传染病预测分析说明',
                        onCancel: toggleDrawer,
                        footer: null
                    }, React.createElement(DrawerContent)),
                    drawerRoot
                );
            };

            render();
            return toggleDrawer;
        }

        // React组件渲染函数
        function renderReactComponents() {
            const toggleDrawer = renderHelpDrawer();

            // 更新帮助图标，添加点击事件
            const helpIcon = document.querySelector('.help-icon i');
            if (helpIcon) {
                helpIcon.addEventListener('click', toggleDrawer);
            }

            // 预测指标选择
            const IndexSelect = () => {
                const options = [
                    { label: '本市户籍', value: 'local_hukou' },
                    { label: '非本市户籍', value: 'nonlocal_hukou' },
                    { label: '总数', value: 'total_hukou' }
                ];

                return React.createElement(
                    Select,
                    {
                        mode: 'multiple',
                        placeholder: '选择指标',
                        style: { width: 320 },
                        defaultValue: window.selectedIndices,
                        allowClear: true,
                        size: "large",
                        onChange: (values) => {
                            window.selectedIndices = values;
                            // 如果图表已存在，更新图表显示
                            const chartDom = document.querySelector('.chart-container');
                            const chart = echarts.getInstanceByDom(chartDom);
                            if (chart) {
                                const series = chart.getOption().series;
                                series.forEach((s) => {
                                    let shouldShow = false;
                                    values.forEach(value => {
                                        if (s.name.includes(value === 'local_hukou' ? '本市户籍' :
                                            value === 'nonlocal_hukou' ? '非本市户籍' : '总数')) {
                                            shouldShow = true;
                                        }
                                    });
                                    chart.dispatchAction({
                                        type: shouldShow ? 'legendSelect' : 'legendUnSelect',
                                        name: s.name
                                    });
                                });
                            }
                        },
                        renderTag: ({ label, value, closable, onClose }) => {
                            return React.createElement(
                                Tag,
                                {
                                    closable: true,
                                    onClose: onClose,
                                    style: {
                                        backgroundColor: 'var(--color-fill-2)',
                                        border: '1px solid var(--color-border)',
                                        marginRight: '8px',
                                        padding: '4px 8px',
                                    }
                                },
                                label
                            );
                        }
                    },
                    options.map(option =>
                        React.createElement(Select.Option, {
                            key: option.value,
                            value: option.value
                        }, option.label)
                    )
                );
            };

            // 渲染指标选择组件
            ReactDOM.render(
                React.createElement(IndexSelect),
                document.getElementById('indexSelect')
            );

            // 预测周期状态管理
            const PredictionPeriodControl = () => {
                const [value, setValue] = React.useState(6);

                const handleChange = (newValue) => {
                    setValue(newValue);
                    window.predictionPeriod = newValue; // 存储当前值到全局
                };

                return React.createElement(
                    'div',
                    {
                        style: {
                            display: 'flex',
                            alignItems: 'center',
                            gap: '20px'
                        }
                    },
                    React.createElement(InputNumber, {
                        min: 1,
                        max: 12,
                        value: value,
                        prefix: "预测周期",
                        suffix: "个月",
                        size: "large",
                        id: 'predictionPeriod',
                        style: { width: 160 },
                        onChange: handleChange
                    }),
                    React.createElement(arco.Slider, {
                        min: 1,
                        max: 12,
                        value: value,
                        style: { width: 200 },
                        id: 'periodSlider',
                        marks: {
                            1: '1',
                            3: '3',
                            6: '6',
                            9: '9',
                            12: '12'
                        },
                        formatTooltip: (val) => `${val}月`,
                        onChange: handleChange
                    })
                );
            };

            // 全局变量存储日期范围
            window.dateRange = [
                moment().subtract(3, 'years').startOf('month').format('YYYY-MM-DD'),
                moment().endOf('month').format('YYYY-MM-DD')
            ];

            // 日期范围选择器
            ReactDOM.render(
                React.createElement(DatePicker.RangePicker, {
                    style: { width: 280 },
                    mode: 'month',
                    size: "large",
                    onChange: (valueString, value) => {
                        if (value && value.length === 2) {
                            window.dateRange = [
                                value[0].startOf('month').format('YYYY-MM-DD'),
                                value[1].endOf('month').format('YYYY-MM-DD')
                            ];
                        }
                    },
                    defaultValue: [
                        moment().subtract(3, 'years').startOf('month'),
                        moment().endOf('month')
                    ],
                    shortcuts: [
                        {
                            text: '近3月',
                            value: () => [moment().subtract(3, 'months').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近6月',
                            value: () => [moment().subtract(6, 'months').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近1年',
                            value: () => [moment().subtract(1, 'year').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近2年',
                            value: () => [moment().subtract(2, 'years').startOf('month'), moment().endOf('month')]
                        },
                        {
                            text: '近3年',
                            value: () => [moment().subtract(3, 'years').startOf('month'), moment().endOf('month')]
                        }
                    ]
                }),
                document.getElementById('dateRangePicker')
            );

            // 初始化预测周期的全变量
            window.predictionPeriod = 6;

            // 渲染预测周期控制组
            ReactDOM.render(
                React.createElement(PredictionPeriodControl),
                document.getElementById('predictionInput')
            );

            // 疾病选择下拉框
            ReactDOM.render(
                React.createElement(Select, {
                    placeholder: '选择传染病',
                    style: { width: 180 },
                    allowClear: true,
                    showSearch: true,
                    size: "large",
                    id: 'disease',
                    onChange: (value) => {
                        window.selectedDisease = value;
                    }
                }),
                document.getElementById('diseaseSelect')
            );

            // 生成预测按钮
            ReactDOM.render(
                React.createElement(Button, {
                    type: 'primary',
                    icon: React.createElement('i', { className: 'arco-icon-line-chart' }),
                    onClick: handleGenerate,
                    size: "large"
                }, "生成预测"),
                document.getElementById('generateButton')
            );
        }

        // 页面加载完成后初始化
        $(document).ready(function () {
            renderReactComponents();
            renderLoadingSpinner();

            // 加载疾病数据
            $.ajax({
                url: '/get_diseases_shanghai',
                method: 'GET',
                timeout: 10000,
                success: function (data) {
                    if (!data || data.error) {
                        console.error('Failed to load diseases:', data.error);
                        Message.error('加载疾病列表失败: ' + (data.error || '未知错误'));
                        return;
                    }

                    if (data.diseases && Array.isArray(data.diseases)) {
                        const options = data.diseases.map(disease => ({
                            label: disease.name,
                            value: disease.value
                        }));

                        ReactDOM.render(
                            React.createElement(Select, {
                                placeholder: '请选择传染病名称',
                                style: { width: 240 },
                                allowClear: true,
                                showSearch: true,
                                prefix: "传染病名称",
                                size: "large",
                                id: 'disease',
                                options: options,
                                onChange: (value) => {
                                    window.selectedDisease = value;
                                }
                            }),
                            document.getElementById('diseaseSelect')
                        );
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Failed to load diseases:', error);
                    let errorMessage = '加载疾病列表失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage += ': ' + xhr.responseJSON.error;
                    }
                    Message.error(errorMessage);
                }
            });
        });

        // 设置预测周期
        function setPredictionPeriod(months) {
            const inputNumber = document.querySelector('#predictionPeriod');
            if (inputNumber) {
                inputNumber.value = months;
            }
        }

        // 渲染加载动画
        function renderLoadingSpinner() {
            ReactDOM.render(
                React.createElement(Spin, {
                    dot: true,
                    size: 'large',
                    style: { fontSize: '32px' }
                }),
                document.getElementById('loadingSpinner')
            );
        }

        // 显示加载动画
        function showLoading() {
            document.querySelector('.loading-overlay').classList.add('active');
        }

        // 隐藏加载动画
        function hideLoading() {
            document.querySelector('.loading-overlay').classList.remove('active');
        }

        // 处理生成预测
        function handleGenerate() {
            if (!window.selectedDisease) {
                Message.error('请选择传染病');
                return;
            }

            const generateBtn = document.querySelector('#generateButton button');
            generateBtn.setAttribute('loading', 'true');
            generateBtn.disabled = true;

            // 显示加载动画
            showLoading();

            // 获取日期范围，如果没有选择则使用默认值（2年前到今天）
            const defaultEndDate = moment().format('YYYY-MM-DD');
            const defaultStartDate = moment().subtract(2, 'years').format('YYYY-MM-DD');
            const startDate = window.dateRange ? window.dateRange[0] : defaultStartDate;
            const endDate = window.dateRange ? window.dateRange[1] : defaultEndDate;

            $.ajax({
                url: '/predict_disease_shanghai',
                method: 'POST',
                data: {
                    disease: window.selectedDisease,
                    start_date: startDate,
                    end_date: endDate,
                    prediction_period: window.predictionPeriod
                },
                success: function (response) {
                    generateBtn.removeAttribute('loading');
                    generateBtn.disabled = false;
                    hideLoading();
                    updateChart(response);
                },
                error: function (xhr, status, error) {
                    let errorMessage = '生成预测失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    Message.error(errorMessage);
                    generateBtn.removeAttribute('loading');
                    generateBtn.disabled = false;
                    hideLoading();
                }
            });
        }

        // 更新图表
        function updateChart(data) {
            // 数据验证
            if (!data || typeof data !== 'object') {
                Message.error('返回数据格式错误');
                return;
            }

            // 检查必要的数据字段
            const requiredFields = ['historical_dates', 'dates', 'historical_data', 'predictions'];
            const missingFields = requiredFields.filter(field => !data[field]);
            if (missingFields.length > 0) {
                Message.error('数据结构不完整: ' + missingFields.join(', '));
                return;
            }

            // 检查数据类型
            if (!Array.isArray(data.historical_dates) || !Array.isArray(data.dates)) {
                Message.error('日期数据类型错误');
                return;
            }

            if (!data.historical_data || !data.predictions ||
                !data.predictions.local_hukou || !data.predictions.nonlocal_hukou || !data.predictions.total_hukou) {
                Message.error('预测数据结构不完整');
                return;
            }

            const chartDom = document.querySelector('.chart-container');
            if (!chartDom) {
                Message.error('图表容器不存在');
                return;
            }

            // 销毁现有的图表实例（如果存在）
            const existingChart = echarts.getInstanceByDom(chartDom);
            if (existingChart) {
                existingChart.dispose();
            }

            // 初始化新的图表实例
            const myChart = echarts.init(chartDom);

            try {
                // 合并历史数据和预测数据的日期
                const allDates = [...data.historical_dates, ...data.dates];

                // 准备数据系列
                const series = [
                    // 本市户籍
                    {
                        name: '本市户籍预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.local_hukou.upper],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#5470c6',
                            origin: 'end'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '本市户籍预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.local_hukou.lower],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#5470c6',
                            origin: 'start'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '历史本市户籍',
                        type: 'line',
                        data: data.historical_data.local_hukou,
                        lineStyle: { width: 2 },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#5470c6' }
                    },
                    {
                        name: '预测本市户籍',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length - 1).fill(null), data.historical_data.local_hukou[data.historical_data.local_hukou.length - 1], ...data.predictions.local_hukou.mean],
                        lineStyle: { width: 2, type: 'dashed' },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#5470c6' }
                    },

                    // 非本市户籍
                    {
                        name: '非本市户籍预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.nonlocal_hukou.upper],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#ee6666',
                            origin: 'end'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '非本市户籍预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.nonlocal_hukou.lower],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#ee6666',
                            origin: 'start'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '历史非本市户籍',
                        type: 'line',
                        data: data.historical_data.nonlocal_hukou,
                        lineStyle: { width: 2 },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#ee6666' }
                    },
                    {
                        name: '预测非本市户籍',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length - 1).fill(null), data.historical_data.nonlocal_hukou[data.historical_data.nonlocal_hukou.length - 1], ...data.predictions.nonlocal_hukou.mean],
                        lineStyle: { width: 2, type: 'dashed' },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#ee6666' }
                    },

                    // 总数
                    {
                        name: '总数预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.total_hukou.upper],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#73c0de',
                            origin: 'end'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '总数预测区间',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length).fill(null), ...data.predictions.total_hukou.lower],
                        lineStyle: { opacity: 0 },
                        areaStyle: {
                            opacity: 0.3,
                            color: '#73c0de',
                            origin: 'start'
                        },
                        symbol: 'none',
                        showSymbol: false
                    },
                    {
                        name: '历史总数',
                        type: 'line',
                        data: data.historical_data.total_hukou,
                        lineStyle: { width: 2 },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#73c0de' }
                    },
                    {
                        name: '预测总数',
                        type: 'line',
                        data: [...new Array(data.historical_dates.length - 1).fill(null), data.historical_data.total_hukou[data.historical_data.total_hukou.length - 1], ...data.predictions.total_hukou.mean],
                        lineStyle: { width: 2, type: 'dashed' },
                        symbol: 'circle',
                        symbolSize: 6,
                        itemStyle: { color: '#73c0de' }
                    }
                ];

                const option = {
                    title: {
                        text: '上海疾病预测分析',
                        subtext: '基于SARIMA和SEIR模型',
                        left: 'center',
                        top: 10,
                        padding: [0, 0, 10, 0]
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross'
                        },
                        formatter: function (params) {
                            let result = params[0].axisValueLabel + '<br/>';

                            // 判断是否是预测数据
                            const isPrediction = params[0].axisValueLabel >= data.dates[0];

                            // 按照类型分组显示数据
                            const groups = {
                                '本市户籍': { color: '#5470c6', data: [], ci: {} },
                                '非本市户籍': { color: '#ee6666', data: [], ci: {} },
                                '总数': { color: '#73c0de', data: [], ci: {} }
                            };

                            params.forEach(param => {
                                // 处理置信区间数据
                                if (param.seriesName.includes('预测区间')) {
                                    Object.keys(groups).forEach(key => {
                                        if (param.seriesName.includes(key)) {
                                            if (param.value !== null) {
                                                if (!groups[key].ci.upper || param.value > groups[key].ci.upper) {
                                                    groups[key].ci.upper = param.value;
                                                }
                                                if (!groups[key].ci.lower || param.value < groups[key].ci.lower) {
                                                    groups[key].ci.lower = param.value;
                                                }
                                            }
                                        }
                                    });
                                    return;
                                }

                                // 只显示历史数据或预测数据
                                const isParamPrediction = param.seriesName.includes('预测');
                                if (isPrediction !== isParamPrediction) {
                                    return;
                                }

                                // 更精确地将数据分配到正确的分组中
                                if ((param.seriesName.includes('本市户籍') && !param.seriesName.includes('非本市户籍'))) {
                                    groups['本市户籍'].data.push({
                                        name: param.seriesName,
                                        value: param.value
                                    });
                                } else if (param.seriesName.includes('非本市户籍')) {
                                    groups['非本市户籍'].data.push({
                                        name: param.seriesName,
                                        value: param.value
                                    });
                                } else if (param.seriesName.includes('总数') && !param.seriesName.includes('户籍')) {
                                    groups['总数'].data.push({
                                        name: param.seriesName,
                                        value: param.value
                                    });
                                }
                            });

                            // 生成tooltip内容
                            Object.keys(groups).forEach(key => {
                                if (groups[key].data.length > 0) {
                                    result += '<div style="margin: 10px 0;">';
                                    result += `<span style="color:${groups[key].color}">● ${key}：</span><br/>`;
                                    groups[key].data.forEach(item => {
                                        if (item.value !== null) {
                                            result += `${item.name}：${item.value >= 10000 ? (item.value / 10000).toFixed(2) + '万' : item.value}<br/>`;
                                        }
                                    });
                                    // 如果是预测数据，显示置信区间
                                    if (isPrediction && groups[key].ci.upper !== undefined && groups[key].ci.lower !== undefined) {
                                        const upperValue = groups[key].ci.upper >= 10000 ? (groups[key].ci.upper / 10000).toFixed(2) + '万' : groups[key].ci.upper;
                                        const lowerValue = groups[key].ci.lower >= 10000 ? (groups[key].ci.lower / 10000).toFixed(2) + '万' : groups[key].ci.lower;
                                        result += `95%置信区间：[${lowerValue}, ${upperValue}]<br/>`;
                                    }
                                    result += '</div>';
                                }
                            });

                            return result;
                        }
                    },
                    legend: {
                        data: ['历史本市户籍', '预测本市户籍', '历史非本市户籍', '预测非本市户籍', '历史总数', '预测总数'],
                        bottom: 10,
                        selected: {
                            '历史本市户籍': window.selectedIndices.includes('local_hukou'),
                            '预测本市户籍': window.selectedIndices.includes('local_hukou'),
                            '本市户籍预测区间': window.selectedIndices.includes('local_hukou'),
                            '历史非本市户籍': window.selectedIndices.includes('nonlocal_hukou'),
                            '预测非本市户籍': window.selectedIndices.includes('nonlocal_hukou'),
                            '非本市户籍预测区间': window.selectedIndices.includes('nonlocal_hukou'),
                            '历史总数': window.selectedIndices.includes('total_hukou'),
                            '预测总数': window.selectedIndices.includes('total_hukou'),
                            '总数预测区间': window.selectedIndices.includes('total_hukou')
                        }
                    },
                    grid: {
                        left: '3%',
                        right: '4%',
                        bottom: '15%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: allDates,
                        axisLabel: {
                            rotate: 45,
                            formatter: function (value) {
                                return value.substring(0, 7); // 显示年月
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: '人数',
                        axisLabel: {
                            formatter: function (value) {
                                if (value >= 10000) {
                                    return (value / 10000).toFixed(1) + '万';
                                }
                                return value;
                            }
                        }
                    },
                    series: series,
                    dataZoom: [
                        {
                            type: 'slider',
                            show: true,
                            start: 0,
                            end: 100,
                            bottom: 50
                        },
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        }
                    ]
                };

                myChart.setOption(option);

                // 处理窗口大小变化
                window.addEventListener('resize', () => {
                    if (myChart) {
                        myChart.resize();
                    }
                });
            } catch (error) {
                Message.error('更新图表时发生错误');
            }
        }

        // 页面加载完成后初始化图表容器
        document.addEventListener('DOMContentLoaded', function () {
            const chartDom = document.querySelector('.chart-container');
            if (chartDom) {
                const myChart = echarts.init(chartDom);
                myChart.setOption({
                    title: {
                        text: '请选择疾病并生成预测',
                        left: 'center',
                        top: '40%'
                    }
                });
            }
        });

        function renderHelpIcon() {
            ReactDOM.render(
                React.createElement(Tooltip, {
                    content: '图表说明：实线表示历史数据，虚线表示预测数据，浅色区域表示预测的95%置信区间',
                    position: 'top'
                },
                    React.createElement('i', {
                        className: 'fa-solid fa-circle-question',
                        style: {
                            fontSize: '20px',
                            color: '#1d2129',
                            cursor: 'pointer',
                            opacity: '0.8',
                            transition: 'opacity 0.2s'
                        },
                        onMouseEnter: (e) => e.target.style.opacity = '1',
                        onMouseLeave: (e) => e.target.style.opacity = '0.8'
                    })
                ),
                document.getElementById('chartHelpIcon')
            );
        }
    </script>
</body>

</html>