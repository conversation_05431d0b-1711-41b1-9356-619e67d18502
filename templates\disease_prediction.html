<!DOCTYPE html>
<html>
<head>
    <title>疾病传播预测分析</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <style>
        .container {
            padding: 20px;
            width: 100%;
            margin: 0 auto;
        }
        .chart-container {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            gap: 20px;
            margin-top: 20px;
        }
        .chart-wrapper {
            position: relative;
            width: calc(50% - 10px);
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
        }
        .chart {
            width: 100%;
            height: 400px;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .chart-help {
            position: absolute;
            top: 10px;
            right: 10px;
            cursor: pointer;
            color: rgba(0,0,0,0.5);
            z-index: 1000;
            font-size: 16px;
        }
        .chart-help:hover {
            color: rgba(0,0,0,0.8);
        }
        .ui.popup {
            max-width: 400px !important;
            white-space: pre-line;
            font-size: 13px;
            line-height: 1.6;
            padding: 15px;
        }
        .ui.popup h4 {
            margin: 0 0 8px 0;
            color: #1a1a1a;
            font-size: 14px;
        }
        .ui.popup p {
            margin: 0 0 10px 0;
        }
        .ui.popup ul {
            margin: 5px 0;
            padding-left: 20px;
        }
        .ui.popup .math-formula {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            margin: 8px 0;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="ui segment">
            <h2 class="ui top attached header">
                <i class="chart line icon"></i>
                疾病传播预测分析
            </h2>
            <div class="ui attached segment">
                <div class="filter-wrapper" style="margin-top: 5px;">
                    <div class="ui floating dropdown labeled icon button">
                        <input type="hidden" name="country">
                        <i class="globe icon"></i>
                        <span class="text">请选择国家</span>
                        <div class="menu">
                            <div class="ui icon search input">
                                <i class="search icon"></i>
                                <input type="text" placeholder="选择国家..." id="countrySearchInput">
                            </div>
                            <div class="divider"></div>
                            <div class="header">
                                <i class="world icon"></i>
                                国家列表
                            </div>
                            <div class="scrolling menu" id="countryList">
                            </div>
                        </div>
                    </div>

                    <div class="ui floating dropdown labeled icon button">
                        <input type="hidden" name="disease">
                        <i class="filter icon"></i>
                        <span class="text">请选择传染病名称</span>
                        <div class="menu">
                            <div class="ui icon search input">
                                <i class="search icon"></i>
                                <input type="text" placeholder="选择传染病..." id="diseaseSearchInput">
                            </div>
                            <div class="divider"></div>
                            <div class="header">
                                <i class="tags icon"></i>
                                传染病列表
                            </div>
                            <div class="scrolling menu" id="diseaseList">
                            </div>
                        </div>
                    </div>

                    <button id="generateButton" class="ui button green" style="height: 36px; margin-left: 10px; padding: 9px 15px;">
                        <i class="chart line icon"></i>
                        生成预测
                    </button>
                </div>
                <div class="chart-container">
                    <div class="chart-wrapper">
                        <div id="predictionChart" class="chart"></div>
                        <i class="question circle outline icon chart-help" 
                           data-content="每日新增病例预测" data-position="right center"></i>
                    </div>
                    <div class="chart-wrapper">
                        <div id="seirChart" class="chart"></div>
                        <i class="question circle outline icon chart-help" 
                           data-content="SEIR模型" data-position="left center"></i>
                    </div>
                    <div class="chart-wrapper">
                        <div id="trendChart" class="chart"></div>
                        <i class="question circle outline icon chart-help" 
                           data-content="累计病例长期趋势" data-position="right center"></i>
                    </div>
                    <div class="chart-wrapper">
                        <div id="phasesChart" class="chart"></div>
                        <i class="question circle outline icon chart-help" 
                           data-content="疫情阶段" data-position="left center"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 初始化下拉框
            $('.ui.dropdown').dropdown({
                fullTextSearch: true,
                match: 'text',
                forceSelection: false,
                selectOnKeydown: false,
                showOnFocus: true,
                allowAdditions: false
            });

            // 全局变量存储国家和疾病数据
            let allCountries = [];
            let allDiseases = [];
            let countryDiseaseMap = {}; // 国家-疾病映射
            let diseaseCountryMap = {}; // 疾病-国家映射
            
            // 加载国家和疾病数据
            $.ajax({
                url: '/get_dict_config',
                method: 'GET',
                timeout: 10000,
                success: function(data) {
                    if (!data || data.error) {
                        console.error('Failed to load config:', data.error);
                        alert('加载配置失败: ' + (data.error || '未知错误'));
                        return;
                    }
                    
                    // 保存所有国家和疾病数据
                    if (data.countries && Array.isArray(data.countries)) {
                        allCountries = data.countries;
                    }
                    
                    if (data.diseases && Array.isArray(data.diseases)) {
                        allDiseases = data.diseases;
                    }
                    
                    // 加载国家-疾病关联数据
                    loadRelatedData();
                },
                error: function(xhr, status, error) {
                    console.error('Failed to load config:', error);
                    let errorMessage = '加载配置失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage += ': ' + xhr.responseJSON.error;
                    }
                    alert(errorMessage);
                }
            });

            // 加载国家和疾病的关联数据
            function loadRelatedData() {
                $.ajax({
                    url: '/get_related_data',
                    method: 'GET',
                    success: function(data) {
                        if (!data || data.error) {
                            console.error('Failed to load related data:', data.error);
                            // 如果加载关联数据失败，仍然加载所有选项
                            populateDropdowns(allCountries, allDiseases);
                            return;
                        }
                        
                        // 保存关联数据
                        countryDiseaseMap = data.country_disease_map || {};
                        diseaseCountryMap = data.disease_country_map || {};
                        
                        // 填充下拉框
                        populateDropdowns(allCountries, allDiseases);
                        
                        // 设置下拉框事件
                        setupDropdownEvents();
                    },
                    error: function(xhr, status, error) {
                        console.error('Error loading related data:', error);
                        // 如果加载关联数据失败，仍然加载所有选项
                        populateDropdowns(allCountries, allDiseases);
                    }
                });
            }
            
            // 填充下拉框
            function populateDropdowns(countries, diseases) {
                const countryList = $('#countryList');
                const diseaseList = $('#diseaseList');
                
                // 清空现有选项
                countryList.empty();
                diseaseList.empty();
                
                // 添加国家
                if (countries && countries.length > 0) {
                    countries.forEach(country => {
                        countryList.append(`
                            <div class="item" data-value="${country.data_value}">
                                <div class="ui green empty circular label"></div>
                                ${country.name}
                            </div>
                        `);
                    });
                }
                
                // 添加疾病
                if (diseases && diseases.length > 0) {
                    diseases.forEach(disease => {
                        diseaseList.append(`
                            <div class="item" data-value="${disease.data_value}">
                                <div class="ui green empty circular label"></div>
                                ${disease.name}
                            </div>
                        `);
                    });
                }
                
                // 初始化下拉框
                $('.ui.dropdown').dropdown('refresh');
            }
            
            // 设置下拉框事件
            function setupDropdownEvents() {
                // 国家下拉框变化事件
                $('input[name="country"]').parent().dropdown({
                    onChange: function(value) {
                        if (!value) return;
                        
                        // 重置疾病下拉框
                        $('input[name="disease"]').parent().dropdown('clear');
                        

                        
                        // 如果选择了特定国家，只显示该国家发生过的疾病
                        const relatedDiseases = countryDiseaseMap[value] || [];
                        const filteredDiseases = allDiseases.filter(disease => 
                            relatedDiseases.includes(disease.data_value)
                        );
                        
                        // 更新疾病下拉框
                        const diseaseList = $('#diseaseList');
                        diseaseList.find('.item:not([data-value="clear"])').remove();
                        
                        filteredDiseases.forEach(disease => {
                            diseaseList.append(`
                                <div class="item" data-value="${disease.data_value}">
                                    <div class="ui green empty circular label"></div>
                                    ${disease.name}
                                </div>
                            `);
                        });
                        
                        // 刷新下拉框
                        $('input[name="disease"]').parent().dropdown('refresh');
                    }
                });
                
                // 疾病下拉框变化事件
                $('input[name="disease"]').parent().dropdown({
                    onChange: function(value) {
                        if (!value) return;
                        
                        // 重置国家下拉框
                        $('input[name="country"]').parent().dropdown('clear');
                        

                        
                        // 如果选择了特定疾病，只显示发生过该疾病的国家
                        const relatedCountries = diseaseCountryMap[value] || [];
                        const filteredCountries = allCountries.filter(country => 
                            relatedCountries.includes(country.data_value)
                        );
                        
                        // 更新国家下拉框
                        const countryList = $('#countryList');
                        countryList.find('.item:not([data-value="all"])').remove();
                        
                        filteredCountries.forEach(country => {
                            countryList.append(`
                                <div class="item" data-value="${country.data_value}">
                                    <div class="ui green empty circular label"></div>
                                    ${country.name}
                                </div>
                            `);
                        });
                        
                        // 刷新下拉框
                        $('input[name="country"]').parent().dropdown('refresh');
                    }
                });
            }
            
            // 监听生成预测按钮的点击事件
            $('#generateButton').on('click', function() {
                const country = $('input[name="country"]').val();
                const disease = $('input[name="disease"]').val();

                if (!country || !disease) {
                    alert('请选择国家和传染病');
                    return;
                }
                
                // 检查是否选择了"全部"选项
                if (country === 'all' && disease === 'all') {
                    alert('请至少选择一个具体的国家或传染病');
                    return;
                }

                // 移除现有的错误消息
                $('.ui.negative.message').remove();

                // 显示加载状态
                $(this).addClass('loading').prop('disabled', true);
                $('.chart-wrapper').addClass('loading');

                // 发送预测请求
                $.post('/predict_disease', {
                    country: country,
                    disease: disease
                }, function(response) {
                    updateCharts(response);
                    $('#generateButton').removeClass('loading').prop('disabled', false);
                    $('.chart-wrapper').removeClass('loading');
                }).fail(function(xhr) {
                    let errorMessage = '生成预测失败';
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        errorMessage = xhr.responseJSON.error;
                    }
                    // 使用 Semantic UI 的消息组件显示错误
                    const errorDiv = $('<div class="ui negative message" style="margin-top: 20px;">' +
                        '<i class="close icon"></i>' +
                        '<div class="header">预测失败</div>' +
                        '<p>' + errorMessage + '</p>' +
                    '</div>');
                    
                    // 移除之前的错误消息
                    $('.ui.negative.message').remove();
                    
                    // 添加新的错误消息
                    $('.chart-container').before(errorDiv);
                    
                    // 初始化关闭按钮
                    errorDiv.find('.close').on('click', function() {
                        $(this).closest('.message').transition('fade');
                    });

                    // 3秒后自动消失
                    setTimeout(function() {
                        errorDiv.transition('fade');
                    }, 3000);
                    
                    $('#generateButton').removeClass('loading').prop('disabled', false);
                    $('.chart-wrapper').removeClass('loading');
                });
            });

            const tooltipContent = {
                'predictionChart': `
<h4>预测趋势图原理</h4>
<p>该图表展示了疾病传播的历史数据（蓝色）和预测趋势（红色），基于SEIR模型计算得出。</p>

<h4>数学模型说明</h4>
<p>SEIR模型是一个经典的传染病数学模型，由以下微分方程组成：</p>
<div class="math-formula">dS/dt = -βSI/N
dE/dt = βSI/N - σE
dI/dt = σE - γI
dR/dt = γI</div>

<h4>参数说明</h4>
• S: 易感人群(Susceptible) - 可能被感染的人群
• E: 潜伏人群(Exposed) - 已被感染但未显症状的人群
• I: 感染人群(Infected) - 已显症状且具有传染性的人群
• R: 康复人群(Recovered) - 已康复且具有免疫力的人群
• β: 传染率 - 疾病的传播速率
• σ: 潜伏期转化率 - 从潜伏到发病的转化率
• γ: 康复率 - 患者康复的速率
• N: 总人口数

<h4>预测方法</h4>
<p>系统通过分析历史数据，结合SEIR模型参数动态调整，预测未来趋势：</p>
• 历史传播数据分析
• 人口密度因素考虑
• 季节性影响评估
• 防控措施效果评估`,
                'seirChart': `
<h4>SEIR组件图说明</h4>
<p>该图表展示了SEIR模型中各个组件的动态变化情况。</p>

<h4>组件说明</h4>
• 蓝线: 易感人群(S) - 显示可能被感染的人群变化
• 黄线: 潜伏人群(E) - 显示已感染但未发病的人群变化
• 红线: 感染人群(I) - 显示正在传播疾病的人群变化
• 绿线: 康复人群(R) - 显示已康复具有免疫力的人群变化

<h4>图表特点</h4>
• 实时显示各组人群的数量变化
• 直观展示疾病传播的各个阶段
• 可预测关键时间点和峰值`,
                'trendChart': `
<h4>长期趋势分析</h4>
<p>该图表展示了疾病传播的长期发展趋势。</p>

<h4>关键指标</h4>
• 累计病例数增长趋势
• 传播速度变化
• 拐点预测
• 长期影响评估

<h4>预测方法</h4>
<p>系统采用时间序列分析和机器学习方法，结合以下因素：</p>
• 历史数据趋势
• 季节性因素
• 社会干预措施
• 人口流动性`,
                'phasesChart': `
<h4>疫情阶段划分</h4>
<p>该图表展示了疫情发展的不同阶段。</p>

<h4>阶段说明</h4>
• 初始阶段: 疾病开始传播
• 快速增长: 病例数快速上升
• 高峰期: 新增病例达到顶峰
• 平缓期: 新增病例趋于稳定
• 下降期: 新增病例明显减少
• 控制期: 疫情基本受控

<h4>阶段划分依据</h4>
• 新增病例数变化率
• 累计病例数增长曲线
• 传播速度变化
• 防控措施效果`
            };

            // 初始化问号图标点击事件
            $('.chart-wrapper').each(function(index) {
                const chartId = $(this).find('.chart').attr('id');
                $(this).find('.chart-help').popup({
                    on: 'click',
                    position: 'left center',
                    html: tooltipContent[chartId],
                    hoverable: true,
                    exclusive: true,
                    lastResort: true,
                    delay: {
                        show: 100,
                        hide: 300
                    }
                });
            });

            // 格式化数字，自动适配单位（万、十万、百万、千万、亿）
            function formatNumber(num) {
                if (num === null || num === undefined || isNaN(num)) {
                    return '0';
                }
                
                const absNum = Math.abs(num);
                
                if (absNum >= 100000000) { // 亿
                    return (num / 100000000).toFixed(2) + ' 亿';
                } else if (absNum >= 10000000) { // 千万
                    return (num / 10000000).toFixed(2) + ' 千万';
                } else if (absNum >= 1000000) { // 百万
                    return (num / 1000000).toFixed(2) + ' 百万';
                } else if (absNum >= 100000) { // 十万
                    return (num / 100000).toFixed(2) + ' 十万';
                } else if (absNum >= 10000) { // 万
                    return (num / 10000).toFixed(2) + ' 万';
                } else if (absNum >= 1000) { // 千
                    return (num / 1000).toFixed(2) + ' 千';
                } else {
                    return num.toFixed(0);
                }
            }
            
            // 更新所有图表
            function updateCharts(data) {
                // 预测趋势图
                const predictionChart = echarts.init(document.getElementById('predictionChart'));
                
                // 准备历史数据和预测数据的系列
                let series = [];
                
                // 历史数据和预测数据的日期
                let historyDates = [];
                let historyData = [];
                let predictionDates = data.dates;
                let predictionData = data.predictions;
                
                // 如果有历史数据，添加历史数据系列
                if (data.historical_data && data.historical_data.dates && data.historical_data.growth) {
                    historyDates = data.historical_data.dates;
                    historyData = data.historical_data.growth;
                    
                    series.push({
                        name: '历史数据',
                        type: 'line',
                        data: historyData.map((value, index) => [historyDates[index], value]),
                        smooth: true,
                        symbolSize: 8,
                        itemStyle: {
                            color: '#3498db' // 蓝色表示历史数据
                        },
                        lineStyle: {
                            width: 3
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    });
                }
                
                // 添加预测数据系列
                series.push({
                    name: '预测数据',
                    type: 'line',
                    data: predictionData.map((value, index) => [predictionDates[index], value]),
                    smooth: true,
                    symbolSize: 8,
                    itemStyle: {
                        color: '#e74c3c' // 红色表示预测数据
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    areaStyle: {
                        opacity: 0.3,
                        color: '#e74c3c'
                    }
                });
                
                // 添加连接历史和预测的虚线
                if (historyDates.length > 0 && historyData.length > 0 && predictionDates.length > 0 && predictionData.length > 0) {
                    series.push({
                        name: '连接线',
                        type: 'line',
                        data: [
                            [historyDates[historyDates.length - 1], historyData[historyData.length - 1]],
                            [predictionDates[0], predictionData[0]]
                        ],
                        symbol: 'none',
                        lineStyle: {
                            type: 'dashed',
                            color: 'grey',
                            width: 1
                        },
                        tooltip: { show: false },
                        legendHoverLink: false,
                        silent: true // 使其不响应鼠标事件
                    });
                }
                
                // 合并所有日期以确定 X 轴范围
                let allDates = [];
                if (historyDates.length > 0) {
                    allDates = [...historyDates, ...predictionDates];
                } else {
                    allDates = predictionDates;
                }
                
                predictionChart.setOption({
                    title: { text: '预测趋势' },
                    tooltip: { 
                        trigger: 'axis',
                        formatter: function(params) {
                            let result = params[0].name + '<br/>';
                            params.forEach(param => {
                                result += param.seriesName + ': ' + formatNumber(param.value[1]) + '<br/>';
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['历史数据', '预测数据'],
                        top: 25
                    },
                    xAxis: { 
                        type: 'category',
                        axisLabel: {
                            formatter: function(value) {
                                // 简化日期显示
                                return value.substring(5); // 只显示月-日
                            }
                        }
                    },
                    yAxis: { 
                        type: 'value',
                        axisLabel: {
                            formatter: function(value) {
                                return formatNumber(value);
                            }
                        }
                    },
                    series: series
                });

                // SEIR模型组件图
                const seirChart = echarts.init(document.getElementById('seirChart'));
                seirChart.setOption({
                    title: { text: 'SEIR模型组件' },
                    tooltip: { 
                        trigger: 'axis',
                        formatter: function(params) {
                            let result = params[0].name + '<br/>';
                            params.forEach(param => {
                                result += param.seriesName + ': ' + formatNumber(param.value) + '<br/>';
                            });
                            return result;
                        }
                    },
                    legend: { data: ['易感人群', '潜伏人群', '感染人群', '康复人群'] },
                    xAxis: { type: 'category', data: data.dates },
                    yAxis: { 
                        type: 'value',
                        axisLabel: {
                            formatter: function(value) {
                                return formatNumber(value);
                            }
                        }
                    },
                    series: [
                        {
                            name: '易感人群',
                            type: 'line',
                            data: data.seir.susceptible,
                            smooth: true
                        },
                        {
                            name: '潜伏人群',
                            type: 'line',
                            data: data.seir.exposed,
                            smooth: true
                        },
                        {
                            name: '感染人群',
                            type: 'line',
                            data: data.seir.infected,
                            smooth: true
                        },
                        {
                            name: '康复人群',
                            type: 'line',
                            data: data.seir.recovered,
                            smooth: true
                        }
                    ]
                });

                // 长期趋势图
                const trendChart = echarts.init(document.getElementById('trendChart'));
                
                // 准备累计病例的历史和预测数据
                let trendSeries = [];
                
                // 历史和预测的累计数据
                let historyCumulativeDates = [];
                let historyCumulativeData = [];
                let predictionCumulativeDates = data.dates;
                let predictionCumulativeData = data.cumulative;
                
                // 如果有历史数据，添加历史累计病例系列
                if (data.historical_data && data.historical_data.dates && data.historical_data.cumulative) {
                    historyCumulativeDates = data.historical_data.dates;
                    historyCumulativeData = data.historical_data.cumulative;
                    
                    trendSeries.push({
                        name: '历史累计病例',
                        type: 'line',
                        data: historyCumulativeData.map((value, index) => [historyCumulativeDates[index], value]),
                        smooth: true,
                        symbolSize: 8,
                        itemStyle: {
                            color: '#3498db' // 蓝色表示历史数据
                        },
                        lineStyle: {
                            width: 3
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        }
                    });
                    
                    // 如果有历史数据，预测的累计数据应该从历史数据的最后一个点开始
                    if (historyCumulativeData.length > 0) {
                        const lastHistoricalValue = historyCumulativeData[historyCumulativeData.length - 1];
                        // 调整预测累计数据，使其从历史数据的最后一个点开始
                        predictionCumulativeData = predictionCumulativeData.map((value, index) => {
                            return lastHistoricalValue + value;
                        });
                    }
                }
                
                // 添加预测累计病例系列
                trendSeries.push({
                    name: '预测累计病例',
                    type: 'line',
                    data: predictionCumulativeData.map((value, index) => [predictionCumulativeDates[index], value]),
                    smooth: true,
                    symbolSize: 8,
                    itemStyle: {
                        color: '#e74c3c' // 红色表示预测数据
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    areaStyle: {
                        opacity: 0.3,
                        color: '#e74c3c'
                    }
                });
                
                // 添加连接历史和预测的虚线（长期趋势图）
                if (historyCumulativeDates.length > 0 && historyCumulativeData.length > 0 && predictionCumulativeDates.length > 0 && predictionCumulativeData.length > 0) {
                    trendSeries.push({
                        name: '连接线',
                        type: 'line',
                        data: [
                            [historyCumulativeDates[historyCumulativeDates.length - 1], historyCumulativeData[historyCumulativeData.length - 1]],
                            [predictionCumulativeDates[0], predictionCumulativeData[0]]
                        ],
                        symbol: 'none',
                        lineStyle: {
                            type: 'dashed',
                            color: 'grey',
                            width: 1
                        },
                        tooltip: { show: false },
                        legendHoverLink: false,
                        silent: true // 使其不响应鼠标事件
                    });
                }
                
                // 合并日期数据（历史+预测）
                let allTrendDates = [];
                if (historyCumulativeDates.length > 0) {
                    allTrendDates = [...historyCumulativeDates, ...predictionCumulativeDates];
                } else {
                    allTrendDates = predictionCumulativeDates;
                }
                
                trendChart.setOption({
                    title: { text: '长期趋势' },
                    tooltip: { 
                        trigger: 'axis',
                        formatter: function(params) {
                            let result = params[0].name + '<br/>';
                            params.forEach(param => {
                                result += param.seriesName + ': ' + formatNumber(param.value[1]) + '<br/>';
                            });
                            return result;
                        }
                    },
                    legend: {
                        data: ['历史累计病例', '预测累计病例'],
                        top: 25
                    },
                    xAxis: { 
                        type: 'category',
                        axisLabel: {
                            formatter: function(value) {
                                // 简化日期显示
                                return value.substring(5); // 只显示月-日
                            }
                        }
                    },
                    yAxis: { 
                        type: 'value',
                        axisLabel: {
                            formatter: function(value) {
                                return formatNumber(value);
                            }
                        }
                    },
                    series: trendSeries
                });

                // 疫情阶段图
                const phasesChart = echarts.init(document.getElementById('phasesChart'));
                
                // 只显示预测数据的疫情阶段图
                let phasesDates = data.dates;
                
                // 准备柱状图数据，只显示预测数据
                let barData = [];
                
                // 添加预测数据
                for (let i = 0; i < data.phases.length; i++) {
                    barData.push({
                        value: [phasesDates[i], data.growth_rates[i]],
                        itemStyle: {
                            color: data.phases[i] === '增长期' ? '#ff4d4f' : 
                                  data.phases[i] === '平稳期' ? '#d9d9d9' : '#52c41a'
                        }
                    });
                }
                
                phasesChart.setOption({
                    title: { text: '疫情阶段（预测数据）' },
                    tooltip: { 
                        trigger: 'axis',
                        formatter: function(params) {
                            let result = params[0].name + '<br/>';
                            params.forEach(param => {
                                result += '预测增长率: ' + formatNumber(param.value[1]) + '<br/>';
                            });
                            return result;
                        }
                    },
                    xAxis: { 
                        type: 'category',
                        axisLabel: {
                            formatter: function(value) {
                                // 简化日期显示
                                return value.substring(5); // 只显示月-日
                            }
                        }
                    },
                    yAxis: { 
                        type: 'value',
                        axisLabel: {
                            formatter: function(value) {
                                return formatNumber(value);
                            }
                        }
                    },
                    series: [{
                        name: '增长率',
                        type: 'bar',
                        data: barData
                    }]
                });
            }

            // 监听窗口大小变化，调整图表大小
            window.addEventListener('resize', function() {
                echarts.init(document.getElementById('predictionChart')).resize();
                echarts.init(document.getElementById('seirChart')).resize();
                echarts.init(document.getElementById('trendChart')).resize();
                echarts.init(document.getElementById('phasesChart')).resize();
            });
        });
    </script>
</body>
</html>
