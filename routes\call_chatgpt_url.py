import json
import os
from openai import OpenAI

def process_data(news_contents, prompt_text):
    ai_text = ''
    
    try:
        for news_content in news_contents:
            ai_text += call_ai(news_content, prompt_text)
        
        return ai_text
    except Exception as e:
        print(f"Failed to process data: {e}")
        return f"Error: {str(e)}"

def call_ai(news_content, prompt_text):
    config = load_api_config()
    
    try:
        extracted_content = extract_news_content_claude(news_content, prompt_text, config)
        result_text = f"AI摘取信息：\n\n{'*' * 50}\n\n{extracted_content}\n\n{'*' * 50}\n\n"
        return result_text
    except Exception as e:
        return f"Error processing news with AI (ChatGPT 4o): {e}\n\n{'*' * 50}\n\n"

def extract_news_content_claude(news_content, prompt_text, config):
    try:
        client = OpenAI(base_url=config['api_endpoint'], api_key=config['api_key'])
        response = client.chat.completions.create(
            #model="claude-3-5-sonnet-20240620",
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": prompt_text},
                {"role": "user", "content": news_content}
            ]
        )
        extracted_content = response.choices[0].message.content
        return extracted_content
    except Exception as e:
        print("Error occurred: ", e)
        raise

def load_api_config():
    config_details = {}
    current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    config_path = os.path.join(current_directory, 'api_config.json')
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8-sig') as json_file:
            config = json.load(json_file)
            config_details['api_endpoint'] = config.get('api_endpoint', '')
            config_details['api_key'] = config.get('api_key', '')
    return config_details