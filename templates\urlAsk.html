<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>AI搜索</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/jquery-ui.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/app.css') }}">
    <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
    <script src="{{ url_for('static', filename='js/jquery-ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>

    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            font-family: Arial, sans-serif;
        }

        .container {
            display: flex;
            height: 100%;
            overflow: hidden;
            gap: 10px;
        }

        .sidebar {
            width: 200px;
            background-color: #2c3e50;
            color: white;
            padding: 20px 0;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
            position: relative;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            z-index: 100;
        }

        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar h2 {
            margin: 0;
            padding: 0 20px 20px;
            font-size: 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar.collapsed h2 {
            font-size: 0;
            padding: 0;
            margin: 0;
            height: 0;
            border: none;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
            flex-grow: 1;
        }

        .sidebar a {
            display: flex;
            align-items: center;
            color: #ecf0f1;
            text-decoration: none;
            padding: 12px 20px;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar a:hover {
            background-color: #34495e;
            border-left-color: #3498db;
        }

        .sidebar a i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed a {
            padding: 12px 18px;
            justify-content: center;
        }

        .sidebar.collapsed a span {
            display: none;
        }

        .sidebar.collapsed a i {
            margin: 0;
            font-size: 1.2em;
        }

        .hamburger {
            position: absolute;
            top: 20px;
            right: -12px;
            width: 24px;
            height: 24px;
            background-color: #2c3e50;
            border: none;
            border-radius: 50%;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 101;
        }

        .hamburger:hover {
            background-color: #34495e;
            transform: scale(1.1);
        }

        .hamburger i {
            font-size: 14px;
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .hamburger i {
            transform: rotate(180deg);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
            margin-left: 0;
            transition: margin-left 0.3s ease;
            padding-left: 0;
            padding-right: 10px;
        }

        .search-area {
            padding: 10px;
            background-color: #f9f9f9;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .search-container {
            width: 40%;
            position: relative;
        }

        .ui.left.icon.action.input {
            width: 100%;
        }

        .search-results {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: 1fr;
            gap: 10px;
            padding: 10px;
            height: calc(100vh - 80px);
            overflow: hidden;
        }

        .result-card {
            min-width: 0;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            position: relative;
            margin: 0;
        }

        .result-card.maximized {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            max-width: none;
            z-index: 1000;
            margin: 0;
            border-radius: 0;
        }

        .result-header {
            padding: 10px;
            background-color: #f5f5f5;
            border-bottom: 1px solid #ddd;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .maximize-btn {
            cursor: pointer;
            padding: 5px;
            background: none;
            border: none;
            font-size: 1.2em;
        }

        .maximize-btn:hover {
            color: #1a73e8;
        }

        .result-content {
            flex: 1;
            position: relative;
            height: 100%;
        }

        .result-frame {
            width: 100%;
            height: 100%;
            border: none;
            position: absolute;
            top: 0;
            left: 0;
        }

        .sidebar.collapsed+.main-content {
            margin-left: 0;
        }

        @media (max-width: 768px) {
            .search-container {
                width: 90%;
            }

            .search-results {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, 1fr);
            }
        }

        @media (max-width: 1200px) {
            .search-results {
                grid-template-columns: repeat(2, 1fr);
                grid-template-rows: repeat(2, 1fr);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="sidebar" id="sidebar">
            <button class="hamburger" onclick="toggleSidebar()">
                <i class="angle left icon"></i>
            </button>
            <h2>AI导航</h2>
            <div class="sidebar-menu">
                <a href="https://www.tiangong.cn/" target="_blank">
                    <i class="chat icon"></i>
                    <span>天工AI</span>
                </a>
                <a href="https://kimi.moonshot.cn/" target="_blank">
                    <i class="chat icon"></i>
                    <span>KIMI</span>
                </a>
                <a href="https://www.perplexity.ai/" target="_blank">
                    <i class="chat icon"></i>
                    <span>Perplexity</span>
                </a>
                <a href="https://www.so.com/" target="_blank">
                    <i class="chat icon"></i>
                    <span>360AI问答</span>
                </a>
                <a href="https://chat.baidu.com/" target="_blank">
                    <i class="chat icon"></i>
                    <span>百度AI助手</span>
                </a>
                <a href="https://chat.deepseek.com/" target="_blank">
                    <i class="chat icon"></i>
                    <span>DeepSeek</span>
                </a>
                <a href="https://chat.openai.com/" target="_blank">
                    <i class="chat icon"></i>
                    <span>ChatGPT</span>
                </a>
            </div>
        </div>

        <div class="main-content">
            <div class="search-area">
                <div class="search-container">
                    <div class="ui left icon action input">
                        <i class="search icon"></i>
                        <input type="text" id="searchInput" placeholder="请输入关键词...">
                        <button class="ui primary button" id="searchButton">协同搜索</button>
                    </div>
                    <div class="error-message" id="errorMessage"></div>
                </div>
            </div>

            <div class="search-results" id="searchResults">
                <div class="result-card">
                    <div class="result-header">
                        <span>OpenAI</span>
                        <button class="maximize-btn" onclick="toggleMaximize(this)">
                            <i class="expand icon"></i>
                        </button>
                    </div>
                    <div class="result-content">
                        <iframe id="frame1" class="result-frame" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="result-card">
                    <div class="result-header">
                        <span>书生大模型</span>
                        <button class="maximize-btn" onclick="toggleMaximize(this)">
                            <i class="expand icon"></i>
                        </button>
                    </div>
                    <div class="result-content">
                        <iframe id="frame2" class="result-frame" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="result-card">
                    <div class="result-header">
                        <span>千问大模型</span>
                        <button class="maximize-btn" onclick="toggleMaximize(this)">
                            <i class="expand icon"></i>
                        </button>
                    </div>
                    <div class="result-content">
                        <iframe id="frame3" class="result-frame" src="about:blank"></iframe>
                    </div>
                </div>
                <div class="result-card">
                    <div class="result-header">
                        <span>Claude AI</span>
                        <button class="maximize-btn" onclick="toggleMaximize(this)">
                            <i class="expand icon"></i>
                        </button>
                    </div>
                    <div class="result-content">
                        <iframe id="frame4" class="result-frame" src="about:blank"></iframe>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class SearchManager {
            constructor() {
                this.searchInput = document.getElementById('searchInput');
                this.searchButton = document.getElementById('searchButton');
                this.errorMessage = document.getElementById('errorMessage');
                this.frames = {
                    frame1: document.getElementById('frame1'),
                    frame2: document.getElementById('frame2'),
                    frame3: document.getElementById('frame3'),
                    frame4: document.getElementById('frame4')
                };

                this.endpoints = {
                    frame1: 'http://*************:3001',
                    frame2: 'http://*************:3002',
                    frame3: 'http://*************:3003',
                    frame4: 'http://*************:3004'
                };

                this.initializeEventListeners();
            }

            initializeEventListeners() {
                this.searchButton.addEventListener('click', () => this.performSearch());
                this.searchInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.performSearch();
                    }
                });

                // Add input event listener to clear error state when user starts typing
                this.searchInput.addEventListener('input', () => {
                    if (this.searchInput.value.trim()) {
                        this.hideError();
                    }
                });

                // 监听iframe加载完成事件
                Object.values(this.frames).forEach(frame => {
                    frame.addEventListener('load', () => this.handleFrameLoad());
                });
            }

            showLoading() {
                this.searchButton.classList.add('loading');
                this.searchButton.disabled = true;
            }

            hideLoading() {
                this.searchButton.classList.remove('loading');
                this.searchButton.disabled = false;
            }

            showError(message) {
                const inputContainer = this.searchInput.closest('.ui.input');
                inputContainer.classList.add('error');
                this.searchInput.setAttribute('placeholder', '请输入需要搜索的问题');
            }

            hideError() {
                const inputContainer = this.searchInput.closest('.ui.input');
                inputContainer.classList.remove('error');
                this.searchInput.setAttribute('placeholder', '请输入关键词...');
            }

            validateInput() {
                const query = this.searchInput.value.trim();
                if (!query) {
                    this.showError();
                    return false;
                }
                this.hideError();
                return true;
            }

            handleFrameLoad() {
                // 可以在这里添加每个iframe加载完成后的处理逻辑
            }

            performSearch() {
                if (!this.validateInput()) return;

                this.showLoading();
                const query = encodeURIComponent(this.searchInput.value.trim());

                // 更新所有iframe的URL
                Object.entries(this.frames).forEach(([key, frame]) => {
                    const baseUrl = this.endpoints[key];
                    frame.src = `${baseUrl}/search?q=${query}`;
                });

                // 缩短超时时间
                setTimeout(() => {
                    this.hideLoading();
                }, 500);
            }
        }

        // 初始化搜索管理器
        document.addEventListener('DOMContentLoaded', () => {
            window.searchManager = new SearchManager();
        });

        // 侧边栏切换功能
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');
        }

        // 最大化/最小化切换功能
        function toggleMaximize(button) {
            const card = button.closest('.result-card');
            const icon = button.querySelector('i');

            card.classList.toggle('maximized');

            if (card.classList.contains('maximized')) {
                icon.classList.remove('expand');
                icon.classList.add('compress');
                document.body.style.overflow = 'hidden';
            } else {
                icon.classList.remove('compress');
                icon.classList.add('expand');
                document.body.style.overflow = '';
            }
        }
    </script>
</body>

</html>