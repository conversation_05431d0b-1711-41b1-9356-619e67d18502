from flask import jsonify, request, render_template, session
import routes.get_news as get_news
import routes.call_chatgpt_url as call_chatgpt_url
import routes.call_chatgpt as call_chatgpt

def handler():
    if request.method == 'POST':
        urls = request.json.get('urls', [])
        prompt_text = request.json.get('prompt_text', '')
                
        # 获取新闻内容
        news_contents = get_news.fetch_news(urls)
        
        # 拼接新闻内容
        processed_urls = call_chatgpt_url.process_data(news_contents, prompt_text)
        
        return jsonify({'processed_urls': processed_urls})
    else:
        config = call_chatgpt.load_api_config()
        prompt_text = config.get('prompt_text', '提示词为空，请重新设定！')  
        user_name = session.get('user_name', '游客')
        login_date = session.get('login_date', '')
        return render_template('urlAI.html', prompt_text=prompt_text, user_name=user_name, login_date=login_date) 