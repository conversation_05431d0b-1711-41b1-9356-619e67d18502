#!/usr/bin/env python3
"""
测试完整的AI分析接口
"""

import sys
import os
import json

sys.path.append(os.path.dirname(os.path.abspath(__file__)))


# 模拟Flask请求对象
class MockRequest:
    def __init__(self, json_data):
        self._json_data = json_data

    def get_json(self):
        return self._json_data


def test_full_analysis():
    """测试完整的AI分析接口"""

    # 使用您提供的25年7月份报告内容作为测试数据
    test_pdf_content = """
2025 年 7 月中国需关注的突发公共卫生事件风险评估
摘要：目的 评估2025年7月在我国（不含香港、澳门特别行政区和台湾地区，
下同）发生或者可能由境外输入的突发公共卫生事件风险。 方法 根据国内外
突发公共卫生事件报告及重点传染病监测等各种资料和部门通报信息，采用专
家会商法，并通过视频会议形式邀请各省（自治区、直辖市）疾病预防控制中
心专家参与评估。 结果 2025年7月突发公共卫生事件数可能比6月有所减少，
以传染病类事件为主。 登革热境外输入及跨地区传播风险上升，Ⅰ类地区及部
分Ⅱ类地区发生聚集性疫情的风险较高；基孔肯雅热等其他蚊媒传染病的输入
和本土传播风险上升。 预计7月仍将出现霍乱散发病例。发热伴血小板减少综
合征病例以散发为主，人传人导致的聚集性疫情风险也较高。 7月仍为食物中
毒事件高发期，毒蘑菇、有毒动植物和微生物污染是主要风险。 7－8月高温中
暑发病存在上升风险，男性、婴幼儿和老年人是中暑的高风险人群。 新型冠状
病毒感染疫情将继续呈现下降或波动下降趋势。 我国持续存在发生人感染禽流
感散发疫情的可能。 猴痘Ⅱb亚分支疫情仍将保持低水平波动态势，Ⅰa、Ⅰb
亚分支病例存在输入及续发传播风险。 7月我国全面进入主汛期，洪涝灾害受
影响地区存在水源性、食源性疾病以及虫媒传染病发病上升风险。 结论 对登
革热、霍乱、发热伴血小板减少综合征、食物中毒、高温中暑、新型冠状病毒
感染、人感染禽流感、猴痘及洪涝灾害等予以关注。 
关键词 突发公共卫生事件; 传染病疫情; 风险评估

2.1.1 重点关注
2.1.1.1 登革热
2025 年截至 7 月 1 日，全国报告病例数较 2024 年同期升高 41.8%，较 2019
年同期降低 63.5%。病例分布在 23 个省份，其中 13.9%为本地病例，由广东、云
南、湖南和浙江省报告，以登革病毒 1 型为主。境外输入病例主要来自印度尼西
亚、老挝、柬埔寨等东南亚国家，登革病毒 1～4 型均有报告，以 1 型和 2 型为
主。93.4%的病例为 20～64 岁人群，男女性别比为 2.5∶1，职业分布以商业服
务、农民、家务及待业和工人为主。

2.1.1.2 霍乱
2025 年截至 6 月 30 日，全国报告确诊病例 4 例、病原携带者 1 例，2024 年
同期无病例报告。疫情均由食源性传播引起。

2.1.1.3 发热伴血小板减少综合征
2025 年截至 7 月 2 日，全国报告病例数及死亡人数分别较 2024 年同期下降
12.3%及 35.3%。病例仍以散发为主，分布在 18 个省份，97.0%的病例分布在安
徽、湖北、河南、山东、江苏、浙江和辽宁这 7 个既往高发省份。

2.1.1.4 食物中毒
2025年1－6月，全国共报告食物中毒事件62起，较2024年同期增加31.9%。
其中，6 月报告 21 起，占 2025 年上半年报告总数的 33.9%，较 2024 年同期增加
90.9%。有毒动植物及毒蘑菇引起的食物中毒事件 12 起，致病因子为乌头、毒蘑
菇等；微生物性食物中毒 4 起，致病因子为产气荚膜梭菌、副溶血弧菌、致泻性
大肠埃希菌等。

2.1.1.5 高温中暑
2025 年 1－6 月，全国累计报告高温中暑病例数及死亡人数分别较 2024 年
同期增加 30.1%和 8.3%。其中，6 月报告病例数占 2025 年上半年报告总数的
75.3%，较 2024 年同期增加 24.1%，死亡人数较 2024 年同期减少 30.0%。

2.1.2 一般关注
2.1.2.1 新型冠状病毒感染
全国报告病例数自 2025 年第 7 周开始持续回升，于第 21 周达到阶段高点，
当前各省报告病例数均下降或波动下降。2025 年 6 月，全国 31 个省（自治区、
直辖市）及新疆生产建设兵团发热门诊（诊室）每日诊疗量在 5.3 万～7.2 万人
次之间波动。

2.1.2.2 人感染禽流感
2025 年 6 月，全国新增 2 例人感染 H9N2 禽流感病例，分布在广东和湖北
省。2 例病例发病前均有明确的禽相关暴露史。

2.1.2.3 猴痘
2025 年 6 月报告病例 110 例，较 5 月有所减少。101 例属Ⅱb 亚分支感染，
均为男性，主要通过男男性行为接触感染；9 例属Ⅰb 亚分支感染，5 例男性，4
例女性，涉及 3 起境外输入病例引发的聚集性疫情，通过亲密接触感染，其中 8
例为外籍。

2.1.2.4 洪涝灾害
7 月我国全面进入主汛期，预计降雨呈"南北多、中间少"分布，北方部分
地区洪涝和风雹灾害风险高，黄河中下游、海河流域、松辽流域部分河流可能发
生暴雨洪水；可能有 2～3 个台风登陆或影响我国，1 个较强台风影响北方地区。
洪涝灾害受影响地区存在水源性、食源性疾病以及虫媒传染病发病上升风险。
"""

    print("=" * 60)
    print("测试完整的AI分析接口")
    print("=" * 60)

    # 模拟请求数据
    request_data = {
        "pdf_text": test_pdf_content,  # 直接提供文本内容，避免网络问题
        "pdf_url": "https://www.chinacdc.cn/jksj/jksj02/202507/U020250731478621084646.pdf",
    }

    print("1. 准备测试数据...")
    print(f"   文本长度: {len(test_pdf_content)} 字符")
    print(f"   PDF URL: {request_data['pdf_url']}")

    # 模拟Flask请求
    import routes.cdc_report_ai_analysis as analysis_module

    # 由于我们无法直接调用Flask路由，我们测试核心逻辑
    print("\n2. 测试AI分析逻辑...")

    try:
        # 测试AI分析调用
        print("   正在调用AI分析...")
        ai_result = analysis_module.call_ai_analysis(test_pdf_content)
        print(f"   AI调用结果: {ai_result}")

        if ai_result.get("success"):
            print("   AI分析调用成功!")
            print(f"   使用模型: {ai_result.get('model_used', 'unknown')}")
            print(f"   响应长度: {len(ai_result['content'])} 字符")

            # 测试结果格式化
            from routes.cdc_report_analyzer import (
                format_analysis_result,
                generate_html_report,
            )

            formatted_result = format_analysis_result(ai_result["content"])

            if formatted_result.get("success"):
                print("   结果格式化成功!")

                # 生成HTML报告
                html_report = generate_html_report(formatted_result)

                if html_report:
                    print("   HTML报告生成成功!")

                    # 保存完整的分析结果
                    with open("full_analysis_result.html", "w", encoding="utf-8") as f:
                        f.write(html_report)
                    print("   完整分析结果已保存到 full_analysis_result.html")

                    # 保存JSON数据
                    with open("analysis_data.json", "w", encoding="utf-8") as f:
                        json.dump(formatted_result, f, ensure_ascii=False, indent=2)
                    print("   分析数据已保存到 analysis_data.json")

                else:
                    print("   HTML报告生成失败")
            else:
                print(
                    f"   结果格式化失败: {formatted_result.get('error', 'Unknown error')}"
                )
                print(f"   原始AI响应: {ai_result['content'][:500]}...")
        else:
            print(f"   AI分析调用失败: {ai_result.get('error', 'Unknown error')}")

    except Exception as e:
        print(f"   测试过程中发生异常: {str(e)}")
        import traceback

        traceback.print_exc()

    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)


if __name__ == "__main__":
    test_full_analysis()
