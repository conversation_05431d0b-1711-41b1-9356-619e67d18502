<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>AI智能系统 - 登录</title>

    <!-- Google Font: Source Sans Pro -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.7.0/css/all.min.css">
    <!-- Bootstrap 4 -->
    <link rel="stylesheet" href="https://unpkg.com/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <!-- AdminLTE -->
    <link rel="stylesheet" href="https://unpkg.com/admin-lte@3.2.0/dist/css/adminlte.min.css">
    
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Source Sans Pro', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* 动态背景粒子效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            animation: float 6s ease-in-out infinite;
            z-index: 1;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .login-container {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 450px;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            animation: slideUp 0.8s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 30px 60px rgba(0, 0, 0, 0.15),
                0 0 0 1px rgba(255, 255, 255, 0.3);
        }

        .brand-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .brand-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .brand-logo i {
            font-size: 2.5rem;
            color: white;
        }

        .brand-title {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #2c3e50, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand-subtitle {
            color: #7f8c8d;
            font-size: 1rem;
            margin-bottom: 0;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-control {
            height: 55px;
            border: 2px solid #e8ecf0;
            border-radius: 12px;
            padding: 15px 20px 15px 55px;
            font-size: 1rem;
            background: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
            background: white;
            outline: none;
        }

        .form-control::placeholder {
            color: #95a5a6;
            font-weight: 400;
        }

        .input-icon {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #95a5a6;
            font-size: 1.1rem;
            z-index: 3;
            transition: color 0.3s ease;
        }

        .form-group:focus-within .input-icon {
            color: #3498db;
        }

        .login-btn {
            width: 100%;
            height: 55px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
            background: linear-gradient(135deg, #2980b9, #3498db);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .form-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 25px 0;
            flex-wrap: wrap;
            gap: 15px;
        }

        .checkbox-group {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }

        .custom-checkbox {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            user-select: none;
        }

        .custom-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3498db;
            cursor: pointer;
        }

        .custom-checkbox label {
            color: #7f8c8d;
            font-size: 0.9rem;
            cursor: pointer;
            margin: 0;
        }

        .forgot-password {
            color: #3498db;
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #2980b9;
            text-decoration: none;
        }

        .flash-messages {
            margin-bottom: 20px;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 15px 20px;
            font-size: 0.9rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .alert-success {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
            color: white;
        }

        .loading-spinner {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-container {
                max-width: 90%;
                padding: 15px;
            }
            
            .login-card {
                padding: 30px 25px;
            }
            
            .brand-title {
                font-size: 1.5rem;
            }
            
            .form-options {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
            
            .checkbox-group {
                width: 100%;
                justify-content: space-between;
            }
        }

        @media (max-width: 480px) {
            .login-card {
                padding: 25px 20px;
            }
            
            .brand-logo {
                width: 60px;
                height: 60px;
            }
            
            .brand-logo i {
                font-size: 2rem;
            }
            
            .checkbox-group {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            .login-card {
                background: rgba(44, 62, 80, 0.95);
                color: #ecf0f1;
            }
            
            .brand-title {
                color: #ecf0f1;
            }
            
            .form-control {
                background: rgba(52, 73, 94, 0.8);
                border-color: #34495e;
                color: #ecf0f1;
            }
            
            .form-control::placeholder {
                color: #95a5a6;
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <div class="login-card">
            <!-- 品牌区域 -->
            <div class="brand-section">
                <div class="brand-logo">
                    <i class="fa-solid fa-hexagon-nodes"></i>
                </div>
                <h1 class="brand-title">AI智能系统</h1>
                <p class="brand-subtitle">数据分析可视化 · 一站式解决方案</p>
            </div>

            <!-- 登录表单 -->
            <form id="loginForm" action="/login" method="post">
                <div id="flash-messages" class="flash-messages"></div>
                
                <div class="form-group">
                    <i class="fas fa-user input-icon"></i>
                    <input type="text" class="form-control" id="inputUsername" name="username" placeholder="请输入用户名" required>
                </div>
                
                <div class="form-group">
                    <i class="fas fa-lock input-icon"></i>
                    <input type="password" class="form-control" id="inputPassword" name="password" placeholder="请输入密码" required>
                </div>

                <div class="form-options">
                    <div class="checkbox-group">
                        <div class="custom-checkbox">
                            <input type="checkbox" id="rememberMe">
                            <label for="rememberMe">记住密码</label>
                        </div>
                        <div class="custom-checkbox">
                            <input type="checkbox" id="keepLogin">
                            <label for="keepLogin">保持登录</label>
                        </div>
                    </div>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <div class="loading-spinner" id="loadingSpinner"></div>
                    <span id="btnText">登录系统</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Bootstrap 4 -->
    <script src="https://unpkg.com/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var loginForm = document.getElementById('loginForm');
            var loginBtn = document.getElementById('loginBtn');
            var loadingSpinner = document.getElementById('loadingSpinner');
            var btnText = document.getElementById('btnText');
            var usernameInput = document.getElementById('inputUsername');
            var passwordInput = document.getElementById('inputPassword');
            var rememberMeCheckbox = document.getElementById('rememberMe');
            var keepLoginCheckbox = document.getElementById('keepLogin');

            // 页面加载时检查是否有保存的用户名和密码
            if (localStorage.getItem('rememberedUser')) {
                var userData = JSON.parse(localStorage.getItem('rememberedUser'));
                usernameInput.value = userData.username || '';
                passwordInput.value = userData.password || '';
                rememberMeCheckbox.checked = true;
            }

            // 表单提交处理
            loginForm.addEventListener('submit', function (event) {
                event.preventDefault();
                
                var username = usernameInput.value.trim();
                var password = passwordInput.value.trim();

                if (!username || !password) {
                    displayFlashMessage('用户名和密码不能为空', 'danger');
                    return;
                }

                // 显示加载状态
                setLoadingState(true);

                // 处理"记住密码"功能
                if (rememberMeCheckbox.checked) {
                    localStorage.setItem('rememberedUser', JSON.stringify({
                        username: username,
                        password: password
                    }));
                } else {
                    localStorage.removeItem('rememberedUser');
                }

                $.ajax({
                    url: '/login',
                    type: 'POST',
                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                    data: {
                        username: username,
                        password: password,
                        keep_login: keepLoginCheckbox.checked
                    },
                    dataType: 'json',
                    success: function (data) {
                        setLoadingState(false);
                        if (data.loginSuccess) {
                            displayFlashMessage('登录成功，正在跳转...', 'success');
                            setTimeout(function() {
                                if (data.redirect) {
                                    window.location.href = data.redirect;
                                } else {
                                    window.location.href = "/";
                                }
                            }, 1000);
                        } else {
                            displayFlashMessage(data.message, data.category);
                        }
                    },
                    error: function (xhr) {
                        setLoadingState(false);
                        var errorData = xhr.responseJSON;
                        displayFlashMessage(errorData.message || '登录失败，请重试', 'danger');
                    }
                });
            });

            // 设置加载状态
            function setLoadingState(loading) {
                if (loading) {
                    loadingSpinner.style.display = 'inline-block';
                    btnText.textContent = '登录中...';
                    loginBtn.disabled = true;
                    loginBtn.style.opacity = '0.8';
                } else {
                    loadingSpinner.style.display = 'none';
                    btnText.textContent = '登录系统';
                    loginBtn.disabled = false;
                    loginBtn.style.opacity = '1';
                }
            }

            // 输入框焦点效果
            var inputs = document.querySelectorAll('.form-control');
            inputs.forEach(function(input) {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('focused');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('focused');
                });
            });
        });

        $(document).ready(function () {
            // 尝试从 sessionStorage 中获取 flashMessage
            var flashMessageJSON = sessionStorage.getItem('flashMessage');
            if (flashMessageJSON) {
                var flashMessage = JSON.parse(flashMessageJSON);
                displayFlashMessage(flashMessage.message, flashMessage.category);
                sessionStorage.removeItem('flashMessage');
            }
        });

        function displayFlashMessage(message, category) {
            category = category || 'danger';
            var flashMessagesDiv = $('#flash-messages');
            if (message) {
                flashMessagesDiv.html(`<div class="alert alert-${category}" role="alert">${message}</div>`);
                
                // 自动隐藏成功消息
                if (category === 'success') {
                    setTimeout(function() {
                        flashMessagesDiv.fadeOut();
                    }, 3000);
                }
            }
        }
    </script>
</body>

</html>