from flask import jsonify, request, g 
from collections import deque
from model.models import db, Disease, Disaster, DisasterChina
from model.disease_knowledge import DiseaseRelation, DiseaseAttribute
from sqlalchemy import func, and_, text
from datetime import datetime, timedelta
import numpy as np
from scipy import stats

# For simplicity in this example, let's make a module-level queue that the SSE handler will read from.
# A better approach for production would be a proper message queue or a shared object managed by the app context.

# Let's define a way to send progress updates.
# We'll create a simple list to act as a message queue for progress updates for now.
# A more robust solution would use a proper message queue (e.g., Redis pub/sub or RabbitMQ)
# or Flask-SSE extensions.
progress_messages = deque()

def send_progress_update(status, progress_percent):
    """Helper function to format and send progress updates."""
    message = f"data: {{ \"status\": \"{status}\", \"progress\": {progress_percent} }}\n\n"
    progress_messages.append(message)
    # For immediate feedback in console during development of this part
    print(f"Progress Update: {status} - {progress_percent}%")

def calculate_disease_relations(correlation_threshold=0.3, min_common_dates=3, aggregate_method='weekly'):
    """计算疾病之间的关系
    
    Args:
        correlation_threshold: 相关系数阈值，默认0.3
        min_common_dates: 最小共同日期数，默认3
        aggregate_method: 时间序列聚合方法，可选'daily','weekly','monthly'
    """
    send_progress_update("开始基于全部历史数据计算疾病关系...", 0)
    # 获取所有疾病
    try:
        send_progress_update("正在查询Disease表...", 5)
        diseases = Disease.query.all()
        if not diseases:
            send_progress_update("警告：Disease表为空！计算中止。", 100)
            print("警告：Disease表为空！")
            return []
        send_progress_update(f"找到{len(diseases)}种疾病，准备分析...", 10)
    except Exception as e:
        error_msg = f"获取疾病数据出错：{str(e)}"
        print(error_msg)
        send_progress_update(error_msg, 100)
        return []
    
    relations = []
    total_diseases = len(diseases)
    if total_diseases < 2:
        send_progress_update("疾病数量不足以计算关系。", 100)
        return []

    total_pairs = total_diseases * (total_diseases - 1) // 2
    send_progress_update(f"共需计算 {total_pairs} 对疾病关系，使用{aggregate_method}聚合方式", 15)
    pair_count = 0
    
    # Base progress for setup
    base_progress = 15 
    # Remaining progress for actual calculation (e.g., 15% to 90%)
    calculation_progress_range = 75 

    for i, disease1 in enumerate(diseases):
        for j, disease2 in enumerate(diseases):
            if disease1.data_value >= disease2.data_value:
                continue
            
            pair_count += 1
            current_progress_percentage = base_progress + int((pair_count / total_pairs) * calculation_progress_range) if total_pairs > 0 else base_progress
            
            status_msg = f"分析中 ({pair_count}/{total_pairs}): {disease1.name} - {disease2.name}"
            send_progress_update(status_msg, current_progress_percentage)
            
            try:
                # 使用改进的时间序列获取方法，支持数据聚合
                series1 = get_disease_time_series(disease1.name, aggregate=aggregate_method)
                series2 = get_disease_time_series(disease2.name, aggregate=aggregate_method)
                
                # 检查是否有足够的数据点
                if len(series1) >= min_common_dates and len(series2) >= min_common_dates:
                    dates1 = set(date for date, _ in series1)
                    dates2 = set(date for date, _ in series2)
                    common_dates = sorted(list(dates1.intersection(dates2)))
                    
                    # 检查共同日期是否足够
                    if len(common_dates) >= min_common_dates:
                        # 按日期对齐数据
                        dict_series1 = dict(series1)
                        dict_series2 = dict(series2)
                        values1 = [dict_series1[date] for date in common_dates]
                        values2 = [dict_series2[date] for date in common_dates]
                        
                        # 计算相关系数和P值
                        correlation, p_value = stats.pearsonr(values1, values2)
                        
                        # 根据相关系数阈值过滤
                        if abs(correlation) > correlation_threshold and p_value < 0.05:  # 添加P值显著性检验
                            # 确定关系类型和强度
                            relation_strength = "强" if abs(correlation) > 0.7 else "中" if abs(correlation) > 0.5 else "弱"
                            relation_direction = "正相关" if correlation > 0 else "负相关"
                            relation_type = f"{relation_strength}{relation_direction}"
                            
                            send_progress_update(f"发现{relation_type}: {disease1.name} & {disease2.name} ({correlation:.2f})", current_progress_percentage)
                            
                            # 计算时间滞后相关性（简单版本）
                            lag_info = ""
                            if len(common_dates) >= 5:  # 只有足够多的数据点才计算滞后
                                try:
                                    # 尝试计算滞后1个时间单位的相关性
                                    values1_lag = values1[:-1]
                                    values2_lag = values2[1:]
                                    if len(values1_lag) >= 3:  # 确保有足够的数据点
                                        lag_corr, lag_p = stats.pearsonr(values1_lag, values2_lag)
                                        if abs(lag_corr) > abs(correlation) and lag_p < 0.05:
                                            lag_info = f"，存在时间滞后效应(滞后相关系数：{lag_corr:.2f})"
                                except Exception as lag_error:
                                    print(f"计算滞后相关性时出错: {str(lag_error)}")
                            
                            relations.append({
                                'source_disease': disease1.data_value,
                                'target_disease': disease2.data_value,
                                'relation_type': relation_type,
                                'confidence': float(abs(correlation)),
                                'evidence': f'基于{aggregate_method}聚合的{len(common_dates)}个共同时间点分析，相关系数：{correlation:.2f}, P值：{p_value:.3f}{lag_info}'
                            })
            except Exception as e:
                # Log error but continue
                print(f"处理疾病对 {disease1.name}-{disease2.name} 时出错: {str(e)}")
                send_progress_update(f"处理 {disease1.name}-{disease2.name} 时出错: {str(e)[:50]}...", current_progress_percentage) # Keep UI message brief
                continue
    
    send_progress_update("疾病关系计算完成。", base_progress + calculation_progress_range) # Should be 90%
    return relations

def get_disease_time_series(disease_name, days=None, aggregate='daily'):  # 移除默认的时间限制
    """获取疾病的时间序列数据
    
    Args:
        disease_name: 疾病名称
        days: 可选，限制查询的天数
        aggregate: 数据聚合方式，可选值：'daily'(按天),'weekly'(按周),'monthly'(按月)
    
    Returns:
        时间序列数据列表，格式为[(日期, 增长率)]
    """
    try:
        # 从疾病名称中提取英文名称 - 由于疾病名称唯一，可以直接使用完整名称
        # 但仍然保留拆分逻辑以处理可能带括号的名称
        english_name = disease_name.split('(')[0].strip().lower()
        
        # 如果指定了天数，则使用时间过滤
        if days:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            # 使用精确匹配而非模糊包含
            results = Disaster.query.filter(
                and_(
                    Disaster.time.between(start_date, end_date),
                    func.lower(Disaster.disaster) == english_name
                )
            ).order_by(Disaster.time).all()
        else:
            # 不限制时间，查询所有数据，使用精确匹配
            results = Disaster.query.filter(
                func.lower(Disaster.disaster) == english_name
            ).order_by(Disaster.time).all()
        
        # 过滤掉增长率为None的记录
        valid_results = [(r.time.date(), r.growth) for r in results if r.growth is not None]
        
        # 根据聚合方式处理数据
        if aggregate == 'daily' or len(valid_results) < 7:  # 数据点太少时默认使用每日数据
            return valid_results
        elif aggregate == 'weekly':
            return aggregate_time_series(valid_results, 'week')
        elif aggregate == 'monthly':
            return aggregate_time_series(valid_results, 'month')
        else:
            return valid_results  # 默认返回每日数据
            
    except Exception as e:
        print(f"  获取 {disease_name} 时间序列数据时出错: {str(e)}")
        return []

def aggregate_time_series(time_series, period_type):
    """聚合时间序列数据
    
    Args:
        time_series: 原始时间序列数据，格式为[(日期, 值)]
        period_type: 聚合周期类型，'week'或'month'
    
    Returns:
        聚合后的时间序列数据
    """
    if not time_series:
        return []
        
    # 按周期分组
    period_data = {}
    
    for date, value in time_series:
        if period_type == 'week':
            # 获取该日期所在周的周一作为键
            period_key = date - timedelta(days=date.weekday())
        elif period_type == 'month':
            # 获取该日期所在月的第一天作为键
            period_key = date.replace(day=1)
        else:
            period_key = date  # 默认不聚合
            
        if period_key not in period_data:
            period_data[period_key] = []
        period_data[period_key].append(value)
    
    # 计算每个周期的平均值
    aggregated_data = [(period, sum(values)/len(values)) for period, values in period_data.items()]
    
    # 按日期排序
    return sorted(aggregated_data, key=lambda x: x[0])

def handler():
    """知识图谱API处理函数"""
    # Clear any old messages from previous runs if the queue is module-level
    # This is a simplistic approach.
    global progress_messages
    
    print(f"收到{request.method}请求 for knowledge_graph")
    if request.method == 'GET':
        start_time = datetime.now()
        print("开始加载知识图谱数据...", start_time)
        
        # 优化查询 - 使用JOIN一次性获取所有疾病及其属性
        # 1. 获取所有疾病及其属性（使用LEFT OUTER JOIN避免丢失没有属性的疾病）
        diseases_with_attributes = db.session.query(Disease, DiseaseAttribute)\
            .outerjoin(DiseaseAttribute, Disease.data_value == DiseaseAttribute.disease_id)\
            .all()
        
        # 2. 获取所有关系数据（单次查询）
        relations = DiseaseRelation.query.all()
        
        # 处理疾病和属性数据
        disease_map = {}
        for disease, attribute in diseases_with_attributes:
            if disease.data_value not in disease_map:
                disease_map[disease.data_value] = {
                    'id': disease.data_value,
                    'name': disease.name,
                    'attributes': {}
                }
            if attribute:  # 属性可能为None（因为是outer join）
                disease_map[disease.data_value]['attributes'][attribute.attribute_key] = attribute.attribute_value
        
        # 构建节点列表
        nodes = list(disease_map.values())
        
        # 构建关系数据
        links = [{
            'source': relation.source_disease,
            'target': relation.target_disease,
            'type': relation.relation_type,
            'confidence': relation.confidence,
            'evidence': relation.evidence
        } for relation in relations]
        
        end_time = datetime.now()
        print(f"知识图谱数据加载完成，耗时: {(end_time - start_time).total_seconds()}秒")
        print(f"节点数量: {len(nodes)}, 关系数量: {len(links)}")
        
        return jsonify({
            'success': True,
            'data': {
                'nodes': nodes,
                'links': links
            }
        })
    
    elif request.method == 'POST':
        # 更新知识图谱数据
        progress_messages.clear() # Clear at the start of a new POST request
        send_progress_update("收到更新请求，正在初始化...", 0)
        try:
            # print("开始更新知识图谱数据...")
            # print("检查数据库连接...")
            # 测试数据库连接
            send_progress_update("正在连接数据库并准备清除旧数据...", 91) # After calculation
            db.session.execute(text('SELECT 1'))
            # print("数据库连接正常")
            
            # 清除旧的关系数据
            # print("正在查询现有关系数据...")
            old_count = DiseaseRelation.query.count()
            DiseaseRelation.query.delete()
            # print(f"已清除{old_count}条旧的关系数据")
            send_progress_update(f"已清除{old_count}条旧关系数据，将基于全部历史数据重新计算。", 92)
            
            # 计算新的关系
            # This call now populates 'progress_messages'
            # 使用改进的参数：降低相关系数阈值，减少最小共同日期要求，使用周聚合
            relations = calculate_disease_relations(correlation_threshold=0.3, min_common_dates=3, aggregate_method='weekly') 
            
            # 保存新的关系数据
            # print(f"开始保存{len(relations)}个新的关系数据...")
            send_progress_update(f"开始保存 {len(relations)} 个新关系...", 95)
            if not relations: # No relations to save
                send_progress_update("没有新的关系需要保存。", 99)
            else:
                for i, relation_data in enumerate(relations, 1):
                    new_relation = DiseaseRelation(**relation_data)
                    db.session.add(new_relation)
                    # if i % 10 == 0:  # 每10条打印一次进度
                    #     print(f"已处理: {i}/{len(relations)}")
                    if i % 10 == 0 or i == len(relations):
                        current_save_progress = 95 + int((i / len(relations)) * 4) if len(relations) > 0 else 95 # Save from 95 to 99%
                        send_progress_update(f"已保存: {i}/{len(relations)}", current_save_progress)
            
            db.session.commit()
            send_progress_update(f"成功更新了 {len(relations)} 个疾病关系。", 100)
            
            return jsonify({
                'success': True,
                'message': f'成功更新了{len(relations)}个疾病关系'
            })
            
        except Exception as e:
            error_msg = f'更新失败：{str(e)}'
            print(error_msg)
            try:
                db.session.rollback()
            except Exception as e2:
                print(f'回滚失败：{str(e2)}')
            send_progress_update(error_msg, 100) # Send error as final status
            return jsonify({
                'success': False,
                'message': error_msg
            }), 500
            
    return jsonify({'success': False, 'message': '不支持的请求方法'}), 405
