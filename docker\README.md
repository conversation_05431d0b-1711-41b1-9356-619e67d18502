# Docker 配置说明

本目录包含新闻网站应用的 Docker 相关配置文件。通过 Docker 可以快速部署和运行整个应用环境。

## 目录结构

```
docker/
├── Dockerfile          # 主应用容器配置文件
├── docker-compose.yml  # 容器编排配置文件
├── .dockerignore       # Docker 忽略文件配置
└── README.md          # 说明文档
```

## 配置文件说明

### 1. Dockerfile
主要配置内容：
- 基础镜像：Python 3.10.6-slim
- 系统依赖：
  - build-essential：编译工具
  - chromium：浏览器（用于 Selenium）
  - chromium-driver：浏览器驱动
  - libpq-dev：PostgreSQL 开发库
- 环境配置：
  - 时区设置为 Asia/Shanghai
  - Python 环境变量配置
- 端口：暴露 83 端口
- 启动命令：python app.py

### 2. docker-compose.yml
服务配置：
- web 服务：
  - 端口映射：83:83
  - 卷挂载：项目目录挂载到容器 /app
  - 环境变量：
    - FLASK_ENV=development
    - FLASK_APP=app.py
  - 自动重启策略：unless-stopped

数据库服务（默认注释）：
- MySQL 8.0
- 可配置数据库名称、用户名和密码
- 数据持久化存储

### 3. .dockerignore
忽略以下文件和目录：
- Git 相关文件
- Python 缓存和编译文件
- 虚拟环境目录
- IDE 配置文件
- 日志文件
- 环境配置文件
- Node.js 依赖

## 使用说明

### 环境要求
1. 安装 Docker（推荐 20.10.0 或更高版本）
2. 安装 Docker Compose（推荐 2.0.0 或更高版本）

### 构建和运行
1. 进入 docker 目录：
```bash
cd docker
```

2. 构建并启动容器：
```bash
# 前台运行（可以看到日志输出）
docker-compose up --build

# 后台运行
docker-compose up -d --build
```

3. 停止服务：
```bash
docker-compose down
```

### 注意事项
1. 首次运行前，请确保：
   - 已经安装了 Docker 和 Docker Compose
   - 83 端口未被占用
   - 如果使用数据库，已经正确配置数据库连接信息

2. 环境变量配置：
   - 创建 `.env` 文件存放敏感信息
   - 不要将 `.env` 文件提交到版本控制系统
   - 参考 `.env.example`（如果有）进行配置

3. 数据库配置：
   - 如需使用数据库，取消 docker-compose.yml 中数据库服务的注释
   - 修改数据库密码和用户名
   - 确保数据持久化目录有适当的权限

4. 开发建议：
   - 开发环境建议使用卷挂载，方便实时修改代码
   - 生产环境建议使用多阶段构建优化镜像大小
   - 定期清理未使用的镜像和容器

### 常见问题
1. 端口冲突：
   - 修改 docker-compose.yml 中的端口映射
   - 确保主机端口未被占用

2. 权限问题：
   - 确保数据卷目录有正确的权限
   - 容器内进程使用适当的用户运行

3. 内存问题：
   - 适当调整容器内存限制
   - 监控容器资源使用情况

## 维护和更新
- 定期更新基础镜像
- 检查并更新依赖包版本
- 保持安全补丁的及时更新
- 定期备份数据库和关键数据
