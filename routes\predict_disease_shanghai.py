from flask import jsonify, request
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pymysql
from statsmodels.tsa.statespace.sarimax import SARIMAX
from db.db_config import USERNAME, PASSWORD, HOST, PORT, DATABASE
import traceback
from routes.sarima_prediction_china import SARIMAPredictionChina
from scipy.integrate import odeint # Added for SEIR

# Definition of the SEIR Model
class SEIRModel:
    def __init__(self, beta, sigma, gamma, N):
        self.beta = beta
        self.sigma = sigma  # 1 / incubation period
        self.gamma = gamma  # 1 / duration of illness
        self.N = N          # Total population

    def model(self, y, t):
        S, E, I, R = y
        dSdt = -self.beta * S * I / self.N
        dEdt = self.beta * S * I / self.N - self.sigma * E
        dIdt = self.sigma * E - self.gamma * I
        dRdt = self.gamma * I
        return dSdt, dEdt, dIdt, dRdt

    def predict(self, S0, E0, I0, R0, t):
        y0 = S0, E0, I0, R0
        ret = odeint(self.model, y0, t)
        return ret.T

class SARIMAPredictionShanghai(SARIMAPredictionChina):
    """上海疾病预测类，继承中国疾病预测类的大部分逻辑，但修改数据源和字段映射
    
    数据库字段到UI显示字段的映射：
    - local_hukou (本市户籍人口增长数) -> local_hukou -> 户籍新增 -> 本市户籍(UI)
    - nonlocal_hukou (非本市户籍人口增长数) -> nonlocal_hukou -> 非户籍新增 -> 非本市户籍(UI)
    - total_hukou (总人口增长数) -> total_hukou -> 合计 -> 总(UI)
    """
    
    def __init__(self):
        super().__init__() # Assuming SARIMAPredictionChina might have an __init__
        # SEIR Model Parameters
        self.disease_params = {
            # beta: transmission rate, sigma: 1/incubation, gamma: 1/duration
            # Values are illustrative and should be calibrated
            'default': {'incubation': 5, 'duration': 10, 'beta': 0.35},
            '流感': {'incubation': 2, 'duration': 7, 'beta': 0.4, 'population_factor': 0.1}, # Example
            '手足口病': {'incubation': 4, 'duration': 7, 'beta': 0.3, 'population_factor': 0.05}, # Example
            # Add other diseases common in Shanghai
        }
        # Shanghai Population (approximate, can be made more dynamic)
        self.shanghai_population = 25000000 # Roughly 25 million

    def get_data_from_db(self, start_date=None, end_date=None, disease=None):
        """从数据库获取上海疾病数据，并进行字段映射
        local_hukou (本市户籍人口增长数)
        nonlocal_hukou (非本市户籍人口增长数)
        total_hukou (总人口增长数)
        """
        try:
            connection = pymysql.connect(
                host=HOST,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE,
                port=int(PORT)
            )
            
            query = """
                SELECT time, disaster, 
                       local_hukou, 
                       nonlocal_hukou, 
                       total_hukou 
                FROM t_disaster_shanghai 
                WHERE disaster = %s
                AND time BETWEEN %s AND %s
                AND sub_disaster=''
                ORDER BY time
            """
            
            with connection.cursor() as cursor:
                cursor.execute(query, (disease, start_date, end_date))
                columns = [col[0] for col in cursor.description]
                data = cursor.fetchall()
                
            df = pd.DataFrame(list(data), columns=columns)
            df['time'] = pd.to_datetime(df['time'])
            
            connection.close()
            return df
            
        except Exception as e:
            print(f"Error in get_data_from_db (Shanghai): {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise
    
    def get_seir_model_parameters(self, disease, historical_df):
        disease_key = disease if disease in self.disease_params else 'default'
        params = self.disease_params[disease_key].copy()

        # Basic beta estimation from recent data (optional, can be refined)
        if historical_df is not None and not historical_df.empty and 'total_hukou' in historical_df.columns and len(historical_df) > 1:
            # Use 'total_hukou' which represents new cases
            recent_cases = historical_df['total_hukou'].astype(float).dropna()
            if len(recent_cases) >= 2:
                avg_growth = recent_cases.pct_change().mean()
                # This is a very rough way to estimate beta factor, needs proper epidemiological calibration
                if pd.notna(avg_growth) and avg_growth > 0:
                     estimated_beta_factor = 1 + avg_growth 
                     # params['beta'] = max(0.1, min(params['beta'] * estimated_beta_factor, 0.9)) # Adjust beta based on trend
        
        return params['beta'], 1.0/params['incubation'], 1.0/params['duration'], params.get('population_factor', 1.0)

    def get_seir_prediction(self, disease, start_date, end_date, prediction_months=6):
        try:
            historical_df = self.get_data_from_db(start_date, end_date, disease)
            if historical_df.empty or 'total_hukou' not in historical_df.columns:
                return None, "insufficient_data_for_seir"

            historical_df['time'] = pd.to_datetime(historical_df['time'])
            historical_df = historical_df.sort_values('time')
            # Ensure 'total_hukou' is numeric
            historical_df['total_hukou'] = pd.to_numeric(historical_df['total_hukou'], errors='coerce').fillna(0)

            if len(historical_df) < 3: # Need some data for initial conditions
                 return None, "insufficient_data_for_seir_initialization"

            beta, sigma, gamma, पापुलेशन_फैक्टर = self.get_seir_model_parameters(disease, historical_df)
            
            N_effective = self.shanghai_population * पापुलेशन_फैक्टर


            # Estimate initial conditions (I0, R0, E0)
            # This is a simplified estimation and can be improved
            cumulative_cases = historical_df['total_hukou'].cumsum()
            
            # I0: Active cases - sum of new cases over the typical duration of illness
            # Assuming data points are roughly monthly. If daily, adjust divisor.
            # For monthly data, duration_days / 30 gives months.
            duration_in_months = max(1, int(round(1.0/gamma / 30))) if gamma > 0 else 1
            I0 = historical_df['total_hukou'].tail(duration_in_months).sum()
            I0 = max(I0, 1) # Ensure I0 is at least 1 to start the model if there's any case history

            R0 = cumulative_cases.iloc[-1] - I0 if not cumulative_cases.empty else 0
            R0 = max(R0, 0)

            # E0: Exposed individuals, often estimated as a proportion of I0 or recent new cases
            incubation_period_days = 1.0/sigma if sigma > 0 else 5
            # Assuming last reported cases are a proxy for recent exposure rate
            E0 = historical_df['total_hukou'].iloc[-1] * (incubation_period_days / 30.0) if not historical_df.empty else I0 * 0.5
            E0 = max(E0, I0 * 0.2) # Ensure E0 is at least a fraction of I0

            S0 = N_effective - I0 - E0 - R0
            if S0 < 0: S0 = 0 # Population cannot be negative

            seir_model = SEIRModel(beta, sigma, gamma, N_effective)
            
            prediction_days = prediction_months * 30 # Approximate days
            t = np.linspace(0, prediction_days -1, prediction_days)
            
            S, E, I, R = seir_model.predict(S0, E0, I0, R0, t)
            
            # Calculate daily new cases (people moving from E to I, or new reported I)
            # More accurately, new cases are sigma * E
            daily_new_exposed = np.diff(S0 - S, prepend=0) # People leaving S
            daily_new_infected_from_exposed = sigma * E # Number of people moving from E to I each day
            
            # predicted_growth = np.maximum(0, daily_new_infected_from_exposed).round().astype(int)
            # Alternative: new people entering I+R compartment (as in example)
            # This represents people who newly become infected or recovered.
            # For "growth" of cases, sigma * E is more standard for SEIR.
            predicted_growth = np.maximum(0, np.diff(I + R, prepend=(I[0]+R[0]) if len(I)>0 else 0)).round().astype(int)


            last_hist_date = historical_df['time'].iloc[-1]
            predicted_dates_daily = [(last_hist_date + timedelta(days=int(i))).strftime('%Y-%m-%d') for i in range(prediction_days)]

            # Aggregate daily SEIR predictions to monthly to match SARIMA
            # Use SARIMA's monthly dates for alignment
            # Generate monthly dates starting from the month after the last historical data point
            monthly_prediction_dates = []
            current_pred_date = last_hist_date.replace(day=1)
            for _ in range(prediction_months):
                if current_pred_date.month == 12:
                    current_pred_date = current_pred_date.replace(year=current_pred_date.year + 1, month=1)
                else:
                    current_pred_date = current_pred_date.replace(month=current_pred_date.month + 1)
                monthly_prediction_dates.append(current_pred_date.strftime('%Y-%m-%d'))

            monthly_growth = []
            # Ensure predicted_growth has values
            if len(predicted_growth) > 0:
                df_seir_daily = pd.DataFrame({'date': pd.to_datetime(predicted_dates_daily), 'growth': predicted_growth})
                df_seir_daily = df_seir_daily.set_index('date')
                
                # Aggregate daily growth to monthly sums
                # This resampling will align with the start of each month
                # df_seir_monthly = df_seir_daily['growth'].resample('MS').sum() # 'MS' for Month Start
                # Ensure it covers the prediction_months correctly
                
                temp_monthly_growth = []
                start_day_offset = (last_hist_date - last_hist_date.replace(day=1)).days
                current_day_index = 0
                
                for _ in range(prediction_months):
                    days_in_this_pred_month = 30 # Approximation
                    month_sum = 0
                    if current_day_index < len(predicted_growth):
                        # Sum daily predictions for the current prediction month
                        # This is a simplified aggregation, assuming 30 days per month
                        month_sum = sum(predicted_growth[current_day_index : current_day_index + days_in_this_pred_month])
                    temp_monthly_growth.append(month_sum)
                    current_day_index += days_in_this_pred_month
                monthly_growth = temp_monthly_growth[:prediction_months]


            if len(monthly_growth) != prediction_months: # Fallback if aggregation fails
                 monthly_growth = [0] * prediction_months


            return {
                'dates': monthly_prediction_dates, # Monthly dates
                'predicted_monthly_growth': monthly_growth, # Monthly aggregated growth
                'details': { # Optional: full daily SEIR compartment data
                    'daily_dates': predicted_dates_daily,
                    'S': S.tolist(), 'E': E.tolist(), 'I': I.tolist(), 'R': R.tolist()
                }
            }, "success"

        except Exception as e:
            print(f"Error in get_seir_prediction (Shanghai): {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, f"seir_prediction_failed: {str(e)}"

    def get_prediction(self, disease, start_date, end_date, prediction_months=6):
        """获取上海特定疾病的预测"""
        try:
            # 获取数据
            df = self.get_data_from_db(start_date, end_date, disease)
            
            if df.empty or len(df) < 12:
                print(f"Insufficient data for disease {disease} in Shanghai")
                return None, "insufficient_data"
            
            # 数据预处理
            prepared_data = self.prepare_data(df)
            if prepared_data is None or prepared_data.empty:
                return None, "data_preparation_failed"
            
            # 拟合模型和进行预测
            results = self.fit_sarima_model(prepared_data, prediction_months, disease)
            if results is None:
                return None, "model_fitting_failed"
            
            # 准备返回的数据结构
            historical_dates = prepared_data.index.strftime('%Y-%m-%d').tolist()
            predicted_dates = results['dates']
            
            result_data = {
                'historical_dates': historical_dates,
                'dates': predicted_dates,
                'historical_data': {
                    'local_hukou': prepared_data['户籍新增'].fillna(0).tolist(),
                    'nonlocal_hukou': prepared_data['非户籍新增'].fillna(0).tolist(),
                    'total_hukou': prepared_data['合计'].fillna(0).tolist()
                },
                'predictions': {
                    'local_hukou': results['户籍新增'],
                    'nonlocal_hukou': results['非户籍新增'],
                    'total_hukou': results['合计']
                }
            }
            
            return result_data, "success"
            
        except Exception as e:
            print(f"Error in get_prediction (Shanghai): {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, f"prediction_failed: {str(e)}"
    
    def prepare_data(self, df):
        """准备预测所需的数据"""
        try:
            # 确保数据按时间排序
            df = df.sort_values('time')
            
            # 设置时间索引
            df = df.set_index('time')
            
            # 创建列名映射 - 这些是UI显示名称
            df = df.rename(columns={
                'local_hukou': '户籍新增',
                'nonlocal_hukou': '非户籍新增',
                'total_hukou': '合计'
            })
            
            # 确保所有数值列为数字类型
            for col in ['户籍新增', '非户籍新增', '合计']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 将月度数据重采样，确保没有缺失值
            if len(df) > 1:
                # 按月重采样，使用平均值填充，指定numeric_only=True避免警告
                df_monthly = df.resample('MS').mean(numeric_only=True)
                
                # 对于缺失值，使用前向填充
                df_monthly = df_monthly.fillna(method='ffill')
                
                return df_monthly
            else:
                print("Insufficient data for resampling")
                return df
            
        except Exception as e:
            print(f"Error in prepare_data (Shanghai): {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None
    
    def fit_sarima_model(self, data, prediction_months=6, disease_name=None):
        """使用SARIMA模型进行预测"""
        try:
            # 获取预测所需的列名 - 这些是中间映射名称
            columns = ['户籍新增', '非户籍新增', '合计']
            
            forecast_results = {}
            forecast_dates = []
            
            # 设置预测的开始日期
            if data.index[-1].month == 12:
                start_forecast = pd.Timestamp(year=data.index[-1].year + 1, month=1, day=1)
            else:
                start_forecast = pd.Timestamp(year=data.index[-1].year, month=data.index[-1].month + 1, day=1)
            
            # 生成预测日期
            for i in range(prediction_months):
                if (start_forecast.month + i) % 12 == 0:
                    month = 12
                    year = start_forecast.year + (start_forecast.month + i - 12) // 12
                else:
                    month = (start_forecast.month + i) % 12
                    year = start_forecast.year + (start_forecast.month + i - 1) // 12
                
                forecast_date = pd.Timestamp(year=year, month=month, day=1)
                forecast_dates.append(forecast_date.strftime('%Y-%m-%d'))
            
            # 对每列数据进行SARIMA预测
            for column in columns:
                # 确保列存在
                if column not in data.columns:
                    print(f"Column {column} not found in data. Available columns: {data.columns.tolist()}")
                    continue
                    
                series = data[column].astype(float)
                
                # 使用自动参数选择的SARIMA模型
                # 对于月度数据，季节性周期设为12
                model = SARIMAX(
                    series,
                    order=(1, 1, 1),
                    seasonal_order=(1, 1, 1, 12),
                    enforce_stationarity=False,
                    enforce_invertibility=False
                )
                
                # 拟合模型
                try:
                    results = model.fit(disp=False)
                except Exception as fit_error:
                    print(f"Error fitting SARIMA model for {column}: {str(fit_error)}")
                    # 尝试更简单的模型
                    try:
                        model = SARIMAX(
                            series,
                            order=(1, 0, 0),
                            seasonal_order=(1, 0, 0, 12),
                            enforce_stationarity=False,
                            enforce_invertibility=False
                        )
                        results = model.fit(disp=False)
                    except Exception as simple_fit_error:
                        print(f"Error fitting simple SARIMA model: {str(simple_fit_error)}")
                        # 如果所有模型都失败，使用简单线性外推
                        forecast_results[column] = {
                            'mean': [int(series.iloc[-1])] * prediction_months,
                            'lower': [max(0, int(series.iloc[-1] * 0.8))] * prediction_months,
                            'upper': [int(series.iloc[-1] * 1.2)] * prediction_months
                        }
                        continue
                
                # 预测未来几个月
                forecast = results.get_forecast(steps=prediction_months)
                predicted_mean = forecast.predicted_mean
                confidence_intervals = forecast.conf_int(alpha=0.05)  # 95% 置信区间
                
                # 针对低发病率疾病(如登革热)优化置信区间显示
                # 更严格地限制置信区间范围
                disease_is_low_incidence = predicted_mean.mean() < 15 or (disease_name and disease_name in ['登革热', '疟疾', '黄热病'])
                
                if disease_is_low_incidence:  # 对于低发病率疾病
                    # 使用更严格的置信区间限制
                    for i in range(len(predicted_mean)):
                        mean_value = predicted_mean.iloc[i]
                        # 限制上界不超过均值的1.5倍且最多增加8
                        upper_bound = min(mean_value * 1.5, mean_value + 8)
                        # 限制下界不小于均值的0.5倍且最多减少3
                        lower_bound = max(0, max(mean_value * 0.5, mean_value - 3))
                        
                        confidence_intervals.iloc[i, 0] = lower_bound  # 调整下界
                        confidence_intervals.iloc[i, 1] = upper_bound  # 调整上界
                
                # 应用疾病特性和季节因素
                for i in range(len(predicted_mean)):
                    forecast_date = pd.Timestamp(forecast_dates[i])
                    month = forecast_date.month
                    
                    # 获取疾病和季节因子
                    factor = self.get_disease_factors(disease_name, month) if disease_name else 1.0
                    
                    # 应用因子调整预测值
                    predicted_mean.iloc[i] *= factor
                    confidence_intervals.iloc[i, 0] *= factor  # 调整下界
                    confidence_intervals.iloc[i, 1] *= factor  # 调整上界
                
                # 确保预测值不为负数
                predicted_mean = np.maximum(predicted_mean, 0)
                confidence_intervals.iloc[:, 0] = np.maximum(confidence_intervals.iloc[:, 0], 0)
                confidence_intervals.iloc[:, 1] = np.maximum(confidence_intervals.iloc[:, 1], 0)  # 确保上界也不为负
                
                # 处理NaN和无限值
                predicted_mean = predicted_mean.fillna(0)
                confidence_intervals = confidence_intervals.fillna(0)
                # 将任何无限值替换为0
                predicted_mean = predicted_mean.replace([np.inf, -np.inf], 0)
                confidence_intervals = confidence_intervals.replace([np.inf, -np.inf], 0)
                
                # 存储结果
                forecast_results[column] = {
                    'mean': predicted_mean.round().astype(int).tolist(),
                    'lower': confidence_intervals.iloc[:, 0].round().astype(int).tolist(),
                    'upper': confidence_intervals.iloc[:, 1].round().astype(int).tolist()
                }
            
            return {
                'dates': forecast_dates,
                **forecast_results
            }
            
        except Exception as e:
            print(f"Error in fit_sarima_model (Shanghai): {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None

    def detect_and_correct_anomalies(self, historical_data, predictions, prediction_months=6):
        """
        检测预测结果中的异常值并修正
        - historical_data: 历史数据，用于计算基线统计
        - predictions: 预测结果字典
        - prediction_months: 预测月数
        """
        try:
            corrected_predictions = {}
            
            for field in ['total_hukou', 'local_hukou', 'nonlocal_hukou']:
                if field not in predictions or field not in historical_data:
                    continue
                
                # 获取历史数据和预测数据
                hist_values = np.array(historical_data[field])
                pred_values = np.array(predictions[field]['mean'])
                
                # 计算历史数据的统计指标
                hist_mean = np.mean(hist_values)
                hist_std = np.std(hist_values)
                hist_max = np.max(hist_values)
                
                # 设置异常检测阈值 - 根据历史数据的特征设定
                # 如果历史数据波动很小或平均值很小，使用更严格的阈值
                if hist_mean < 10:  # 低发病率疾病
                    # 对于平均每月不到10例的疾病，预测不应超过历史最大值的5倍或平均值的10倍
                    threshold = max(hist_max * 5, hist_mean * 10)
                    # 确保阈值至少为20
                    threshold = max(threshold, 20)
                else:
                    # 正常情况下，阈值设为均值+5倍标准差，但不少于历史最大值的3倍
                    threshold = max(hist_mean + 5 * hist_std, hist_max * 3)
                
                # 检测异常值
                is_anomaly = False
                for pred_val in pred_values:
                    # 检查是否有极端异常增长
                    if pred_val > threshold:
                        is_anomaly = True
                        break
                
                # 处理异常预测
                if is_anomaly:
                    print(f"检测到异常预测值: {field} 历史均值={hist_mean:.2f}, 阈值={threshold:.2f}, 预测值={pred_values}")
                    # 基于历史数据生成合理的预测值（略微随机波动）
                    np.random.seed(42)  # 设置随机种子以保证可重复性
                    
                    # 基于历史数据的均值和标准差生成新预测
                    # 允许小幅增长趋势，每月增加历史标准差的一小部分
                    growth_factor = min(hist_std * 0.1, hist_mean * 0.05)  # 控制增长幅度
                    
                    new_predictions_mean = []
                    new_predictions_lower = []
                    new_predictions_upper = []
                    
                    base_value = hist_mean
                    for i in range(prediction_months):
                        # 添加随机波动，波动范围为历史标准差的30%
                        random_factor = 1 + np.random.uniform(-0.15, 0.15)
                        
                        # 计算新预测值，基础值加微小趋势加随机波动
                        new_value = max(0, (base_value + i * growth_factor) * random_factor)
                        new_value = round(new_value)
                        
                        # 存储新的预测值
                        new_predictions_mean.append(int(new_value))
                        new_predictions_lower.append(int(max(0, new_value * 0.8)))  # 下界
                        new_predictions_upper.append(int(new_value * 1.2))  # 上界
                    
                    # 使用修正后的预测值替换异常预测值
                    corrected_predictions[field] = {
                        'mean': new_predictions_mean,
                        'lower': new_predictions_lower,
                        'upper': new_predictions_upper,
                        'anomaly_corrected': True  # 标记此预测已被修正
                    }
                else:
                    # 保留原始预测
                    corrected_predictions[field] = predictions[field]
            
            return corrected_predictions, True
            
        except Exception as e:
            print(f"异常检测和修正过程出错: {str(e)}")
            print(traceback.format_exc())
            # 返回原始预测值
            return predictions, False

    def ensure_data_consistency(self, predictions, prediction_months=6):
        """
        确保预测数据的一致性：
        1. 总数(total_hukou)不应小于本市户籍(local_hukou)或非本市户籍(nonlocal_hukou)
        2. 总数应接近或等于本市户籍和非本市户籍的总和
        """
        try:
            if not all(key in predictions for key in ['total_hukou', 'local_hukou', 'nonlocal_hukou']):
                # 如果三个键不全，无法进行一致性校验
                return predictions, False
            
            has_correction = False
            corrected_predictions = predictions.copy()
            
            for i in range(min(prediction_months, 
                              len(predictions['total_hukou']['mean']),
                              len(predictions['local_hukou']['mean']),
                              len(predictions['nonlocal_hukou']['mean']))):
                
                local_val = predictions['local_hukou']['mean'][i]
                nonlocal_val = predictions['nonlocal_hukou']['mean'][i]
                total_val = predictions['total_hukou']['mean'][i]
                
                # 计算应该的总和
                expected_total = local_val + nonlocal_val
                
                # 检查总数是否小于任一分项
                inconsistent = total_val < local_val or total_val < nonlocal_val
                
                # 检查总数是否与分项之和相差过大（允许有5%的误差）
                sum_inconsistent = abs(total_val - expected_total) > max(5, expected_total * 0.05)
                
                if inconsistent or sum_inconsistent:
                    # 需要修正
                    has_correction = True
                    
                    # 新的总数应该至少是各分项的最大值，通常应接近分项之和
                    new_total = max(local_val, nonlocal_val, expected_total)
                    
                    # 更新总数的均值、下界和上界
                    corrected_predictions['total_hukou']['mean'][i] = new_total
                    
                    # 调整置信区间，保持原有的相对比例
                    if total_val > 0:
                        ratio = new_total / total_val
                        corrected_predictions['total_hukou']['lower'][i] = int(predictions['total_hukou']['lower'][i] * ratio)
                        corrected_predictions['total_hukou']['upper'][i] = int(predictions['total_hukou']['upper'][i] * ratio)
                    else:
                        # 如果原总数为0，设置合理的置信区间
                        corrected_predictions['total_hukou']['lower'][i] = int(new_total * 0.8)
                        corrected_predictions['total_hukou']['upper'][i] = int(new_total * 1.2)
                    
                    print(f"数据一致性修正: 月份{i+1}, 原总数={total_val}, 本市={local_val}, 非本市={nonlocal_val}, 修正后总数={new_total}")
            
            if has_correction and 'anomaly_corrected' not in corrected_predictions['total_hukou']:
                corrected_predictions['total_hukou']['consistency_corrected'] = True
                
            return corrected_predictions, has_correction
            
        except Exception as e:
            print(f"数据一致性校验过程出错: {str(e)}")
            print(traceback.format_exc())
            return predictions, False

    def get_combined_prediction(self, disease, start_date, end_date, prediction_months=6):
        try:
            # 1. Get SARIMA prediction
            sarima_results, sarima_status = self.get_prediction(disease, start_date, end_date, prediction_months)
            if sarima_status != "success":
                print(f"SARIMA prediction failed: {sarima_status}")
                # Fallback to SEIR only if SARIMA fails but SEIR might succeed
                seir_results, seir_status = self.get_seir_prediction(disease, start_date, end_date, prediction_months)
                if seir_status == "success" and seir_results:
                    # Adapt SEIR output to be somewhat compatible if SARIMA is missing
                    return {
                        'dates': seir_results['dates'],
                        'predictions': { # Providing SEIR as the main prediction for 'total_hukou'
                            'total_hukou': {'mean': seir_results['predicted_monthly_growth'], 'lower': [g*0.8 for g in seir_results['predicted_monthly_growth']], 'upper': [g*1.2 for g in seir_results['predicted_monthly_growth']]},
                            'local_hukou': {'mean': [0]*prediction_months, 'lower': [0]*prediction_months, 'upper': [0]*prediction_months}, # No basis to split
                            'nonlocal_hukou': {'mean': [0]*prediction_months, 'lower': [0]*prediction_months, 'upper': [0]*prediction_months} # No basis to split
                        },
                        'historical_data': {}, # SARIMA would provide this
                        'seir_details': seir_results.get('details'),
                        'status_info': 'SARIMA_failed_SEIR_provided'
                    }, "success_seir_only"
                return None, f"sarima_failed_{sarima_status}"

            # 2. Get SEIR prediction
            seir_results, seir_status = self.get_seir_prediction(disease, start_date, end_date, prediction_months)
            if seir_status != "success":
                print(f"SEIR prediction failed: {seir_status}, using SARIMA only.")
                # Fallback to SARIMA only if SEIR fails
                # Add SEIR status info to SARIMA results if possible
                sarima_results['status_info'] = 'SEIR_failed_SARIMA_provided'
                return sarima_results, "success_sarima_only"
            
            # Ensure results are valid
            if not sarima_results or not seir_results:
                return None, "missing_model_results"

            # 3. Combine predictions (Weighted average for 'total_hukou')
            # Weights can be adjusted or made dynamic based on model confidence
            seir_weight = 0.4
            sarima_weight = 0.6

            combined_predictions_total = {'mean': [], 'lower': [], 'upper': []}
            
            sarima_total_pred = sarima_results['predictions']['total_hukou']
            seir_monthly_growth = seir_results['predicted_monthly_growth']

            # Ensure lengths match, take min length if not (should be prediction_months)
            num_predictions = min(len(sarima_total_pred['mean']), len(seir_monthly_growth), prediction_months)

            for i in range(num_predictions):
                sarima_mean = sarima_total_pred['mean'][i]
                # SEIR does not produce lower/upper bounds in this setup, use SARIMA's or estimate
                # For simplicity, we'll combine means and use SARIMA's relative interval for combined
                seir_mean = seir_monthly_growth[i]
                
                combined_mean = (sarima_mean * sarima_weight) + (seir_mean * seir_weight)
                combined_predictions_total['mean'].append(int(round(combined_mean)))
                
                # Combine bounds: weighted average of bounds, or use SARIMA's interval scaled by combined_mean
                sarima_lower = sarima_total_pred['lower'][i]
                sarima_upper = sarima_total_pred['upper'][i]
                
                # Simple weighted average for bounds
                combined_lower = (sarima_lower * sarima_weight) + (seir_mean * 0.8 * seir_weight) # Assuming SEIR lower is 0.8*mean
                combined_upper = (sarima_upper * sarima_weight) + (seir_mean * 1.2 * seir_weight) # Assuming SEIR upper is 1.2*mean
                
                combined_predictions_total['lower'].append(int(round(max(0, combined_lower))))  # 确保下界不为负
                combined_predictions_total['upper'].append(int(round(max(0, combined_upper))))
            
            # Fill remaining predictions if lengths were mismatched (e.g. one model produced fewer forecasts)
            if num_predictions < prediction_months:
                for i in range(num_predictions, prediction_months):
                    if i < len(sarima_total_pred['mean']): # Use SARIMA if available
                        combined_predictions_total['mean'].append(sarima_total_pred['mean'][i])
                        combined_predictions_total['lower'].append(sarima_total_pred['lower'][i])
                        combined_predictions_total['upper'].append(sarima_total_pred['upper'][i])
                    elif i < len(seir_monthly_growth): # Use SEIR if available
                         combined_predictions_total['mean'].append(seir_monthly_growth[i])
                         combined_predictions_total['lower'].append(int(round(seir_monthly_growth[i]*0.8)))
                         combined_predictions_total['upper'].append(int(round(seir_monthly_growth[i]*1.2)))
                    else: # Fallback to zero
                        combined_predictions_total['mean'].append(0)
                        combined_predictions_total['lower'].append(0)
                        combined_predictions_total['upper'].append(0)

            # 组织预测结果
            predictions_for_anomaly_check = {
                'local_hukou': sarima_results['predictions']['local_hukou'],
                'nonlocal_hukou': sarima_results['predictions']['nonlocal_hukou'],
                'total_hukou': combined_predictions_total
            }
            
            # 新增：检测异常值并修正
            if sarima_results.get('historical_data'):
                corrected_predictions, was_anomaly_corrected = self.detect_and_correct_anomalies(
                    sarima_results['historical_data'],
                    predictions_for_anomaly_check,
                    prediction_months
                )
            else:
                corrected_predictions = predictions_for_anomaly_check
                was_anomaly_corrected = False
            
            # 新增：确保数据一致性
            final_predictions, was_consistency_corrected = self.ensure_data_consistency(
                corrected_predictions,
                prediction_months
            )
            
            # 生成状态信息
            status_suffix = ""
            if was_anomaly_corrected:
                status_suffix += "_anomaly_corrected"
            if was_consistency_corrected:
                status_suffix += "_consistency_corrected"

            final_result = {
                'dates': sarima_results['dates'][:prediction_months], # Use SARIMA dates as primary
                'historical_dates': sarima_results.get('historical_dates', []),
                'historical_data': sarima_results.get('historical_data', {}),
                'predictions': final_predictions,
                'seir_details': seir_results.get('details'), # Full S,E,I,R daily data
                'sarima_details': { # Original SARIMA 'total_hukou' for comparison
                    'total_hukou': sarima_total_pred
                },
                'status_info': 'success_combined' + status_suffix
            }
            
            return final_result, "success"

        except Exception as e:
            print(f"Error in get_combined_prediction (Shanghai): {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, f"combined_prediction_failed: {str(e)}"

def handler():
    """处理上海疾病预测请求"""
    try:
        disease = request.form.get('disease')
        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')
        prediction_period = int(request.form.get('prediction_period', 6))
        
        if not all([disease, start_date_str, end_date_str]):
            return jsonify({
                "error": "缺少必要参数",
                "status": "invalid_params"
            }), 400
        
        # 验证日期格式
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').strftime('%Y-%m-%d')
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').strftime('%Y-%m-%d')
        except ValueError:
            return jsonify({
                "error": "日期格式不正确，请使用YYYY-MM-DD格式",
                "status": "invalid_date"
            }), 400
            
        # 初始化预测模型
        predictor = SARIMAPredictionShanghai()
        # Call the new combined prediction method
        prediction_results, status = predictor.get_combined_prediction(
            disease, 
            start_date, 
            end_date, 
            prediction_period
        )
        
        if not status.startswith("success") or not prediction_results:
            error_messages = {
                "insufficient_data": "历史数据不足，预测需要至少12个月的历史数据记录。",
                "data_preparation_failed": "数据准备过程中发生错误，请检查数据格式。",
                "model_fitting_failed": "模型拟合失败，请尝试其他参数或数据范围。",
                "insufficient_data_for_seir": "SEIR模型所需历史数据不足。",
                "seir_prediction_failed": "SEIR模型预测失败。",
                "sarima_failed": "SARIMA模型预测失败。",
                "combined_prediction_failed": "组合预测失败。",
                 "missing_model_results": "一个或两个模型未能返回结果"
            }
            # Extract specific failure message if available in status
            detailed_status = status.split(':')[-1] if ':' in status else status
            error_message = error_messages.get(detailed_status, f"预测失败: {status}")
            
            return jsonify({
                "error": error_message,
                "status": status
            }), 400 # Or 500 for server-side failures
            
        return jsonify(prediction_results)
        
    except Exception as e:
        print(f"Error in predict_disease_shanghai: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        
        # 为用户提供更友好的错误信息
        error_message = "预测过程中发生错误"
        if "IntCastingNaNError" in str(e):
            error_message = "预测结果中包含无效值，无法完成计算。请调整日期范围或选择其他疾病。"
        elif "too few observations" in str(e).lower():
            error_message = "历史数据太少，无法建立可靠的预测模型。请增加历史数据范围。"
        
        return jsonify({"error": error_message, "status": "server_error"}), 400 