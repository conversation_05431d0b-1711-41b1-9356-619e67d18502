from flask import jsonify, request
from model.models import db, GlobalRiskAssessment
import json
from datetime import datetime
from collections import defaultdict
import re


def handler():
    """获取全球传染病事件风险评估数据"""
    try:
        # 获取查询参数
        risk_type = request.args.get("risk_type", "import")  # import 或 travel
        disease = request.args.get("disease", "")
        start_date = request.args.get("start_date", "")  # 开始时间
        end_date = request.args.get("end_date", "")  # 结束时间
        include_history = (
            request.args.get("include_history", "false").lower() == "true"
        )  # 是否包含历史对比

        # 构建查询
        query = GlobalRiskAssessment.query

        # 如果指定了疾病，则过滤
        if disease:
            query = query.filter(GlobalRiskAssessment.disease == disease)

        # 如果指定了时间范围，则过滤
        if start_date:
            query = query.filter(GlobalRiskAssessment.data_time >= start_date)
        if end_date:
            query = query.filter(GlobalRiskAssessment.data_time <= end_date)

        # 获取所有数据，按data_time降序排列
        risk_data = query.order_by(GlobalRiskAssessment.data_time.desc()).all()

        if not risk_data:
            return jsonify(
                {
                    "countries": [],
                    "diseases": [],
                    "time_range": get_available_time_range(),
                }
            )

        # 获取所有疾病列表
        diseases = list(set([item.disease for item in risk_data if item.disease]))

        # 按国家和疾病分组，并选择最新的数据
        if include_history:
            processed_data = process_country_disease_data_with_history(
                risk_data, risk_type
            )
        else:
            processed_data = process_country_disease_data(risk_data, risk_type)

        return jsonify(
            {
                "countries": processed_data,
                "diseases": sorted(diseases),
                "time_range": get_available_time_range(),
                "current_time_filter": {"start_date": start_date, "end_date": end_date},
            }
        )

    except Exception as e:
        print(f"Error in global_risk_assessment: {str(e)}")
        return jsonify({"error": str(e)}), 500


def process_country_disease_data(risk_data, risk_type):
    """
    处理国家疾病数据，按照以下规则：
    1. 按国家分组（标准化国家名称）
    2. 对于每个国家，选择最新月份的数据
    3. 如果一个国家有多个疾病，取最高风险等级
    """
    # 按标准化国家名称分组
    country_groups = defaultdict(list)
    for item in risk_data:
        if item.country:
            # 标准化国家名称
            standard_country = standardize_country_name(item.country)
            country_groups[standard_country].append(item)

    processed_data = []

    for country, country_data in country_groups.items():
        # 获取该国家的最新数据
        latest_data = get_latest_country_data(country_data, risk_type)
        if latest_data:
            # 使用标准化的国家名称
            latest_data["name"] = country
            processed_data.append(latest_data)

    return processed_data


def get_latest_country_data(country_data, risk_type):
    """
    获取国家的最新数据，优先选择最新月份，如果有多个疾病则取最高风险等级
    向前查找最多1年
    """
    if not country_data:
        return None

    # 按data_time分组
    time_groups = defaultdict(list)
    for item in country_data:
        data_time = item.data_time or ""
        time_groups[data_time].append(item)

    # 获取最新的时间
    sorted_times = sorted(time_groups.keys(), reverse=True)

    # 确定查找范围：从最新时间向前最多1年
    if sorted_times:
        latest_time = sorted_times[0]
        cutoff_time = get_cutoff_time(latest_time)
        # 过滤掉超过1年的数据
        sorted_times = [t for t in sorted_times if is_within_time_range(t, cutoff_time)]

    # 从最新时间开始查找有效数据
    for data_time in sorted_times:
        time_data = time_groups[data_time]

        # 在同一时间的数据中，选择风险等级最高的
        best_item = None
        max_risk_value = -1

        for item in time_data:
            # 根据风险类型选择相应的风险等级
            if risk_type == "import":
                risk_level = item.import_risk_level
            else:
                risk_level = item.travel_risk_level

            risk_value = get_risk_value(risk_level)

            if risk_value > max_risk_value:
                max_risk_value = risk_value
                best_item = item

        if best_item:
            # 构建返回数据，包含该时间段的所有疾病信息
            diseases_in_time = list(set([d.disease for d in time_data if d.disease]))

            # 根据风险类型选择相应的风险等级
            if risk_type == "import":
                risk_level = best_item.import_risk_level
            else:
                risk_level = best_item.travel_risk_level

            return {
                "name": best_item.country,
                "continent": best_item.continent,
                "disease": best_item.disease,
                "diseases": diseases_in_time,  # 该时间段的所有疾病
                "risk_level": risk_level,
                "risk_value": get_risk_value(risk_level),
                "total_cases": best_item.total_cases,
                "suspected_cases": best_item.suspected_cases,
                "confirmed_cases": best_item.confirmed_cases,
                "cumulative_deaths": best_item.cumulative_deaths,
                "import_risk_level": best_item.import_risk_level,
                "travel_risk_level": best_item.travel_risk_level,
                "start_date": best_item.start_date,
                "end_date": best_item.end_date,
                "data_time": best_item.data_time,
                "data_source": best_item.data_source,
                "source_url": best_item.source_url,
                "all_diseases_data": [
                    {
                        "disease": d.disease,
                        "continent": d.continent,
                        "country": d.country,
                        "start_date": d.start_date,
                        "end_date": d.end_date,
                        "total_cases": d.total_cases,
                        "suspected_cases": d.suspected_cases,
                        "confirmed_cases": d.confirmed_cases,
                        "cumulative_deaths": d.cumulative_deaths,
                        "import_risk": d.import_risk_level,
                        "travel_risk": d.travel_risk_level,
                        "data_time": d.data_time,
                        "data_source": d.data_source,
                        "source_url": d.source_url,
                    }
                    for d in time_data
                ],
            }

    return None


def get_risk_value(risk_level):
    """将风险等级转换为数值"""
    if not risk_level:
        return 0

    risk_level = risk_level.strip().lower()
    if risk_level in ["高", "high"]:
        return 3
    elif risk_level in ["中", "medium", "moderate"]:
        return 2
    elif risk_level in ["低", "low"]:
        return 1
    else:
        return 0


def standardize_country_name(country_name):
    """
    标准化国家名称，将海外省和领地映射到主权国家
    支持模糊匹配，处理括号内的说明文字
    """
    if not country_name:
        return country_name

    # 移除括号内容，获取主要名称
    main_name = re.sub(r"\（.*?\）|\(.*?\)", "", country_name).strip()

    # 法国海外省和领地关键词
    france_keywords = [
        "留尼汪",
        "法属圭亚那",
        "马提尼克",
        "瓜德罗普",
        "马约特",
        "新喀里多尼亚",
        "法属波利尼西亚",
        "瓦利斯和富图纳",
        "圣皮埃尔和密克隆",
        "法属圣马丁",
        "圣巴泰勒米",
    ]

    # 英国海外领地关键词
    uk_keywords = [
        "百慕大",
        "开曼群岛",
        "英属维尔京群岛",
        "特克斯和凯科斯群岛",
        "蒙特塞拉特",
        "安圭拉",
        "马尔维纳斯群岛",
        "福克兰群岛",
        "直布罗陀",
        "圣赫勒拿",
        "皮特凯恩群岛",
    ]

    # 美国海外领地关键词
    us_keywords = ["波多黎各", "美属维尔京群岛", "关岛", "美属萨摩亚", "北马里亚纳群岛"]

    # 检查是否包含法国海外省关键词
    for keyword in france_keywords:
        if keyword in main_name or keyword in country_name:
            return "法国"

    # 检查是否包含英国海外领地关键词
    for keyword in uk_keywords:
        if keyword in main_name or keyword in country_name:
            return "英国"

    # 检查是否包含美国海外领地关键词
    for keyword in us_keywords:
        if keyword in main_name or keyword in country_name:
            return "美国"

    # 特殊处理：检查括号内是否包含主权国家信息
    if "法国" in country_name:
        return "法国"
    elif "英国" in country_name:
        return "英国"
    elif "美国" in country_name:
        return "美国"

    # 如果没有匹配，返回主要名称（去除括号后的名称）
    return main_name if main_name else country_name


def get_risk_color(risk_level):
    """根据风险等级获取颜色"""
    if not risk_level:
        return "#cccccc"

    risk_level = risk_level.strip().lower()
    if risk_level in ["高", "high"]:
        return "#ff4444"  # 红色
    elif risk_level in ["中", "medium", "moderate"]:
        return "#ffaa00"  # 黄色
    elif risk_level in ["低", "low"]:
        return "#44ff44"  # 绿色
    else:
        return "#cccccc"  # 灰色


def parse_data_time(data_time_str):
    """
    解析data_time字符串，返回年月元组
    支持格式：2025-06, 2025/06, 2025年6月等
    """
    if not data_time_str:
        return None

    # 尝试匹配 YYYY-MM 或 YYYY/MM 格式
    match = re.match(r"(\d{4})[-/](\d{1,2})", data_time_str)
    if match:
        year, month = int(match.group(1)), int(match.group(2))
        return (year, month)

    # 尝试匹配 YYYY年MM月 格式
    match = re.match(r"(\d{4})年(\d{1,2})月?", data_time_str)
    if match:
        year, month = int(match.group(1)), int(match.group(2))
        return (year, month)

    return None


def get_cutoff_time(latest_time_str):
    """
    根据最新时间计算截止时间（向前1年）
    """
    parsed = parse_data_time(latest_time_str)
    if not parsed:
        return None

    year, month = parsed
    # 向前1年
    cutoff_year = year - 1
    cutoff_month = month

    return (cutoff_year, cutoff_month)


def is_within_time_range(data_time_str, cutoff_time):
    """
    检查数据时间是否在截止时间之后（即在1年范围内）
    """
    if not cutoff_time:
        return True

    parsed = parse_data_time(data_time_str)
    if not parsed:
        return True  # 无法解析的时间默认保留

    year, month = parsed
    cutoff_year, cutoff_month = cutoff_time

    # 比较年月
    if year > cutoff_year:
        return True
    elif year == cutoff_year and month >= cutoff_month:
        return True
    else:
        return False


def get_available_time_range():
    """获取数据库中可用的时间范围"""
    try:
        # 获取最早和最晚的数据时间
        earliest = (
            db.session.query(GlobalRiskAssessment.data_time)
            .filter(GlobalRiskAssessment.data_time.isnot(None))
            .order_by(GlobalRiskAssessment.data_time.asc())
            .first()
        )

        latest = (
            db.session.query(GlobalRiskAssessment.data_time)
            .filter(GlobalRiskAssessment.data_time.isnot(None))
            .order_by(GlobalRiskAssessment.data_time.desc())
            .first()
        )

        if earliest and latest:
            return {"start": earliest[0], "end": latest[0]}
        else:
            return {"start": None, "end": None}
    except Exception as e:
        print(f"Error getting time range: {str(e)}")
        return {"start": None, "end": None}


def process_country_disease_data_with_history(risk_data, risk_type):
    """
    处理国家疾病数据，包含历史对比信息
    """
    # 按标准化国家名称分组
    country_groups = defaultdict(list)
    for item in risk_data:
        if item.country:
            # 标准化国家名称
            standard_country = standardize_country_name(item.country)
            country_groups[standard_country].append(item)

    processed_data = []

    for country, country_data in country_groups.items():
        # 按时间排序，最新的在前
        country_data.sort(key=lambda x: x.data_time or "", reverse=True)

        # 获取最新数据
        latest_data = get_latest_country_data(country_data, risk_type)
        if not latest_data:
            continue

        # 获取历史对比数据（前一个时间点的数据）
        previous_data = get_previous_country_data(
            country_data, risk_type, latest_data["data_time"]
        )

        # 计算风险变化趋势
        risk_trend = calculate_risk_trend(latest_data, previous_data, risk_type)

        # 添加历史信息
        latest_data["name"] = country
        latest_data["previous_risk"] = (
            previous_data.get("risk_level") if previous_data else None
        )
        latest_data["risk_trend"] = risk_trend
        latest_data["change_date"] = (
            previous_data.get("data_time") if previous_data else None
        )
        latest_data["risk_history"] = get_risk_history(country_data, risk_type)

        processed_data.append(latest_data)

    return processed_data


def get_previous_country_data(country_data, risk_type, current_time):
    """获取指定国家的前一个时间点数据"""
    # 过滤掉当前时间的数据，找到前一个时间点
    previous_data = [item for item in country_data if item.data_time != current_time]

    if not previous_data:
        return None

    # 按时间分组
    time_groups = defaultdict(list)
    for item in previous_data:
        time_groups[item.data_time].append(item)

    # 获取最新的前一个时间点
    sorted_times = sorted(time_groups.keys(), reverse=True)
    if not sorted_times:
        return None

    latest_previous_time = sorted_times[0]
    time_data = time_groups[latest_previous_time]

    # 选择风险等级最高的数据
    best_item = None
    max_risk_value = -1

    for item in time_data:
        if risk_type == "import":
            risk_level = item.import_risk_level
        else:
            risk_level = item.travel_risk_level

        risk_value = get_risk_value(risk_level)

        if risk_value > max_risk_value:
            max_risk_value = risk_value
            best_item = item

    if best_item:
        if risk_type == "import":
            risk_level = best_item.import_risk_level
        else:
            risk_level = best_item.travel_risk_level

        return {"risk_level": risk_level, "data_time": best_item.data_time}

    return None


def calculate_risk_trend(current_data, previous_data, risk_type):
    """计算风险变化趋势"""
    if not previous_data:
        return "stable"

    current_risk = get_risk_value(current_data.get("risk_level"))
    previous_risk = get_risk_value(previous_data.get("risk_level"))

    if current_risk > previous_risk:
        return "up"
    elif current_risk < previous_risk:
        return "down"
    else:
        return "stable"


def get_risk_history(country_data, risk_type, limit=6):
    """获取国家的风险历史记录（最近几个月）"""
    # 按时间分组
    time_groups = defaultdict(list)
    for item in country_data:
        if item.data_time:
            time_groups[item.data_time].append(item)

    # 获取最近的时间点
    sorted_times = sorted(time_groups.keys(), reverse=True)[:limit]

    history = []
    for data_time in sorted_times:
        time_data = time_groups[data_time]

        # 选择风险等级最高的数据
        best_item = None
        max_risk_value = -1

        for item in time_data:
            if risk_type == "import":
                risk_level = item.import_risk_level
            else:
                risk_level = item.travel_risk_level

            risk_value = get_risk_value(risk_level)

            if risk_value > max_risk_value:
                max_risk_value = risk_value
                best_item = item

        if best_item:
            if risk_type == "import":
                risk_level = best_item.import_risk_level
            else:
                risk_level = best_item.travel_risk_level

            history.append(
                {
                    "date": data_time,
                    "risk": risk_level,
                    "risk_value": get_risk_value(risk_level),
                }
            )

    return history
