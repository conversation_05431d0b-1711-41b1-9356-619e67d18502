from flask import jsonify, request
from model.models import db, Disease, Disaster, Country
from datetime import datetime, timedelta
import math

def handler():
    try:
        time_range = request.args.get('timeRange', 'month')  # 默认为月
        
        # 计算时间范围
        end_date = datetime.now()
        if time_range == 'week':
            start_date = end_date - timedelta(days=7)
        elif time_range == 'month':
            start_date = end_date - timedelta(days=30)
        else:  # year
            start_date = end_date - timedelta(days=365)
        
        # 获取在 news_diseases 表中存在的疾病数据
        top_diseases = db.session.query(
            Disease.name.label('disease_name'),
            Disaster.disaster.label('disease_value'),
            db.func.sum(Disaster.growth).label('total_infections')
        ).join(
            Disease,
            Disease.data_value == Disaster.disaster
        ).filter(
            Disaster.time >= start_date,
            Disaster.time <= end_date,
            Disaster.growth.isnot(None)
        ).group_by(
            Disease.name,
            Disaster.disaster
        ).order_by(
            db.desc('total_infections')
        ).limit(15).all()
        
        if not top_diseases:
            return jsonify({'diseases': [], 'points': []})
        
        # 创建疾病值到名称的映射
        disease_map = {d.disease_value: d.disease_name for d in top_diseases}
        disease_list = [d.disease_name for d in top_diseases]
        disease_values = [d.disease_value for d in top_diseases]
        
        # 获取这些疾病的所有记录
        outbreak_data = db.session.query(
            Disaster,
            Disease.name.label('disease_name')
        ).join(
            Disease,
            Disease.data_value == Disaster.disaster
        ).filter(
            Disaster.time >= start_date,
            Disaster.time <= end_date,
            Disaster.disaster.in_(disease_values),
            Disaster.growth.isnot(None)
        ).all()
        
        # 从数据库获取国家信息
        countries = Country.query.all()
        country_map = {country.data_value: country for country in countries}
        
        # 用于跟踪每个国家的点数量
        country_point_count = {}
        
        # 构建点数据
        points = []
        for record, disease_name in outbreak_data:
            try:
                country_info = country_map.get(record.cities)
                if country_info and record.growth is not None:
                    # 获取该国家已有的点数量
                    count = country_point_count.get(record.cities, 0)
                    country_point_count[record.cities] = count + 1
                    
                    # 根据点数量计算偏移角度和距离
                    angle = (count * 137.5) % 360  # 黄金角度，确保均匀分布
                    distance = 0.5 + (count * 0.2)  # 随距离逐渐增加偏移量
                    
                    # 计算偏移后的经纬度
                    lat = float(country_info.capital_latitude)
                    lon = float(country_info.capital_longitude)
                    
                    # 将角度转换为弧度
                    angle_rad = math.radians(angle)
                    
                    # 计算经纬度偏移
                    lat_offset = distance * math.cos(angle_rad) / 111.32
                    lon_offset = distance * math.sin(angle_rad) / (111.32 * math.cos(math.radians(lat)))
                    
                    # 添加偏移量
                    new_lat = lat + lat_offset
                    new_lon = lon + lon_offset
                    
                    point_data = [
                        new_lon,
                        new_lat,
                        float(record.growth or 0),
                        disease_name,
                        country_info.name,
                        record.time.strftime('%Y-%m-%d'),
                        int(record.deaths or 0)
                    ]
                    points.append(point_data)
            except Exception as e:
                continue
        
        response_data = {
            'diseases': disease_list,
            'points': points
        }
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in globe_data: {str(e)}")
        return jsonify({'error': str(e)}), 500 