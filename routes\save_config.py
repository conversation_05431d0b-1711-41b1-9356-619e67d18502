from flask import jsonify, request
import json
import os

def handler():
    requestData = request.json
    if all(key in requestData for key in ['api_endpoint', 'api_key', 'ai_model', 'prompt_text']):
        configToSave = {
            'api_endpoint': requestData['api_endpoint'],
            'api_key': requestData['api_key'],
            'ai_model': requestData['ai_model'],
            'prompt_text': requestData['prompt_text'],
        }
        try:
            current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_path = os.path.join(current_directory, 'api_config.json')
            
            with open(config_path, 'w', encoding='utf-8-sig') as f:
                json.dump(configToSave, f, ensure_ascii=False, indent=4)
            return jsonify({'status': 'success', 'message': '配置文件已成功保存.'}), 200
        except IOError as e:
            return jsonify({'status': 'error', 'message': '配置文件保存失败: ' + str(e)}), 500
    else:
        return jsonify({'status': 'error', 'message': 'Invalid configuration data received.'}), 400 