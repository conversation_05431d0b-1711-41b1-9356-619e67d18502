{% extends "main.html" %}

{% block content %}
<!-- 手动生成页面内容 -->
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/urlAI.css') }}">

    <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>
    <div class="ui container">

        <h2 class="ui top attached header">
            <i class="twitch icon"></i>
            手动调用AI生成
        </h2>
        <div class="ui attached segment">
            <div id="input-wrapper" class="ui form">
                <div class="input-container field">
                    <div class="ui left icon input">
                        <i class="linkify icon"></i>
                        <input type="url" name="urls[]" placeholder="请输入网址..." required>
                        <div class="ui left pointing red basic label hidden">这个网址不合法</div>
                    </div>
                </div>
            </div>
            <br>
            <button id="add-button" class="ui compact labeled icon blue button">
                <i class="plus icon"></i>
                新增链接</button>
            <button id="generateButton" class="ui compact labeled icon green button">
                <i class="pencil alternate icon"></i>
                AI生成
            </button>
        </div>


        <div id="url-list" class="ui ignored info message hidden">
            <div class="url-list-header">
                <h3 class="ui horizontal divider header">
                    <i class="clipboard outline icon"></i>
                    AI生成结果
                </h3>
                <div class="ui compact menu">
                    <a id="copy-button" class="item" data-content="复制生成内容" data-variation="inverted">
                        <i class="copy outline icon"></i>
                    </a>
                    <a id="download-button" class="item" data-content="下载生成内容" data-variation="inverted">
                        <i class="download icon"></i>
                    </a>
                </div>
            </div>
            <div class="ui horizontal divider"><i class="chevron down icon"></i>
            </div>
            <div id="urls-container"></div>
        </div>

        <div class="ui styled fluid accordion" style="margin-top: 15px;">
            <div class="active title">
                <h3><i class="dropdown icon"></i>
                    <i class="pencil alternate icon"></i>编辑提示词
                </h3>
            </div>
            <div class="active content">
                <div class="ui form">
                    <div id="prompt_text" class="field">
                        <textarea rows="22">{{ prompt_text }}</textarea>
                    </div>

                </div>
            </div>
        </div>
        <br>

        <div class="ui dimmer">
            <div class="ui text loader" style="font-size: 1.2em; line-height: 1.8;">
                <div style="font-size: 1.3em; margin-bottom: 0.5em;">正在进行AI生成，请耐心等待...</div>
                <br><br><br>
                <div style="font-size: 0.9em;">提示：请勿刷新页面！</div>
            </div>
        </div>

        <script>
            const maxInputs = 6;
            const wrapper = document.getElementById('input-wrapper');
            const addButton = document.getElementById('add-button');
            const generateButton = document.getElementById('generateButton');
            const urlList = document.getElementById('url-list');
            const urlsContainer = document.getElementById('urls-container');
            const promptTextArea = document.getElementById('prompt_text').querySelector('textarea');
            promptTextContent = promptTextArea.value;

            $(document).ready(function () {
                $('.ui.styled.fluid.accordion').accordion();
            });
            $(document).ready(function () {
                $('#copy-button').popup();
                $('#download-button').popup();
            });

            addButton.addEventListener('click', () => {
                const inputs = wrapper.getElementsByTagName('input');
                if (inputs.length < maxInputs) {
                    const newInputContainer = document.createElement('div');
                    newInputContainer.className = 'input-container field';
                    newInputContainer.innerHTML = `
                    <div class="ui left icon input">
                        <i class="linkify icon"></i>
                        <input type="url" name="urls[]" placeholder="请输入网址..." required>
                        <div class="ui left pointing red basic label hidden">这个网址不合法</div>
                    </div>`;
                    wrapper.appendChild(newInputContainer);
                    addBlurEvent(newInputContainer.querySelector('input'));
                } else {
                    alert('每次最多生成6条新闻.');
                }
            });

            generateButton.addEventListener('click', () => {
                const inputs = wrapper.getElementsByTagName('input');
                let allUrlsValid = true;
                let atLeastOneUrlEntered = false;
                let urls = [];

                Array.from(inputs).forEach(input => {
                    const urlErrorLabel = input.nextElementSibling;
                    if (input.value) {
                        atLeastOneUrlEntered = true;
                        if (!isValidUrl(input.value)) {
                            urlErrorLabel.classList.remove('hidden');
                            allUrlsValid = false;
                        } else {
                            urlErrorLabel.classList.add('hidden');
                            urls.push(input.value);
                        }
                    } else {
                        urlErrorLabel.classList.add('hidden');
                    }
                });

                if (allUrlsValid && atLeastOneUrlEntered) {
                    // 显示加载遮罩
                    $('.ui.dimmer').dimmer('show');

                    // 发送 AJAX 请求到后端
                    $.ajax({
                        url: '/urlAI',
                        method: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            urls: urls,
                            prompt_text: promptTextArea.value
                        }),
                        success: function (response) {
                            // 隐藏加载遮罩
                            $('.ui.dimmer').dimmer('hide');

                            // 更新 URL 列表
                            urlsContainer.innerHTML = '';
                            response.processed_urls.split('\n').forEach(url => {
                                const urlItem = document.createElement('div');
                                urlItem.textContent = url;
                                urlsContainer.appendChild(urlItem);
                            });
                            urlList.classList.remove('hidden');
                        },
                        error: function () {
                            // 隐藏加载遮罩
                            $('.ui.dimmer').dimmer('hide');
                            alert('生成过程中发生错误，请重试。');
                        }
                    });
                } else if (!atLeastOneUrlEntered) {
                    alert('请至少输入一条有效的新闻链接。');
                }
            });

            function isValidUrl(url) {
                const pattern = new RegExp('^(https?:\\/\\/)?' + // protocol
                    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
                    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
                    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
                    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
                    '(\\#[-a-z\\d_]*)?$', 'i'); // fragment locator
                return !!pattern.test(url);
            }

            function addBlurEvent(input) {
                input.addEventListener('blur', () => {
                    const urlErrorLabel = input.nextElementSibling;
                    if (input.value && !isValidUrl(input.value)) {
                        urlErrorLabel.classList.remove('hidden');
                    } else {
                        urlErrorLabel.classList.add('hidden');
                    }
                });
            }

            // Add blur event for the initial input field
            document.querySelectorAll('input[name="urls[]"]').forEach(addBlurEvent);
            document.getElementById('copy-button').addEventListener('click', function () {
                const urlsContainer = document.getElementById('urls-container');
                const urls = Array.from(urlsContainer.children).map(div => div.textContent).join('\n');
                const promptText = document.querySelector('#prompt_text textarea').value;
                //const content = promptText + '\n\n链接信息：\n' + urls;
                const content = urls;

                if (!content.trim()) {
                    showMessage('警告', '没有内容可以复制！', 'warning');
                    return;
                }

                const tempTextArea = document.createElement('textarea');
                tempTextArea.value = content;
                document.body.appendChild(tempTextArea);

                tempTextArea.select();
                tempTextArea.setSelectionRange(0, 99999);

                try {
                    const successful = document.execCommand('copy');
                    if (successful) {
                        showMessage('成功', '内容已复制到剪贴板！', 'success');
                    } else {
                        throw new Error('复制命令不可用');
                    }
                } catch (err) {
                    console.error('复制失败：', err);
                    showMessage('错误', '复制失败，请手动复制！', 'error');
                } finally {
                    document.body.removeChild(tempTextArea);
                }
            });

            document.getElementById('download-button').addEventListener('click', function () {
                const urlsContainer = document.getElementById('urls-container');
                const urls = Array.from(urlsContainer.children).map(div => div.textContent).join('\n');

                if (!urls.trim()) {
                    showMessage('警告', '没有内容可以下载！', 'warning');
                    return;
                }

                const blob = new Blob([urls], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'ai_generate.txt';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showMessage('成功', '内容已下载！', 'success');
            });

            function showMessage(title, content, type = 'info') {
                const messageContainer = document.getElementById('message-container');
                const messageElement = document.createElement('div');
                messageElement.className = `ui ${type} message`;
                messageElement.innerHTML = `
        <i class="close icon"></i>
        <div class="header">${title}</div>
        <p>${content}</p>
    `;
                messageContainer.appendChild(messageElement);

                // 添加关闭按钮功能
                const closeIcon = messageElement.querySelector('.close.icon');
                closeIcon.addEventListener('click', function () {
                    messageContainer.removeChild(messageElement);
                });

                // 3秒后自动关闭
                setTimeout(() => {
                    if (messageElement.parentNode === messageContainer) {
                        messageContainer.removeChild(messageElement);
                    }
                }, 3000);
            }
        </script>
{% endblock %}