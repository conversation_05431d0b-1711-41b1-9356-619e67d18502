from flask import jsonify, request
import requests
from bs4 import BeautifulSoup
import re
import traceback

def parse_disease_data(html_content, date, source_url):
    """解析疾病数据的函数"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        disease_data = []
        
        # 查找表格数据
        tables = soup.find_all('table')
        for table in tables:
            rows = table.find_all('tr')
            parent_disease = None
            is_first_row = True
            
            for row in rows:
                cells = row.find_all('td')
                if len(cells) >= 3:
                    name_cell = cells[0].find('p')
                    if not name_cell:
                        continue

                    text_indent = name_cell.get('style', '')
                    if 'text-indent' in text_indent:
                        indent_match = re.search(r'text-indent:\s*([\d.]+)pt', text_indent)
                        indent_value = float(indent_match.group(1)) if indent_match else 0
                    else:
                        indent_value = 0

                    # 移除所有上标元素
                    for sup in name_cell.find_all('sup'):
                        sup.decompose()
                    clean_disease_name = name_cell.get_text(strip=True)
                    
                    # 跳过表头行和合计行
                    if (is_first_row or clean_disease_name.find('合计') != -1 or 
                        clean_disease_name.find('总计') != -1 or 
                        clean_disease_name == '病名' or 
                        cells[1].get_text(strip=True) == '发病数' or 
                        cells[2].get_text(strip=True) == '死亡数'):
                        is_first_row = False
                        continue

                    new_cases = int(cells[1].get_text(strip=True).replace(',', '')) if cells[1].get_text(strip=True) else 0
                    deaths = int(cells[2].get_text(strip=True).replace(',', '')) if cells[2].get_text(strip=True) else 0
                    
                    # 通过text-indent判断是否为子疾病
                    is_sub_disease = indent_value > 30
                    
                    if not is_sub_disease:
                        # 这是父疾病
                        parent_disease = clean_disease_name
                        
                        # 添加父疾病记录
                        disease_data.append({
                            'date': date,
                            'diseaseName': parent_disease,
                            'subDiseaseName': '',
                            'location': '全国',
                            'sourceUrl': source_url,
                            'newCases': new_cases,
                            'deaths': deaths,
                            'cured': 0,
                            'subNewCases': 0,
                            'subDeaths': 0,
                            'subCured': 0
                        })
                    elif parent_disease:
                        # 这是子疾病
                        disease_data.append({
                            'date': date,
                            'diseaseName': parent_disease,
                            'subDiseaseName': clean_disease_name,
                            'location': '全国',
                            'sourceUrl': source_url,
                            'newCases': 0,
                            'deaths': 0,
                            'cured': 0,
                            'subNewCases': new_cases,
                            'subDeaths': deaths,
                            'subCured': 0
                        })

        return disease_data
    except Exception as e:
        print(f"Error parsing disease data: {str(e)}")
        traceback.print_exc()
        return []

def handler():
    """处理疾病数据解析的路由"""
    url = request.args.get('url')
    date = request.args.get('date')
    
    if not url or not date:
        return jsonify({'error': 'Missing required parameters'}), 400
        
    try:
        # 发送请求获取页面内容
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers)
        response.encoding = 'utf-8'
        
        if response.status_code != 200:
            return jsonify({'error': f'Failed to fetch page content: {response.status_code}'}), 500
            
        # 解析数据
        disease_data = parse_disease_data(response.text, date, url)
        
        if not disease_data:
            return jsonify({'error': 'No disease data found'}), 404
            
        return jsonify(disease_data)
        
    except Exception as e:
        print(f"Error in parse_disease_data_route: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500 