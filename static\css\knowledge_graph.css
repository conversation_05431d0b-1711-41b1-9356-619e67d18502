#graphContainer {
    width: 100%;
    height: 800px;
    border: 1px solid #ddd;
    background-color: #f8f9fa;
}

.card {
    margin-bottom: 1rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 按钮组样式 */
.graph-controls {
    position: absolute;
    right: 1rem;
    top: 0.5rem;
    display: flex;
    gap: 8px;
    z-index: 10;
}

/* 调整Semantic UI按钮在当前环境下的样式 */
.graph-controls .ui.button {
    margin: 0 4px;
    font-size: 0.85rem;
}

.graph-controls .ui.blue.button {
    background-color: #2185d0;
}

.graph-controls .ui.blue.button:hover {
    background-color: #1678c2;
}

.graph-controls .ui.basic.button {
    box-shadow: 0 0 0 1px rgba(34, 36, 38, 0.15) inset;
}

/* 确保图标和文本对齐 */
.graph-controls .ui.labeled.icon.button > .icon {
    background-color: rgba(0, 0, 0, 0.05);
}

/* 调整按钮间距 */
.graph-controls .ui.button:not(:last-child) {
    margin-right: 6px;
}

.detail-item {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #eee;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: bold;
    color: #666;
    margin-bottom: 0.25rem;
}

.detail-value {
    color: #333;
}

.confidence-badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    background-color: #17a2b8;
    color: white;
}

/* 加载中样式 */
.loading-overlay {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 1000;
}

.spinner-border {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: 0.25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border .75s linear infinite;
}

.text-primary {
    color: #4e73df !important;
}

.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* 错误消息样式 */
.error-message {
    display: none;
    color: #dc3545;
    padding: 15px;
    text-align: center;
    font-weight: 500;
}
