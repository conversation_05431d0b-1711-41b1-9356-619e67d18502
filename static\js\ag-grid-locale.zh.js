const AG_GRID_LOCALE_ZH = {
    // 设置过滤器
    selectAll: '(选择全部)',
    selectAllSearchResults: '(选择所有搜索结果)',
    addCurrentSelectionToFilter: '将当前选择添加到过滤器',
    searchOoo: '搜索...',
    blanks: '(空白)',
    noMatches: '无匹配项',

    // 数字过滤器 & 文本过滤器
    filterOoo: '过滤器...',
    equals: '等于',
    notEqual: '不等于',
    blank: '空白',
    notBlank: '非空白',
    empty: '选择一个',

    // 数字过滤器
    lessThan: '小于',
    greaterThan: '大于',
    lessThanOrEqual: '小于或等于',
    greaterThanOrEqual: '大于或等于',
    inRange: '在范围内',
    inRangeStart: '从',
    inRangeEnd: '到',

    // 文本过滤器
    contains: '包含',
    notContains: '不包含',
    startsWith: '以...开始',
    endsWith: '以...结束',

    // 日期过滤器
    dateFormatOoo: 'yyyy-mm-dd',
    before: '之前',
    after: '之后',

    // 过滤器条件
    andCondition: 'AND',
    orCondition: 'OR',

    // 过滤器按钮
    applyFilter: '应用',
    resetFilter: '重置',
    clearFilter: '清除',
    cancelFilter: '取消',

    // 过滤器标题
    textFilter: '文本过滤器',
    numberFilter: '数字过滤器',
    dateFilter: '日期过滤器',
    setFilter: '设置过滤器',

    // 列分组过滤器
    groupFilterSelect: '选择字段：',

    // 高级过滤器
    advancedFilterContains: '包含',
    advancedFilterNotContains: '不包含',
    advancedFilterTextEquals: '等于',
    advancedFilterTextNotEqual: '不等于',
    advancedFilterStartsWith: '以...开始',
    advancedFilterEndsWith: '以...结束',
    advancedFilterBlank: '为空',
    advancedFilterNotBlank: '不为空',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: '为真',
    advancedFilterFalse: '为假',
    advancedFilterAnd: 'AND',
    advancedFilterOr: 'OR',
    advancedFilterApply: '应用',
    advancedFilterBuilder: '构建器',
    advancedFilterValidationMissingColumn: '缺少列',
    advancedFilterValidationMissingOption: '缺少选项',
    advancedFilterValidationMissingValue: '缺少值',
    advancedFilterValidationInvalidColumn: '列未找到',
    advancedFilterValidationInvalidOption: '选项未找到',
    advancedFilterValidationMissingQuote: '值缺少结束引号',
    advancedFilterValidationNotANumber: '值不是数字',
    advancedFilterValidationInvalidDate: '值不是有效日期',
    advancedFilterValidationMissingCondition: '缺少条件',
    advancedFilterValidationJoinOperatorMismatch: '条件内的连接运算符必须相同',
    advancedFilterValidationInvalidJoinOperator: '连接运算符未找到',
    advancedFilterValidationMissingEndBracket: '缺少结束括号',
    advancedFilterValidationExtraEndBracket: '多余的结束括号',
    advancedFilterValidationMessage: '表达式有错误。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '表达式有错误。${variable} 在表达式末尾。',
    advancedFilterBuilderTitle: '高级过滤器',
    advancedFilterBuilderApply: '应用',
    advancedFilterBuilderCancel: '取消',
    advancedFilterBuilderAddButtonTooltip: '添加过滤器或分组',
    advancedFilterBuilderRemoveButtonTooltip: '移除',
    advancedFilterBuilderMoveUpButtonTooltip: '向上移动',
    advancedFilterBuilderMoveDownButtonTooltip: '向下移动',
    advancedFilterBuilderAddJoin: '添加分组',
    advancedFilterBuilderAddCondition: '添加过滤器',
    advancedFilterBuilderSelectColumn: '选择列',
    advancedFilterBuilderSelectOption: '选择选项',
    advancedFilterBuilderEnterValue: '输入值...',
    advancedFilterBuilderValidationAlreadyApplied: '当前过滤器已应用。',
    advancedFilterBuilderValidationIncomplete: '不是所有条件都完整。',
    advancedFilterBuilderValidationSelectColumn: '必须选择列。',
    advancedFilterBuilderValidationSelectOption: '必须选择选项。',
    advancedFilterBuilderValidationEnterValue: '必须输入值。',

    // 侧边栏
    columns: '列',
    filters: '过滤器',

    // 列工具面板
    pivotMode: '聚合模式',
    groups: '行分组',
    rowGroupColumnsEmptyMessage: '拖拽至此设置行分组',
    values: '值',
    valueColumnsEmptyMessage: '拖拽至此进行聚合',
    pivots: '列标签',
    pivotColumnsEmptyMessage: '拖拽至此设置列标签',

    // 默认分组列标题
    group: '分组',

    // 行拖拽
    rowDragRow: '行',
    rowDragRows: '行',

    // 其他
    loadingOoo: '加载中...',
    loadingError: 'ERR',
    noRowsToShow: '没有行显示',
    enabled: '已启用',

    // 菜单
    pinColumn: '固定列',
    pinLeft: '固定到左侧',
    pinRight: '固定到右侧',
    noPin: '不固定',
    valueAggregation: '值聚合',
    noAggregation: '无',
    autosizeThiscolumn: '自适应此列',
    autosizeAllColumns: '自适应所有列',
    groupBy: '分组',
    ungroupBy: '取消分组',
    ungroupAll: '取消所有分组',
    addToValues: '添加 ${variable} 到值',
    removeFromValues: '从值中移除 ${variable}',
    addToLabels: '添加 ${variable} 到标签',
    removeFromLabels: '从标签中移除 ${variable}',
    resetColumns: '重置列',
    expandAll: '展开所有行分组',
    collapseAll: '关闭所有行分组',
    copy: '复制',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: '复制包含标题',
    copyWithGroupHeaders: '复制包含分组标题',
    cut: '剪切',
    paste: '粘贴',
    ctrlV: 'Ctrl+V',
    export: '导出',
    csvExport: 'CSV 导出',
    excelExport: 'Excel 导出',

    // 企业菜单聚合和状态栏
    sum: '求和',
    first: '第一',
    last: '最后',
    min: '最小值',
    max: '最大值',
    none: '无',
    count: '计数',
    avg: '平均值',
    filteredRows: '已过滤',
    selectedRows: '已选择',
    totalRows: '总行数',
    totalAndFilteredRows: '行数',
    more: '更多',
    to: ' 至 ',
    of: '#总共:',
    page: '页',
    pageLastRowUnknown: '?',
    nextPage: '下一页',
    lastPage: '最后一页',
    firstPage: '第一页',
    previousPage: '上一页',
    pageSizeSelectorLabel: '每页显示：',
    footerTotal: '总计',

    // 聚合
    pivotColumnGroupTotals: '总计',

    // 企业菜单（图表）
    pivotChartAndPivotMode: '聚合图表 & 聚合模式',
    pivotChart: '聚合图表',
    chartRange: '图表范围',

    columnChart: '柱状图',
    groupedColumn: '分组',
    stackedColumn: '堆叠',
    normalizedColumn: '100% 堆叠',

    barChart: '条形图',
    groupedBar: '分组',
    stackedBar: '堆叠',
    normalizedBar: '100% 堆叠',

    pieChart: '饼图',
    pie: '饼图',
    doughnut: '甜甜圈图',

    line: '折线图',

    xyChart: 'XY（散点）',
    scatter: '散点',
    bubble: '气泡图',

    areaChart: '面积图',
    area: '面积',
    stackedArea: '堆叠',
    normalizedArea: '100% 堆叠',

    histogramChart: '直方图',
    histogramFrequency: "频率",

    combinationChart: '组合',
    columnLineCombo: '柱状 & 折线',
    AreaColumnCombo: '面积 & 柱状',

    // 图表
    pivotChartTitle: '聚合图表',
    rangeChartTitle: '范围图表',
    settings: '设置',
    data: '数据',
    format: '格式',
    categories: '类别',
    defaultCategory: '（无）',
    series: '系列',
    xyValues: 'XY 值',
    paired: '配对模式',
    axis: '轴',
    navigator: '导航器',
    color: '颜色',
    thickness: '厚度',
    xType: 'X 类型',
    automatic: '自动',
    category: '类别',
    number: '数字',
    time: '时间',
    autoRotate: '自动旋转',
    xRotation: 'X 轴旋转',
    yRotation: 'Y 轴旋转',
    ticks: '刻度',
    width: '宽度',
    height: '高度',
    length: '长度',
    padding: '内边距',
    spacing: '间距',
    chart: '图表',
    title: '标题',
    titlePlaceholder: '图表标题 - 双击编辑',
    background: '背景',
    font: '字体',
    top: '顶部',
    right: '右侧',
    bottom: '底部',
    left: '左侧',
    labels: '标签',
    size: '大小',
    minSize: '最小尺寸',
    maxSize: '最大尺寸',
    legend: '图例',
    position: '位置',
    markerSize: '标记大小',
    markerStroke: '标记描边',
    markerPadding: '标记内边距',
    itemSpacing: '项目间距',
    itemPaddingX: '项目横向内边距',
    itemPaddingY: '项目纵向内边距',
    layoutHorizontalSpacing: '水平间距',
    layoutVerticalSpacing: '垂直间距',
    strokeWidth: '描边宽度',
    lineDash: '线条虚线',
    offset: '偏移',
    offsets: '偏移量',
    tooltips: '工具提示',
    callout: '呼叫线',
    markers: '标记',
    shadow: '阴影',
    blur: '模糊',
    xOffset: 'X 偏移',
    yOffset: 'Y 偏移',
    lineWidth: '线宽',
    normal: '正常',
    bold: '加粗',
    italic: '斜体',
    boldItalic: '加粗斜体',
    predefined: '预定义',
    fillOpacity: '填充不透明度',
    strokeOpacity: '线条不透明度',
    histogramBinCount: '直方图桶数',
    columnGroup: '柱状图组',
    barGroup: '条形图组',
    pieGroup: '饼图组',
    lineGroup: '折线图组',
    scatterGroup: '散点图组',
    areaGroup: '面积图组',
    histogramGroup: '直方图组',
    combinationGroup: '组合图组',
    groupedColumnTooltip: '分组',
    stackedColumnTooltip: '堆叠',
    normalizedColumnTooltip: '100% 堆叠',
    groupedBarTooltip: '分组',
    stackedBarTooltip: '堆叠',
    normalizedBarTooltip: '100% 堆叠',
    pieTooltip: '饼图',
    doughnutTooltip: '甜甜圈图',
    lineTooltip: '折线图',
    groupedAreaTooltip: '面积',
    stackedAreaTooltip: '堆叠',
    normalizedAreaTooltip: '100% 堆叠',
    scatterTooltip: '散点图',
    bubbleTooltip: '气泡图',
    histogramTooltip: '直方图',
    columnLineComboTooltip: '柱状 & 折线',
    areaColumnComboTooltip: '面积 & 柱状',
    customComboTooltip: '自定义组合',
    noDataToChart: '没有数据可供图表展示。',
    pivotChartRequiresPivotMode: '聚合图表需要启用聚合模式。',
    chartSettingsToolbarTooltip: '菜单',
    chartLinkToolbarTooltip: '与网格链接',
    chartUnlinkToolbarTooltip: '与网格取消链接',
    chartDownloadToolbarTooltip: '下载图表',
    seriesChartType: '系列图表类型',
    seriesType: '系列类型',
    secondaryAxis: '次要轴',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. 级别 ${variable}. 按 ENTER 编辑。',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. 级别 ${variable}. ${variable} 按 ENTER 编辑。',
    ariaAdvancedFilterBuilderList: '高级过滤器构建器列表',
    ariaAdvancedFilterBuilderFilterItem: '过滤器条件',
    ariaAdvancedFilterBuilderGroupItem: '过滤器组',
    ariaAdvancedFilterBuilderColumn: '列',
    ariaAdvancedFilterBuilderOption: '选项',
    ariaAdvancedFilterBuilderValueP: '值',
    ariaAdvancedFilterBuilderJoinOperator: '连接运算符',
    ariaAdvancedFilterInput: '高级过滤器输入',
    ariaChecked: '已勾选',
    ariaColumn: '列',
    ariaColumnGroup: '列组',
    ariaColumnFiltered: '列已过滤',
    ariaColumnSelectAll: '切换全选列',
    ariaDateFilterInput: '日期过滤器输入',
    ariaDefaultListName: '列表',
    ariaFilterColumnsInput: '过滤器列输入',
    ariaFilterFromValue: '过滤器起始值',
    ariaFilterInput: '过滤器输入',
    ariaFilterList: '过滤器列表',
    ariaFilterToValue: '过滤器结束值',
    ariaFilterValue: '过滤器值',
    ariaFilterMenuOpen: '打开过滤器菜单',
    ariaFilteringOperator: '过滤运算符',
    ariaHidden: '隐藏',
    ariaIndeterminate: '不确定',
    ariaInputEditor: '输入编辑器',
    ariaMenuColumn: '按 CTRL ENTER 打开列菜单',
    ariaRowDeselect: '按 SPACE 取消选择此行',
    ariaRowSelectAll: '按 SPACE 切换所有行选择',
    ariaRowToggleSelection: '按 SPACE 切换行选择',
    ariaRowSelect: '按 SPACE 选择此行',
    ariaSearch: '搜索',
    ariaSortableColumn: '按 ENTER 排序',
    ariaToggleVisibility: '按 SPACE 切换可见性',
    ariaToggleCellValue: '按 SPACE 切换单元格值',
    ariaUnchecked: '未勾选',
    ariaVisible: '可见',
    ariaSearchFilterValues: '搜索过滤器值',
    ariaPageSizeSelectorLabel: '页面大小',

    // ARIA 标签用于 Drop Zones
    ariaRowGroupDropZonePanelLabel: '行组',
    ariaValuesDropZonePanelLabel: '值',
    ariaPivotDropZonePanelLabel: '列标签',
    ariaDropZoneColumnComponentDescription: '按 DELETE 移除',
    ariaDropZoneColumnValueItemDescription: '按 ENTER 更改聚合类型',
    ariaDropZoneColumnGroupItemDescription: '按 ENTER 排序',
    // 用于聚合 Drop Zone，格式：{聚合}{ariaDropZoneColumnComponentAggFuncSeparator}{列名}
    ariaDropZoneColumnComponentAggFuncSeparator: ' of ',
    ariaDropZoneColumnComponentSortAscending: '升序',
    ariaDropZoneColumnComponentSortDescending: '降序',

    // ARIA 标签用于对话框
    ariaLabelColumnMenu: '列菜单',
    ariaLabelCellEditor: '单元格编辑器',
    ariaLabelDialog: '对话框',
    ariaLabelSelectField: '选择字段',
    ariaLabelRichSelectField: '富选择字段',
    ariaLabelTooltip: '工具提示',
    ariaLabelContextMenu: '上下文菜单',
    ariaLabelSubMenu: '子菜单',
    ariaLabelAggregationFunction: '聚合函数',
    ariaLabelAdvancedFilterAutocomplete: '高级过滤器自动完成',
    ariaLabelAdvancedFilterBuilderAddField: '高级过滤器构建器添加字段',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高级过滤器构建器列选择字段',
    ariaLabelAdvancedFilterBuilderOptionSelectField: '高级过滤器构建器选项选择字段',
    ariaLabelAdvancedFilterBuilderJoinSelectField: '高级过滤器构建器连接运算符选择字段',

    // ARIA 标签用于侧边栏
    ariaColumnPanelList: '列列表',
    ariaFilterPanelList: '过滤器列表',

    // 数字格式（状态栏，分页面板）
    thousandSeparator: ',',
    decimalSeparator: '.',

    // 数据类型
    true: '真',
    false: '假',
    invalidDate: '无效日期',
    invalidNumber: '无效数字',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',
}