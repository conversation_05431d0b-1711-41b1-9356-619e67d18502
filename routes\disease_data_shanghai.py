from flask import jsonify, request
import requests
from bs4 import BeautifulSoup
import re
import traceback
import pandas as pd
import io
import os

def parse_disease_data(html_content, date, source_url):
    """解析疾病数据的函数"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        disease_data = []
        
        # 查找包含Excel文件链接的区域
        attachment_links = soup.select("ul li a")
        excel_link = None
        
        for link in attachment_links:
            href = link.get('href', '')
            text = link.get_text(strip=True)
            if href.endswith('.xls') or href.endswith('.xlsx') or '法定报告传染病发病数统计表' in text:
                excel_link = href
                break
        
        if not excel_link:
            print(f"No Excel file link found in the HTML content for {source_url}")
            return []
        
        # 检查链接格式，确保有完整的URL
        if excel_link.startswith('/'):
            base_url = "https://wsjkw.sh.gov.cn"
            excel_url = f"{base_url}{excel_link}"
        else:
            excel_url = excel_link
            
        print("正在从上海卫健委官网下载和解析Excel文件...")
        
        # 下载Excel文件
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        excel_response = requests.get(excel_url, headers=headers, timeout=30)
        
        if excel_response.status_code != 200:
            print(f"Failed to download Excel file: {excel_response.status_code}")
            return []
        
        # 读取Excel文件
        try:
            # 首先尝试直接用pandas读取
            excel_data = pd.read_excel(io.BytesIO(excel_response.content))
        except Exception as e:
            print(f"Error reading Excel file directly, trying with temp file: {str(e)}")
            # 如果直接读取失败，保存到临时文件然后读取
            temp_file = "temp_excel_file.xls"
            with open(temp_file, "wb") as f:
                f.write(excel_response.content)
            excel_data = pd.read_excel(temp_file)
            # 读取完成后删除临时文件
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        # 处理Excel数据
        # 删除完全为空的行和列
        excel_data = excel_data.dropna(how='all').dropna(axis=1, how='all')
        
        # 查找表头行 - 优先查找包含"病名"的行
        header_row_idx = None
        
        # 方法1：查找精确匹配"病名"的单元格
        for idx, row in excel_data.iterrows():
            for cell_value in row.values:
                if pd.notna(cell_value) and str(cell_value).strip() == "病名":
                    header_row_idx = idx
                    break
            if header_row_idx is not None:
                break
        
        # 方法2：如果没找到精确匹配，尝试查找包含"病名"且同行有"户籍"或"发病数"的行
        if header_row_idx is None:
            for idx, row in excel_data.iterrows():
                row_values = [str(val).strip() for val in row.values if pd.notna(val)]
                row_str = ' '.join(row_values)
                if "病名" in row_str and any(term in row_str for term in ["户籍", "发病数", "例数"]):
                    header_row_idx = idx
                    break
        
        if header_row_idx is None:
            print("Could not find header row in Excel file")
            return []
            
        # 重置表头并删除前面的行
        new_header = excel_data.iloc[header_row_idx]
        excel_data = excel_data[header_row_idx+1:]
        
        # 确保表头不包含NaN值，替换为有意义的列名
        for i in range(len(new_header)):
            if pd.isna(new_header[i]):
                new_header[i] = f"Column_{i}"
        
        excel_data.columns = new_header
        excel_data = excel_data.reset_index(drop=True)
        
        # 查找"病名"列
        disease_col = None
        for col in excel_data.columns:
            col_str = str(col).strip()
            if col_str == "病名" or "疾病" in col_str:
                disease_col = col
                break
        
        if disease_col is None and len(excel_data.columns) > 0:
            disease_col = excel_data.columns[0]
        
        if disease_col is None:
            print("Could not find disease name column in Excel file")
            return []

        local_hukou_col = None
        nonlocal_hukou_col = None
        total_col = None

        start_search_idx = 0
        if disease_col in excel_data.columns.tolist():
            start_search_idx = excel_data.columns.tolist().index(disease_col) + 1
        else:
            start_search_idx = 1 if len(excel_data.columns) > 0 else 0

        for i in range(start_search_idx, len(excel_data.columns)):
            current_col_obj = excel_data.columns[i]
            current_col_str = str(current_col_obj).strip()

            if not local_hukou_col and ("本市户籍" in current_col_str):
                local_hukou_col = current_col_obj
                if "发病" in current_col_str:
                    pass 
            
            if not nonlocal_hukou_col and ("非本市户籍" in current_col_str or "外来人口" in current_col_str or "外省市" in current_col_str):
                nonlocal_hukou_col = current_col_obj
                if "发病" in current_col_str:
                    pass

            if not total_col and ("合计" in current_col_str or "总计" in current_col_str):
                total_col = current_col_obj
                if "发病" in current_col_str:
                     pass
        
        if local_hukou_col is None:
            if len(excel_data.columns) > start_search_idx:
                local_hukou_col = excel_data.columns[start_search_idx]
            else:
                print(f"警告：未能定位\"本市户籍\"数据列。")
        
        if nonlocal_hukou_col is None:
            if len(excel_data.columns) > start_search_idx + 1:
                nonlocal_hukou_col = excel_data.columns[start_search_idx + 1]
            else:
                print(f"警告：未能定位\"非本市户籍\"数据列。")
            
        if total_col is None:
            if len(excel_data.columns) > start_search_idx + 2:
                total_col = excel_data.columns[start_search_idx + 2]
            # else:
                # print(f"Warning: Could not find or fallback for total_col. Needed at least {start_search_idx + 3} columns. Will attempt to calculate sum.")

        # --- 从末尾移除无效行 --- 
        if local_hukou_col and nonlocal_hukou_col and not excel_data.empty:
            while not excel_data.empty:
                last_row_index = excel_data.index[-1]
                last_row_series = excel_data.iloc[-1]
                
                val_local_hukou = last_row_series.get(local_hukou_col)
                val_nonlocal_hukou = last_row_series.get(nonlocal_hukou_col)

                def is_value_empty(value):
                    if pd.isna(value):
                        return True
                    if isinstance(value, str) and value.strip() == "":
                        return True
                    return False

                is_local_empty = is_value_empty(val_local_hukou)
                is_nonlocal_empty = is_value_empty(val_nonlocal_hukou)

                if is_local_empty and is_nonlocal_empty:
                    excel_data = excel_data.drop(last_row_index)
                else:
                    break 
        elif excel_data.empty:
            pass
        else:
            print("警告：因未能准确定位户籍数据列，无法执行末尾无效行清理。")

        current_parent = None
        parent_stack = [] 
        last_indent_level = 0 
        
        for idx, row in excel_data.iterrows():
            disease_name_from_cell = str(row[disease_col]).strip() if pd.notna(row[disease_col]) else ""
            disease_name_from_cell = disease_name_from_cell.replace(" ", "") 
            
            if not disease_name_from_cell or disease_name_from_cell.lower() == "nan":
                continue

            if "合计" in disease_name_from_cell or "总计" in disease_name_from_cell:
                continue
            
            processed_disease_name = disease_name_from_cell
            is_sub_disease = False
            
            original_name_before_qizhong_strip = processed_disease_name
            if processed_disease_name.startswith("其中:") or processed_disease_name.startswith("其中："):
                parts = re.split(r"其中[:：]", processed_disease_name, 1)
                if len(parts) > 1 and parts[1].strip():
                    processed_disease_name = parts[1].strip().replace(" ", "")
                    is_sub_disease = True 
                else:
                    continue
            
            if not processed_disease_name or processed_disease_name.lower() == "nan" or \
               processed_disease_name == "其中" or processed_disease_name == "其中:":
                continue

            bracket_pattern = re.compile(r'^\s*[\(（]\s*\d+\s*[\)）]')

            if bracket_pattern.match(processed_disease_name):
                is_sub_disease = True
            
            if not is_sub_disease and (original_name_before_qizhong_strip.startswith('  ') or \
                                       original_name_before_qizhong_strip.startswith('\t') or \
                                       original_name_before_qizhong_strip.startswith('　')):
                if not bracket_pattern.match(processed_disease_name):
                    is_sub_disease = True
            
            if parent_stack and parent_stack[-1] and '肝炎' in parent_stack[-1]:
                if '肝' in processed_disease_name and '肝炎' not in processed_disease_name and len(processed_disease_name) <= 4:
                    is_sub_disease = True
                elif processed_disease_name == "肝炎（未分型）".replace(" ", ""):
                    is_sub_disease = True
            
            final_disease_name_for_record = processed_disease_name.strip().replace(" ", "")
            
            local_hukou_val, nonlocal_hukou_val, total_val = 0, 0, 0
            try:
                if local_hukou_col and pd.notna(row[local_hukou_col]):
                    local_hukou_val = int(float(str(row[local_hukou_col]).replace(',', '')))
                else: local_hukou_val = 0
            except (ValueError, TypeError): local_hukou_val = 0
            
            try:
                if nonlocal_hukou_col and pd.notna(row[nonlocal_hukou_col]):
                    nonlocal_hukou_val = int(float(str(row[nonlocal_hukou_col]).replace(',', '')))
                else: nonlocal_hukou_val = 0
            except (ValueError, TypeError): nonlocal_hukou_val = 0
            
            try:
                if total_col and pd.notna(row[total_col]):
                    total_val = int(float(str(row[total_col]).replace(',', '')))
                elif local_hukou_col and nonlocal_hukou_col: 
                    total_val = local_hukou_val + nonlocal_hukou_val
                else: 
                    total_val = local_hukou_val + nonlocal_hukou_val 
            except (ValueError, TypeError):
                 total_val = local_hukou_val + nonlocal_hukou_val

            if is_sub_disease:
                parent_name_to_use = current_parent if current_parent else (parent_stack[-1] if parent_stack else None)
                
                if parent_name_to_use:
                    record = {
                        'date': date,
                        'diseaseName': parent_name_to_use.replace(" ", ""),
                        'subDiseaseName': final_disease_name_for_record,
                        'local_hukou': local_hukou_val,
                        'nonlocal_hukou': nonlocal_hukou_val,
                        'total': total_val,
                        'location': '上海',
                        'sourceUrl': source_url,
                    }
                    disease_data.append(record)
                else:
                    record = {
                        'date': date,
                        'diseaseName': final_disease_name_for_record, 
                        'subDiseaseName': "", 
                        'local_hukou': local_hukou_val,
                        'nonlocal_hukou': nonlocal_hukou_val,
                        'total': total_val,
                        'location': '上海',
                        'sourceUrl': source_url,
                    }
                    disease_data.append(record)
            else:
                current_parent = final_disease_name_for_record
                parent_stack = [final_disease_name_for_record] 
                                
                record = {
                    'date': date,
                    'diseaseName': final_disease_name_for_record,
                    'subDiseaseName': "", 
                    'local_hukou': local_hukou_val,
                    'nonlocal_hukou': nonlocal_hukou_val, 
                    'total': total_val,
                    'location': '上海',
                    'sourceUrl': source_url,
                }
                disease_data.append(record)
        
        print(f"分析完毕，共提取 {len(disease_data)} 条有效数据。")
        return disease_data
    except Exception as e:
        print(f"Error parsing disease data: {str(e)}")
        traceback.print_exc()
        return []

def test_excel_parsing(file_path):
    """测试Excel文件解析的函数"""
    try:
        print(f"Testing Excel parsing for file: {file_path}")
        excel_data = pd.read_excel(file_path)
        
        print(f"Excel file shape: {excel_data.shape}")
        print(f"Excel columns: {excel_data.columns.tolist()}")
        print("First 10 rows:")
        print(excel_data.head(10).to_string())
        
        excel_data = excel_data.dropna(how='all').dropna(axis=1, how='all')
        
        header_row_idx = None
        
        for idx, row in excel_data.iterrows():
            for cell_value in row.values:
                if pd.notna(cell_value) and str(cell_value).strip() == "病名":
                    header_row_idx = idx
                    print(f"Found exact header row at index {idx} with cell '病名'")
                    break
            if header_row_idx is not None:
                break
        
        if header_row_idx is None:
            for idx, row in excel_data.iterrows():
                row_values = [str(val).strip() for val in row.values if pd.notna(val)]
                row_str = ' '.join(row_values)
                print(f"Row {idx}: {row_str}")
                if "病名" in row_str and any(term in row_str for term in ["户籍", "发病数", "例数"]):
                    header_row_idx = idx
                    print(f"Found header row by keyword match at index {idx}: {row_str}")
                    break
        
        if header_row_idx is not None:
            new_header = excel_data.iloc[header_row_idx]
            
            for i in range(len(new_header)):
                if pd.isna(new_header[i]):
                    new_header[i] = f"Column_{i}"
                    
            excel_data = excel_data[header_row_idx+1:]
            excel_data.columns = new_header
            excel_data = excel_data.reset_index(drop=True)
            
            print(f"After header processing, columns: {excel_data.columns.tolist()}")
            print(excel_data.head(10).to_string())
            
            disease_col = None
            local_hukou_col = None
            nonlocal_hukou_col = None
            total_col = None
            
            for col in excel_data.columns:
                col_str = str(col).strip()
                if col_str == "病名" or "疾病" in col_str:
                    disease_col = col
                    print(f"Found disease name column: {col}")
                elif "本市户籍" in col_str:
                    local_hukou_col = col
                    print(f"Found local hukou column: {col}")
                elif "非本市户籍" in col_str:
                    nonlocal_hukou_col = col
                    print(f"Found nonlocal hukou column: {col}")
                elif "合计" in col_str:
                    total_col = col
                    print(f"Found total column: {col}")
            
            print("\nSample data for disease name analysis:")
            for idx in range(min(20, len(excel_data))):
                disease_name = str(excel_data.iloc[idx][disease_col]).strip() if disease_col and pd.notna(excel_data.iloc[idx][disease_col]) else ""
                if disease_name:
                    bracket_pattern = re.compile(r'^\s*[\(（]\s*\d+\s*[\)）]')
                    is_sub = "Yes" if bracket_pattern.match(disease_name) or disease_name.startswith('  ') else "No"
                    print(f"Row {idx}, Disease: '{disease_name}', Is Sub: {is_sub}")
        
        return True
    except Exception as e:
        print(f"Error testing Excel parsing: {str(e)}")
        traceback.print_exc()
        return False

def handler():
    """处理疾病数据解析的路由"""
    url = request.args.get('url')
    date = request.args.get('date')
    test_file = request.args.get('test_file')
    
    if test_file:
        success = test_excel_parsing(test_file)
        return jsonify({'success': success})
    
    if not url or not date:
        return jsonify({'error': 'Missing required parameters'}), 400
        
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(url, headers=headers, timeout=15)
        response.encoding = 'utf-8'
        
        if response.status_code != 200:
            return jsonify({'error': f'Failed to fetch page content: {response.status_code}'}), 500
            
        disease_data = parse_disease_data(response.text, date, url)
        
        if not disease_data and disease_data != []:
            pass
            
        return jsonify(disease_data)
        
    except Exception as e:
        print(f"Error in handler: {str(e)}")
        traceback.print_exc()
        return jsonify([]) 