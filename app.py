from flask import (
    Flask,
    request,
    jsonify,
    render_template,
    redirect,
    url_for,
    make_response,
    session,
    Response,
    stream_with_context,
)
from flask_login import (
    <PERSON><PERSON><PERSON><PERSON>ger,
    UserMixin,
    login_user,
    login_required,
    logout_user,
    current_user,
)
from model.models import (
    db,
    User,
    Disaster,
    Country,
    Disease,
    GlobalRiskAssessment,
)  # 导入db和User模型
from db.db_config import Config  # 导入数据库配置
from werkzeug.security import generate_password_hash
import os, json, requests
from datetime import datetime, timedelta
import random
import math
from flask import flash
from routes.searchBing import search_bing_news
from routes.searchDuckduckgo import search_duckduckgo_news
import routes.call_chatgpt as call_chatgpt
from deep_translator import GoogleTranslator
import routes.call_chatgpt_url as call_chatgpt_url, routes.graph_generator as graph_generator
import routes.get_news as get_news
from routes.fetch_news import handler as fetch_news_handler
import pandas as pd
from routes.graph_generator import generate_graph
import traceback
import math
from flask import send_file
from routes.disease_prediction import DiseasePrediction  # 更新导入路径
from routes.save_config import handler as save_config_handler
from routes.get_config import handler as get_config_handler
from routes.ai_generate import handler as ai_generate_handler
from routes.get_news_content import handler as get_news_content_handler
from routes.url_ai import handler as url_ai_handler
from routes.manual_input import handler as manual_input_handler
from routes.url_figure import handler as url_figure_handler
from routes.globe_data import handler as globe_data_handler
from routes.predict_disease import handler as predict_disease_handler
from routes.fetch_news import handler as fetch_news_handler
from routes.get_dict_config import handler as get_dict_config_handler
from routes.disease_network_data import handler as disease_network_data_handler
from routes.disease_network_data import filter_handler as disease_network_filter_handler
from routes.cdc_news_china import handler as cdc_news_china_handler
from routes.disease_data_china import handler as parse_disease_data_china_handler
from routes.import_disease_data_china import (
    handler as import_disease_data_china_handler,
)
from routes.get_diseases_china import handler as get_diseases_china_handler
from routes.predict_disease_china import handler as predict_disease_china_handler
from routes.sarima_prediction_china import handler as sarima_prediction_china_handler
from routes.cdc_news_shanghai import handler as cdc_news_shanghai_handler
from routes.disease_data_shanghai import handler as parse_disease_data_shanghai_handler
from routes.import_disease_data_shanghai import (
    handler as import_disease_data_shanghai_handler,
)
from routes.cdc_risk_reports_china import handler as cdc_risk_reports_china_handler
from routes.cdc_report_ai_analysis import handler as cdc_report_ai_analysis_handler
from routes.get_diseases_shanghai import handler as get_diseases_shanghai_handler
from routes.predict_disease_shanghai import handler as predict_disease_shanghai_handler
from routes.knowledge_graph import (
    progress_messages as kg_progress_messages,
)  # Import the deque
from routes.global_risk_assessment import handler as global_risk_assessment_handler
import time  # For SSE sleep

app = Flask(__name__)
app.secret_key = "soNiceDay"  # 设置了一个安全的密钥
app.config["PERMANENT_SESSION_LIFETIME"] = timedelta(minutes=30)  # 30分钟超时

app.config.from_object(Config)  # 使用配置对象
db.init_app(app)  # 初始化应用

# Flask-Login setup
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = "login"

API_KEY = "408ee4c04bb74fc1b9d2843cc55e132c"


@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, user_id)


@app.route("/check_session", methods=["GET"])
def check_session():
    if "user_name" in session:
        return jsonify({"session": True})
    else:
        message = "登录已超时，请重新登录。"
        return jsonify({"session": False, "message": message, "category": "danger"})


# 登录页面路由
@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        username = request.form["username"]
        password = request.form["password"]
        keep_login = request.form.get("keep_login") == "true"

        user = User.query.filter_by(user_id=username).first()
        if user:
            if user.status == 0:
                return jsonify(
                    {
                        "loginSuccess": False,
                        "message": "该账户已被禁用，请联系管理员。",
                        "category": "danger",
                    }
                )

            if user.check_password(password):
                login_user(user, remember=keep_login)
                if keep_login:
                    session.permanent = True
                    app.permanent_session_lifetime = timedelta(days=30)
                else:
                    session.permanent = True
                    app.permanent_session_lifetime = timedelta(days=1)

                session["user_name"] = user.user_name
                current_time = datetime.now()
                session["login_date"] = current_time.strftime("%Y/%m/%d %H:%M:%S")

                next_page = session.get("next_url")
                if next_page:
                    session.pop("next_url", None)
                    return jsonify(
                        {
                            "loginSuccess": True,
                            "message": "登录成功！",
                            "category": "success",
                            "redirect": next_page,
                        }
                    )
                return jsonify(
                    {
                        "loginSuccess": True,
                        "message": "登录成功！",
                        "category": "success",
                    }
                )

        return jsonify(
            {
                "loginSuccess": False,
                "message": "用户名或密码不正确。",
                "category": "danger",
            }
        )

    return render_template("login.html")


# 注销路由
@app.route("/logout")
@login_required
def logout():
    next_url = request.args.get("next") or request.referrer
    if next_url:
        session["next_url"] = next_url
    logout_user()
    session.pop("user_name", None)
    session.pop("login_date", None)
    return redirect(url_for("login"))


# 未登录用户处理器
@login_manager.unauthorized_handler
def unauthorized():
    session["next_url"] = request.url
    return redirect(url_for("login"))


# 保护路由，确保用户已登录
@app.route("/")
@login_required
def index():
    user_name = session.get("user_name", "游客")
    login_date = session.get("login_date", "")
    return render_template("main.html", user_name=user_name, login_date=login_date)


# 新闻检索页面路由
@app.route("/newsSearch")
@login_required
def news_search():
    user_name = session.get("user_name", "游客")
    login_date = session.get("login_date", "")
    return render_template(
        "newsSearch.html", user_name=user_name, login_date=login_date
    )


# 新闻检索
@app.route("/fetch_news", methods=["GET"])
@login_required
def fetch_news():
    return fetch_news_handler()


# 保存配置文件
@app.route("/save_config", methods=["POST"])
@login_required
def save_config():
    return save_config_handler()


# 获取配置文件
@app.route("/get_config", methods=["GET"])
@login_required
def get_config():
    return get_config_handler()


# AI生成事件
@app.route("/ai_generate", methods=["POST"])
@login_required
def ai_generate():
    return ai_generate_handler()


# 获取新闻正文
@app.route("/get_news_content", methods=["POST"])
@login_required
def get_news_content():
    return get_news_content_handler()


# 手动调用AI生成
@app.route("/urlAI", methods=["GET", "POST"])
@login_required
def urlAI():
    return url_ai_handler()


# 生成趋势图
@app.route("/urlFigure", methods=["GET", "POST"])
def urlFigure():
    return url_figure_handler()


# 手动输入生成图片
@app.route("/manualInput", methods=["POST"])
def manual_input():
    return manual_input_handler()


@app.route("/globe")
def globe():
    return render_template("globe_view.html")


@app.route("/globe_data")
def globe_data():
    return globe_data_handler()


@app.route("/urlAsk", methods=["GET", "POST"])
@login_required
def urlAsk():
    return render_template("urlAsk.html")


@app.route("/newsNow", methods=["GET", "POST"])
@login_required
def newsNow():
    return render_template("newsNow.html")


@app.route("/openwebui", methods=["GET", "POST"])
def openwebui():
    return render_template("openwebui.html")


@app.route("/chatBot", methods=["GET", "POST"])
@login_required
def chatBot():
    return render_template("chatBot.html")


@app.route("/disease_network")
def disease_network():
    return render_template("disease_network.html")


@app.route("/disease_network_data")
def disease_network_data():
    return disease_network_data_handler()


@app.route("/disease_network_filter")
def disease_network_filter():
    return disease_network_filter_handler()


@app.route("/get_dict_config")
def get_dict_config():
    return get_dict_config_handler()


@app.route("/disease_prediction")
def disease_prediction():
    return render_template("disease_prediction.html")


@app.route("/predict_disease", methods=["POST"])
def predict_disease():
    return predict_disease_handler()


@app.route("/disease_prediction_china")
@login_required
def disease_prediction_china():
    return render_template("disease_prediction_china.html")


@app.route("/get_diseases_china")
def get_diseases_china():
    return get_diseases_china_handler()


@app.route("/predict_disease_china", methods=["POST"])
def predict_disease_china():
    return sarima_prediction_china_handler()


@app.route("/cdc_news_china")
@login_required
def cdc_news_china():
    if current_user.user_id != "ssc":
        return render_template("404.html"), 404
    return render_template("cdc_news_china.html")


@app.route("/api/cdc_news_china")
@login_required
def get_cdc_news_china():
    if current_user.user_id != "ssc":
        return jsonify({"error": "Unauthorized"}), 401
    return cdc_news_china_handler()


# 新页面：重点传染病和突发公卫事件风险评估报告
@app.route("/cdc_risk_reports_china")
@login_required
def cdc_risk_reports_china():
    return render_template("cdc_risk_reports_china.html")


@app.route("/api/cdc_risk_reports_china")
@login_required
def get_cdc_risk_reports_china():
    return cdc_risk_reports_china_handler()


@app.route("/api/cdc_report_ai_analysis", methods=["POST"])
@login_required
def cdc_report_ai_analysis():
    return cdc_report_ai_analysis_handler()


@app.route("/api/parse_disease_data_china")
@login_required
def parse_disease_data_china():
    if current_user.user_id != "ssc":
        return jsonify({"error": "Unauthorized"}), 401
    return parse_disease_data_china_handler()


@app.route("/api/import_disease_data_china", methods=["POST"])
@login_required
def import_disease_data_china():
    if current_user.user_id != "ssc":
        return jsonify({"error": "Unauthorized"}), 401
    return import_disease_data_china_handler()


@app.route("/cdc_news_shanghai")
@login_required
def cdc_news_shanghai():
    return render_template("cdc_news_shanghai.html")


@app.route("/api/cdc_news_shanghai")
@login_required
def get_cdc_news_shanghai():
    return cdc_news_shanghai_handler()


@app.route("/api/parse_disease_data_shanghai")
@login_required
def parse_disease_data_shanghai():
    return parse_disease_data_shanghai_handler()


@app.route("/api/import_disease_data_shanghai", methods=["POST"])
@login_required
def import_disease_data_shanghai():
    return import_disease_data_shanghai_handler()


@app.route("/get_diseases_shanghai")
@login_required
def get_diseases_shanghai():
    return get_diseases_shanghai_handler()


@app.route("/disease_prediction_shanghai")
@login_required
def disease_prediction_shanghai():
    return render_template("disease_prediction_shanghai.html")


@app.route("/predict_disease_shanghai", methods=["POST"])
def predict_disease_shanghai():
    return predict_disease_shanghai_handler()


@app.route("/knowledge-graph", methods=["GET"])
@login_required
def knowledge_graph():
    return render_template("knowledge_graph.html")


@app.route("/api/knowledge-graph", methods=["GET", "POST"])
@login_required
def api_knowledge_graph():
    from routes.knowledge_graph import handler as knowledge_graph_handler

    return knowledge_graph_handler()


@app.route("/api/knowledge-graph/progress")
@login_required
def knowledge_graph_progress():
    def generate():
        print("SSE stream /api/knowledge-graph/progress started.")
        while True:
            try:
                if kg_progress_messages:
                    message = kg_progress_messages.popleft()
                    yield message
                    if '"progress": 100' in message:
                        print(
                            "SSE stream: Detected 100% progress, attempting to close stream."
                        )
                        break
                else:
                    yield ": keepalive\n\n"

            except IndexError:
                yield ": keepalive\n\n"

            time.sleep(0.1)

        print("SSE stream /api/knowledge-graph/progress finishing generate() function.")

    return Response(stream_with_context(generate()), mimetype="text/event-stream")


@app.route("/global_risk_assessment")
@login_required
def global_risk_assessment():
    return render_template("global_risk_assessment.html")


@app.route("/api/global_risk_assessment")
@login_required
def api_global_risk_assessment():
    return global_risk_assessment_handler()


@app.errorhandler(404)
def page_not_found(e):
    return render_template("404.html"), 404


@app.after_request
def add_security_headers(response):
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["Cache-Control"] = "no-store, must-revalidate"
    response.headers["Server"] = "Flask"
    response.headers["Secure-Cookie"] = "true"

    if request.path.startswith("/static/"):
        response.headers["Cache-Control"] = "public, max-age=31536000"

    response.headers.pop("Pragma", None)

    return response


# 用户管理页面路由
@app.route("/userManagement")
@login_required
def user_management():
    if current_user.role != "admin":
        return render_template("404.html"), 404
    return render_template("userManagement.html")


# 用户管理API
@app.route("/api/users", methods=["GET"])
@login_required
def get_users():
    if current_user.role != "admin":
        return jsonify({"error": "Unauthorized"}), 401

    users = User.query.all()
    return jsonify(
        {
            "users": [
                {
                    "id": user.id,
                    "user_id": user.user_id,
                    "user_name": user.user_name,
                    "email": user.email,
                    "phone": user.phone,
                    "role": user.role,
                    "department": user.department,
                    "status": user.status,
                    "remark": user.remark,
                    "created_time": user.created_time.strftime("%Y-%m-%d %H:%M:%S")
                    if user.created_time
                    else None,
                    "last_login_time": user.last_login_time.strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    if user.last_login_time
                    else None,
                }
                for user in users
            ]
        }
    )


@app.route("/api/users/<int:user_id>", methods=["GET"])
@login_required
def get_user(user_id):
    if current_user.role != "admin":
        return jsonify({"error": "Unauthorized"}), 401

    user = User.query.get_or_404(user_id)
    return jsonify(
        {
            "user": {
                "id": user.id,
                "user_id": user.user_id,
                "user_name": user.user_name,
                "email": user.email,
                "phone": user.phone,
                "role": user.role,
                "department": user.department,
                "status": user.status,
                "remark": user.remark,
            }
        }
    )


@app.route("/api/users/check-availability", methods=["POST"])
@login_required
def check_user_availability():
    if current_user.role != "admin":
        return jsonify({"error": "Unauthorized"}), 401

    data = request.get_json()
    user_id = data.get("user_id", "").strip()

    if not user_id:
        return jsonify({"available": False, "message": "用户名不能为空"})

    existing_user = User.query.filter_by(user_id=user_id).first()
    if existing_user:
        return jsonify({"available": False, "message": "该用户名已存在"})

    return jsonify({"available": True, "message": "用户名可用"})


@app.route("/api/users", methods=["POST"])
@login_required
def create_or_update_user():
    if current_user.role != "admin":
        return jsonify({"error": "Unauthorized"}), 401

    data = request.get_json()

    existing_user = User.query.filter_by(user_id=data["user_id"]).first()
    if existing_user:
        # 更新用户
        try:
            existing_user.user_name = data["user_name"]
            if data.get("password"):  # 只在提供密码时更新密码
                salt = os.urandom(16).hex()
                existing_user.salt = salt
                existing_user.pwd = generate_password_hash(data["password"] + salt)
            existing_user.email = data.get("email")
            existing_user.phone = data.get("phone")
            existing_user.role = data["role"]
            existing_user.department = data.get("department")
            existing_user.status = data["status"]
            existing_user.remark = data.get("remark")

            db.session.commit()
            return jsonify({"success": True, "message": "用户更新成功"})
        except Exception as e:
            db.session.rollback()
            return jsonify({"success": False, "message": f"用户更新失败: {str(e)}"})
    else:
        # 创建新用户 - 再次检查用户名是否重复
        if User.query.filter_by(user_id=data["user_id"]).first():
            return jsonify(
                {"success": False, "message": "该用户名已存在，请选择其他用户名"}
            )

        try:
            salt = os.urandom(16).hex()
            new_user = User(
                user_id=data["user_id"],
                user_name=data["user_name"],
                salt=salt,
                pwd=generate_password_hash(data["password"] + salt),
                email=data.get("email"),
                phone=data.get("phone"),
                role=data["role"],
                department=data.get("department"),
                status=data["status"],
                remark=data.get("remark"),
            )
            db.session.add(new_user)
            db.session.commit()
            return jsonify({"success": True, "message": "用户创建成功"})
        except Exception as e:
            db.session.rollback()
            return jsonify({"success": False, "message": f"用户创建失败: {str(e)}"})


@app.route("/api/users/<int:user_id>", methods=["DELETE"])
@login_required
def delete_user(user_id):
    if current_user.role != "admin":
        return jsonify({"error": "Unauthorized"}), 401

    user = User.query.get_or_404(user_id)
    if user.user_id == current_user.user_id:
        return jsonify({"success": False, "message": "不能删除当前登录用户"})

    try:
        db.session.delete(user)
        db.session.commit()
        return jsonify({"success": True, "message": "用户删除成功"})
    except Exception as e:
        db.session.rollback()
        return jsonify({"success": False, "message": f"用户删除失败: {str(e)}"})


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=83, debug=True)
