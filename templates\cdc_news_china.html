<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>中国疾控中心全国法定传染病疫情情况</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
    <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }
        .main-container {
            width: 90%;
            height: 90%;
            margin: 2% auto;
            display: flex;
            flex-direction: column;
        }
        .header-container {
            margin-bottom: 20px;
        }
        .grid-container {
            flex: 1;
            width: 100%;
            min-height: 500px;
        }
        /* 修改模态窗口样式 */
        .ui.fullscreen.modal {
            display: flex !important;
            flex-direction: column;
            height: 90vh !important;
            margin: 5vh auto !important;
        }
        .ui.fullscreen.modal > .content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: auto;
        }
        .ui.fullscreen.modal .description {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        #diseaseDetailsGrid {
            height: 700px !important;
            width: 100% !important;
        }
        #myGrid {
            height: 100% !important;
            width: 100% !important;
        }
        /* Toast 消息样式 */
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        .toast-message {
            background-color: #fff;
            color: #000;
            padding: 15px 25px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            display: none;
        }
        .toast-message.success {
            border-left: 4px solid #21ba45;
        }
        .toast-message.error {
            border-left: 4px solid #db2828;
        }
        .toast-message.info {
            border-left: 4px solid #2185d0;
        }
    </style>
</head>

<body>
    <!-- Toast 消息容器 -->
    <div id="toast-container"></div>

    <div class="main-container">
        <div class="header-container">
            <h2 class="ui header">
                <i class="hospital icon"></i>
                <div class="content">
                    中国疾控中心全国法定传染病疫情情况
                    <div class="sub header">数据实时获取</div>
                </div>
            </h2>
            <button class="ui primary button" id="manualUrlButton">
                <i class="linkify icon"></i>
                手动输入URL
            </button>
        </div>

        <div class="grid-container">
            <div id="myGrid" style="width: 100%; height: 100%;" class="ag-theme-quartz"></div>
        </div>
    </div>

    <!-- 详细信息模态窗口 -->
    <div class="ui fullscreen modal" id="detailModal">
        <i class="close icon"></i>
        <div class="header">
            详细信息
        </div>
        <div class="content">
            <div class="description">
                <div class="ui header" id="modalTitle"></div>
                <p id="modalSummary"></p>
                <div class="ui divider"></div>
                <div class="ui list">
                    <div class="item">
                        <i class="calendar icon"></i>
                        <div class="content" id="modalDate"></div>
                    </div>
                    <div class="item">
                        <i class="linkify icon"></i>
                        <div class="content">
                            <a id="modalUrl" target="_blank">查看原文</a>
                        </div>
                    </div>
                </div>
                <div class="ui divider"></div>
                <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;">
                    <button class="ui primary button" id="importToDbButton">
                        <i class="database icon"></i>
                        导入数据库
                    </button>
                </div>
                <div id="diseaseDetailsGrid" class="ag-theme-quartz" style="width: 100%; height: 600px;"></div>
            </div>
        </div>
        <div class="actions">
            <button class="ui red labeled icon button" id="closeModalButton">
                <i class="close icon"></i>
                关闭
            </button>
        </div>
    </div>

    <!-- URL输入模态窗口 -->
    <div class="ui tiny modal" id="urlInputModal">
        <i class="close icon"></i>
        <div class="header">
            输入URL
        </div>
        <div class="content">
            <div class="ui form">
                <div class="field">
                    <label>URL地址</label>
                    <input type="url" id="manualUrlInput" placeholder="请输入完整的URL地址">
                </div>
                <div class="field">
                    <label>发布日期</label>
                    <input type="date" id="manualDateInput">
                </div>
            </div>
        </div>
        <div class="actions">
            <button class="ui black deny button">
                取消
            </button>
            <button class="ui positive button" id="confirmUrlButton">
                确定
            </button>
        </div>
    </div>

    <script>
        // Toast 消息函数
        function showToast(message, type = 'info', duration = 3000) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast-message ${type}`;
            toast.textContent = message;
            
            container.appendChild(toast);
            
            // 显示消息
            $(toast).fadeIn();
            
            // 设置定时器，自动隐藏消息
            setTimeout(() => {
                $(toast).fadeOut(() => {
                    container.removeChild(toast);
                });
            }, duration);
        }

        // 替换所有 $('body').toast() 调用
        function showMessage(message, type = 'info') {
            showToast(message, type);
        }

        // AG Grid配置
        const gridOptions = {
            pagination: true,
            paginationPageSize: 20,
            paginationPageSizeSelector: [10, 20, 50],
            localeText: AG_GRID_LOCALE_ZH,

            columnDefs: [
                {
                    headerName: "序号",
                    valueGetter: "node.rowIndex + 1",
                    width: 80,
                    filter: false,
                    suppressSizeToFit: true,
                    cellStyle: { 
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center'
                    }
                },
                {
                    headerName: "标题",
                    field: "title",
                    width: 300,
                    suppressSizeToFit: true,
                    autoHeight: true,
                    cellStyle: { 
                        'text-align': 'left', 
                        'white-space': 'normal', 
                        'line-height': '20px',
                        'display': 'flex',
                        'align-items': 'center',
                        'padding': '10px'
                    }
                },
                {
                    headerName: "发布日期",
                    field: "date",
                    width: 120,
                    cellStyle: { 
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center'
                    }
                },
                {
                    headerName: "摘要",
                    field: "summary",
                    flex: 1,
                    autoHeight: true,
                    cellStyle: { 
                        'text-align': 'left', 
                        'white-space': 'normal', 
                        'line-height': '20px',
                        'display': 'flex',
                        'align-items': 'center',
                        'padding': '10px'
                    }
                },
                {
                    headerName: "原网址",
                    field: "url",
                    width: 100,
                    cellRenderer: function(params) {
                        return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><a href="' + params.value + '" target="_blank" class="ui basic blue button mini" style="margin: 0;">查看原文</a></div>';
                    },
                    cellStyle: { 'padding': '5px' }
                },
                {
                    headerName: "操作",
                    width: 100,
                    cellRenderer: function(params) {
                        return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><button class="ui primary basic button mini viewDetails" style="margin: 0;">查看详情</button></div>';
                    },
                    onCellClicked: function(params) {
                        if (params.event.target.classList.contains('viewDetails')) {
                            showDetails(params.data);
                        }
                    },
                    cellStyle: { 'padding': '5px' }
                }
            ],
            defaultColDef: {
                sortable: true,
                filter: true,
                resizable: true
            },
            rowHeight: 60,
            suppressHorizontalScroll: true  // 禁用水平滚动
        };

        let mainGrid;
        let detailsGrid;

        // 初始化表格
        document.addEventListener('DOMContentLoaded', function() {
            const gridDiv = document.querySelector('#myGrid');
            mainGrid = agGrid.createGrid(gridDiv, gridOptions);

            // 加载数据
            fetch('/api/cdc_news_china')
                .then(response => response.json())
                .then(data => {
                    mainGrid.setGridOption('rowData', data);
                    setTimeout(() => {
                        mainGrid.sizeColumnsToFit();
                    }, 200);
                })
                .catch(error => console.error('Error:', error));
        });

        // 手动URL输入按钮点击事件
        $('#manualUrlButton').on('click', function() {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            $('#manualDateInput').val(today);
            
            $('#urlInputModal')
                .modal({
                    closable: false,
                    onApprove: function() {
                        return false; // 防止模态窗口自动关闭
                    }
                })
                .modal('show');
        });

        // 确认URL按钮点击事件
        $('#confirmUrlButton').on('click', function() {
            const url = $('#manualUrlInput').val();
            const date = $('#manualDateInput').val();
            
            if (!url) {
                showMessage('请输入URL地址', 'error');
                return;
            }
            if (!date) {
                showMessage('请输入发布日期', 'error');
                return;
            }

            // 关闭URL输入模态窗口
            $('#urlInputModal').modal('hide');

            // 创建一个模拟的数据对象
            const mockData = {
                url: url,
                date: date,
                title: '手动输入的URL',
                summary: ''
            };

            // 显示详情模态窗口
            showDetails(mockData);
            
            // 清空输入框
            $('#manualUrlInput').val('');
        });

        // 显示详细信息
        function showDetails(data) {
            $('#modalTitle').text(data.title);
            $('#modalDate').text(data.date);
            $('#modalSummary').text(data.summary);
            $('#modalUrl').attr('href', data.url);
            
            const diseaseGridOptions = {
                columnDefs: [
                    { headerName: "发布时间", field: "date", width: 100 },
                    { headerName: "疾病名称", field: "diseaseName", width: 120 },
                    { headerName: "子疾病名称", field: "subDiseaseName", width: 120 },
                    { headerName: "地点", field: "location", width: 100 },
                    { headerName: "源网址", field: "sourceUrl", width: 120 },
                    { headerName: "新增人数", field: "newCases", width: 100 },
                    { headerName: "死亡人数", field: "deaths", width: 100 },
                    { headerName: "治愈人数", field: "cured", width: 100 },
                    { headerName: "子疾病新增人数", field: "subNewCases", width: 120 },
                    { headerName: "子疾病死亡人数", field: "subDeaths", width: 120 },
                    { headerName: "子疾病治愈人数", field: "subCured", width: 120 }
                ],
                defaultColDef: {
                    sortable: true,
                    filter: true,
                    resizable: true
                },
                localeText: AG_GRID_LOCALE_ZH,
                pagination: true,
                paginationPageSize: 50,
                domLayout: 'normal'
            };

            let currentDiseaseData = [];  // 存储当前表格数据

            // 显示模态窗口
            $('#detailModal')
                .modal({
                    onShow: function() {
                        // 在模态窗口显示后初始化表格
                        setTimeout(() => {
                            const gridDiv = document.querySelector('#diseaseDetailsGrid');
                            if (detailsGrid) {
                                gridDiv.innerHTML = '';
                            }
                            detailsGrid = agGrid.createGrid(gridDiv, diseaseGridOptions);
                            
                            // 从后端获取解析好的数据
                            fetch(`/api/parse_disease_data_china?url=${encodeURIComponent(data.url)}&date=${encodeURIComponent(data.date)}`)
                                .then(response => response.json())
                                .then(parsedData => {
                                    currentDiseaseData = parsedData;
                                    detailsGrid.setGridOption('rowData', currentDiseaseData);
                                    setTimeout(() => {
                                        detailsGrid.sizeColumnsToFit();
                                    }, 200);
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    $('body').toast({
                                        class: 'error',
                                        message: '获取数据失败：' + error.message,
                                        showProgress: 'bottom',
                                        displayTime: 3000
                                    });
                                });
                        }, 100);
                    },
                    observeChanges: true
                })
                .modal('show');

            // 导入数据库按钮点击事件
            $('#importToDbButton').off('click').on('click', function() {
                if (currentDiseaseData && currentDiseaseData.length > 0) {
                    importData(currentDiseaseData, false);
                }
            });

            // 导入数据函数
            function importData(data, force = false) {
                const url = force ? 
                    '/api/import_disease_data_china?force=true' : 
                    '/api/import_disease_data_china';

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    if (!result.success && result.message === 'exists') {
                        // 显示确认对话框
                        const confirmModal = $('<div class="ui basic modal">')
                            .append($('<div class="header">').text('数据已存在'))
                            .append(
                                $('<div class="content">').append(
                                    $('<p>').text(`数据库中已存在 ${result.date} 的数据，是否覆盖？`)
                                )
                            )
                            .append(
                                $('<div class="actions">').append(
                                    $('<div class="ui red basic cancel inverted button">').append(
                                        $('<i class="remove icon">'),
                                        '否'
                                    ),
                                    $('<div class="ui green ok inverted button">').append(
                                        $('<i class="checkmark icon">'),
                                        '是'
                                    )
                                )
                            );

                        confirmModal
                            .modal({
                                closable: false,
                                onDeny: function() {
                                    showMessage('已取消导入操作', 'info');
                                },
                                onApprove: function() {
                                    importData(data, true);
                                }
                            })
                            .modal('show');
                    } else {
                        // 显示导入结果提示
                        const message = result.success ? 
                            `成功导入 ${result.imported_count} 条数据` : 
                            '导入失败：' + result.message;
                        
                        showMessage(message, result.success ? 'success' : 'error');
                    }
                })
                .catch(error => {
                    console.error('Error importing data:', error);
                    showMessage('导入失败：' + error.message, 'error');
                });
            }
        }

        // 关闭模态窗口
        $('#closeModalButton').on('click', function() {
            $('#detailModal').modal('hide');
        });

        // 处理窗口大小改变
        window.addEventListener('resize', function() {
            if (mainGrid) {
                mainGrid.sizeColumnsToFit();
            }
        });
    </script>
</body>

</html> 