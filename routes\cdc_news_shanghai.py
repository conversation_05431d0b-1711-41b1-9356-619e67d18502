import requests
from bs4 import BeautifulSoup
from flask import jsonify, request
import concurrent.futures
import traceback

def fetch_page(page_url, headers):
    """Fetch a single page of CDC news"""
    try:
        response = requests.get(page_url, headers=headers, timeout=10)
        response.encoding = 'utf-8'
        if response.status_code != 200:
            return []
        
        soup = BeautifulSoup(response.text, 'html.parser')
        news_items = soup.select('ul.uli16.nowrapli.list-date.trout-region-list li')
        news_data = []
        
        for item in news_items:
            title_elem = item.select_one('a')
            if not title_elem:
                continue
            
            title = title_elem.get_text(strip=True)
            date_span = item.select_one('span.time')
            date = date_span.get_text(strip=True) if date_span else ''
            
            base_url = "https://wsjkw.sh.gov.cn"
            relative_url = title_elem.get('href', '')
            full_url = f"{base_url}{relative_url}" if relative_url.startswith('/') else f"{base_url}/{relative_url}"
            
            news_data.append({
                'title': title,
                'date': date,
                'summary': '',
                'url': full_url
            })
        
        return news_data
    except Exception as e:
        print(f"Error fetching page {page_url}: {str(e)}")
        return []

def fetch_cdc_news(requested_page=None):
    base_url = "https://wsjkw.sh.gov.cn/yqxx/index"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    target_page_url = ""
    if requested_page and requested_page > 1:
        target_page_url = f"{base_url}_{requested_page}.html"
    else:
        # Default to page 1 if requested_page is 1, None, or invalid
        target_page_url = f"{base_url}.html"
        
    try:
        print(f"Fetching data from: {target_page_url}") # Optional: for logging
        news_data = fetch_page(target_page_url, headers)
        
        # Sorting is still good if a single page returns items out of order, though less critical now.
        news_data.sort(key=lambda x: x['date'], reverse=True)
        
        return jsonify(news_data)
        
    except Exception as e:
        print(f"Error in fetch_cdc_news for page {requested_page}: {str(e)}")
        traceback.print_exc()
        return jsonify([]) # Return empty array on error

def handler():
    """Handle request for Shanghai CDC news data"""
    try:
        page_str = request.args.get('page')
        requested_page = None
        if page_str:
            try:
                page_int = int(page_str)
                if page_int > 0:
                    requested_page = page_int
                else:
                    # Optional: Log invalid page number request, e.g., page=0 or page=-1
                    print(f"Invalid page number requested: {page_str}. Defaulting to page 1.")
            except ValueError:
                # Optional: Log invalid page format, e.g., page=abc
                print(f"Invalid page format: {page_str}. Defaulting to page 1.")

        return fetch_cdc_news(requested_page=requested_page)
    except Exception as e:
        print(f"Unhandled error in cdc_news_shanghai handler: {str(e)}")
        traceback.print_exc()
        # Always return a JSON array even on error to avoid AG Grid failure
        return jsonify([]) 