from flask import jsonify, request, render_template
from model.models import db, Disaster, Country, Disease
import pandas as pd
from routes.graph_generator import generate_graph

def handler():
    if request.method == 'POST':
        # 从请求中获取 JSON 数据
        data = request.get_json()
        
        # 获取日期范围、国家和传染病名
        startDate = data.get('startDate')
        endDate = data.get('endDate')
        country = data.get('country')
        disease = data.get('disease')

        print(f"开始日期: {startDate}, 结束日期: {endDate},国家: {country}, 传染病: {disease}")

        # 查询数据库
        query = Disaster.query.filter(
            Disaster.time >= startDate,
            Disaster.time <= endDate
        )

        if country:  # 如果国家不为空，则添加条件
            query = query.filter(Disaster.cities == country)

        if disease:  # 如果传染病不为空，则添加条件
            query = query.filter(Disaster.disaster == disease)

        results = query.all()

        # 检查查询结果是否为空
        if len(results) == 0:
            return jsonify({'status': 'no_data'})
        
        # 将结果转换为字典列表
        data_set = [
            {
                'time': result.time.strftime('%Y-%m-%d'),
                'cities': result.cities,
                'disaster': result.disaster,
                'growth': result.growth,
                'deaths': result.deaths,
                'cures': result.cures
            }
            for result in results
        ]
        df = pd.DataFrame(data_set)

        # 生成图表
        graph_image = generate_graph(df)

        return jsonify({'status': 'success', 'graph_image': graph_image, 'data_set': data_set})

    else: 
        # 从数据库获取国家和疾病列表
        countries = Country.query.all()
        diseases = Disease.query.all()
        return render_template('urlFigure.html', countries=countries, diseases=diseases) 