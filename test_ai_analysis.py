#!/usr/bin/env python3
"""
测试AI分析功能的脚本（使用模拟PDF内容）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from routes.cdc_report_analyzer import get_analysis_prompt, format_analysis_result, generate_html_report

def test_ai_analysis():
    """测试AI分析功能"""
    
    # 使用您提供的25年7月份报告内容作为测试数据
    test_pdf_content = """
2025 年 7 月中国需关注的突发公共卫生事件风险评估
摘要：目的 评估2025年7月在我国（不含香港、澳门特别行政区和台湾地区，
下同）发生或者可能由境外输入的突发公共卫生事件风险。 方法 根据国内外
突发公共卫生事件报告及重点传染病监测等各种资料和部门通报信息，采用专
家会商法，并通过视频会议形式邀请各省（自治区、直辖市）疾病预防控制中
心专家参与评估。 结果 2025年7月突发公共卫生事件数可能比6月有所减少，
以传染病类事件为主。 登革热境外输入及跨地区传播风险上升，Ⅰ类地区及部
分Ⅱ类地区发生聚集性疫情的风险较高；基孔肯雅热等其他蚊媒传染病的输入
和本土传播风险上升。 预计7月仍将出现霍乱散发病例。发热伴血小板减少综
合征病例以散发为主，人传人导致的聚集性疫情风险也较高。 7月仍为食物中
毒事件高发期，毒蘑菇、有毒动植物和微生物污染是主要风险。 7－8月高温中
暑发病存在上升风险，男性、婴幼儿和老年人是中暑的高风险人群。 新型冠状
病毒感染疫情将继续呈现下降或波动下降趋势。 我国持续存在发生人感染禽流
感散发疫情的可能。 猴痘Ⅱb亚分支疫情仍将保持低水平波动态势，Ⅰa、Ⅰb
亚分支病例存在输入及续发传播风险。 7月我国全面进入主汛期，洪涝灾害受
影响地区存在水源性、食源性疾病以及虫媒传染病发病上升风险。 结论 对登
革热、霍乱、发热伴血小板减少综合征、食物中毒、高温中暑、新型冠状病毒
感染、人感染禽流感、猴痘及洪涝灾害等予以关注。 
关键词 突发公共卫生事件; 传染病疫情; 风险评估

2.1.1 重点关注
2.1.1.1 登革热
2025 年截至 7 月 1 日，全国报告病例数较 2024 年同期升高 41.8%，较 2019
年同期降低 63.5%。病例分布在 23 个省份，其中 13.9%为本地病例，由广东、云
南、湖南和浙江省报告，以登革病毒 1 型为主。境外输入病例主要来自印度尼西
亚、老挝、柬埔寨等东南亚国家，登革病毒 1～4 型均有报告，以 1 型和 2 型为
主。93.4%的病例为 20～64 岁人群，男女性别比为 2.5∶1，职业分布以商业服
务、农民、家务及待业和工人为主。

2.1.1.2 霍乱
2025 年截至 6 月 30 日，全国报告确诊病例 4 例、病原携带者 1 例，2024 年
同期无病例报告。疫情均由食源性传播引起。

2.1.1.3 发热伴血小板减少综合征
2025 年截至 7 月 2 日，全国报告病例数及死亡人数分别较 2024 年同期下降
12.3%及 35.3%。病例仍以散发为主，分布在 18 个省份，97.0%的病例分布在安
徽、湖北、河南、山东、江苏、浙江和辽宁这 7 个既往高发省份。

2.1.1.4 食物中毒
2025年1－6月，全国共报告食物中毒事件62起，较2024年同期增加31.9%。
其中，6 月报告 21 起，占 2025 年上半年报告总数的 33.9%，较 2024 年同期增加
90.9%。有毒动植物及毒蘑菇引起的食物中毒事件 12 起，致病因子为乌头、毒蘑
菇等；微生物性食物中毒 4 起，致病因子为产气荚膜梭菌、副溶血弧菌、致泻性
大肠埃希菌等。

2.1.1.5 高温中暑
2025 年 1－6 月，全国累计报告高温中暑病例数及死亡人数分别较 2024 年
同期增加 30.1%和 8.3%。其中，6 月报告病例数占 2025 年上半年报告总数的
75.3%，较 2024 年同期增加 24.1%，死亡人数较 2024 年同期减少 30.0%。
"""
    
    print("=" * 60)
    print("测试AI分析功能")
    print("=" * 60)
    
    # 1. 测试提示词生成
    print("1. 测试提示词生成...")
    prompt = get_analysis_prompt()
    print(f"   提示词长度: {len(prompt)} 字符")
    print("   提示词预览:")
    print("   " + "-" * 50)
    print("   " + prompt[:300].replace('\n', '\n   '))
    print("   " + "-" * 50)
    
    # 2. 模拟AI响应（JSON格式）
    print("\n2. 模拟AI分析响应...")
    mock_ai_response = """{
  "report_info": {
    "title": "2025年7月中国需关注的突发公共卫生事件风险评估",
    "period": "2025年7月",
    "summary": "评估2025年7月在我国发生或可能由境外输入的突发公共卫生事件风险。预计7月突发公共卫生事件数可能比6月有所减少，以传染病类事件为主。"
  },
  "risk_assessment": {
    "overall_trend": "2025年7月突发公共卫生事件数可能比6月有所减少，以传染病类事件为主",
    "key_concerns": [
      {
        "disease": "登革热",
        "risk_level": "重点关注",
        "description": "境外输入及跨地区传播风险上升",
        "affected_regions": "Ⅰ类地区及部分Ⅱ类地区",
        "recommendations": "加强病例和蚊媒密度监测，做好环境卫生整治"
      },
      {
        "disease": "霍乱",
        "risk_level": "重点关注", 
        "description": "预计仍将出现散发病例",
        "affected_regions": "全国",
        "recommendations": "做好肠道门诊监测，加强水产品监测"
      }
    ]
  },
  "infectious_diseases": {
    "high_risk": [
      {
        "name": "登革热",
        "current_status": "全国报告病例数较2024年同期升高41.8%",
        "risk_factors": "境外输入、蚊媒传播、夏季高发",
        "prevention_measures": "控制蚊媒密度、加强监测、环境整治"
      },
      {
        "name": "霍乱",
        "current_status": "2025年截至6月30日报告确诊病例4例",
        "risk_factors": "食源性传播、水产品污染",
        "prevention_measures": "加强食品安全监管、水产品监测"
      }
    ],
    "moderate_risk": [
      {
        "name": "新型冠状病毒感染",
        "current_status": "疫情将继续呈现下降或波动下降趋势",
        "risk_factors": "病毒变异、人群聚集",
        "prevention_measures": "继续做好监测，重点人群防护"
      }
    ]
  },
  "public_health_events": {
    "food_safety": {
      "risk_description": "7月仍为食物中毒事件高发期",
      "main_hazards": "毒蘑菇、有毒动植物和微生物污染",
      "prevention_advice": "加强食品安全监管，避免误食有毒动植物"
    },
    "environmental_health": {
      "climate_risks": "7-8月高温中暑发病存在上升风险",
      "natural_disasters": "7月全面进入主汛期，洪涝灾害风险",
      "prevention_measures": "做好防暑降温，加强灾后防病"
    }
  },
  "key_recommendations": [
    "对登革热、霍乱、发热伴血小板减少综合征等重点关注",
    "加强食物中毒和高温中暑的预防",
    "做好洪涝灾害相关疾病防控"
  ]
}"""
    
    # 3. 测试结果格式化
    print("\n3. 测试结果格式化...")
    formatted_result = format_analysis_result(mock_ai_response)
    
    if formatted_result.get('success'):
        print("   格式化成功!")
        data = formatted_result['data']
        print(f"   报告标题: {data['report_info'].get('title', 'N/A')}")
        print(f"   评估时期: {data['report_info'].get('period', 'N/A')}")
        print(f"   重点关注事件数: {data['risk_summary'].get('key_concerns_count', 0)}")
        print(f"   高风险疾病数: {data['risk_summary'].get('high_risk_diseases_count', 0)}")
    else:
        print(f"   格式化失败: {formatted_result.get('error', 'Unknown error')}")
        return
    
    # 4. 测试HTML报告生成
    print("\n4. 测试HTML报告生成...")
    html_report = generate_html_report(formatted_result)
    
    if html_report:
        print("   HTML报告生成成功!")
        print(f"   HTML长度: {len(html_report)} 字符")
        
        # 保存HTML到文件以便查看
        with open('test_report.html', 'w', encoding='utf-8') as f:
            f.write(html_report)
        print("   HTML报告已保存到 test_report.html")
    else:
        print("   HTML报告生成失败")
    
    print("\n" + "=" * 60)
    print("测试完成!")
    print("=" * 60)

if __name__ == "__main__":
    test_ai_analysis()
