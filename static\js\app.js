var startTime;
var newsContent = '';
var quillInstance = null;
$(document).ready(function () {
	$("#logoutButton").button();
	// 退出按钮的点击事件
	$("#logoutButton").click(function () {
		// 在这里添加退出的逻辑，比如调用服务端的退出接口
		window.location.href = '/logout'; // 假设你的退出路由是 '/logout'
	});
	// Initialize datepickers
	$.datepicker.setDefaults($.datepicker.regional['zh-CN']);
	$('#startDate').datepicker({
		dateFormat: 'yy-mm-dd',
		maxDate: '0d',
		defaultDate: "-7d",
		changeMonth: true,  // 允许用户选择月份
		changeYear: true    // 允许用户选择年份
	}).datepicker('setDate', '-7d');

	$('#endDate').datepicker({
		dateFormat: 'yy-mm-dd',
		maxDate: '0d',
		changeMonth: true,  // 允许用户选择月份
		changeYear: true    // 允许用户选择年份
	}).datepicker('setDate', new Date());
	var table = $('#newsTable').DataTable({
		"orderCellsTop": true,
		"searching": true, // 禁用初始搜索
		"responsive": true,
		"drawCallback": function (settings) {
			var api = this.api();
			// 在表格的每一行开头添加行号
			var startIndex = api.context[0]._iDisplayStart; // 获取当前页的起始索引
			api.column(0, { page: 'current' }).nodes().each(function (cell, i) {
				// 增加 startIndex 以匹配当前页的行号
				cell.innerHTML = startIndex + i + 1;
			});
			// 判断是否已经设置了开始时间
			if (startTime) {
				var endTime = new Date(); // 记录结束时间
				var timeTaken = (endTime - startTime) / 1000; // 计算耗时，单位秒
				// 在分页信息元素后面追加耗时信息
				$('.dataTables_info').append('&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;耗时：' + timeTaken.toFixed(2) + ' 秒');
				startTime = undefined; // 重置开始时间以便下次使用
			}
		},
		"language": {
			"url": "../static/json/Chinese.json"
		},
		'ajax': {
			'url': 'fetch_news',
			'type': 'GET',
			'data': function (d) {
				d.customSearch = $('#customSearchBox').val();
				d.startDate = $('#startDate').val();
				d.endDate = $('#endDate').val();
			}
		},
		'columns': [
			{
				"data": null, // 这里不绑定数据源，因为行号是自动生成的
				"searchable": false,
				"orderable": false,
				"className": "seq-column"
			},
			{
				'data': 'checkbox',
				'render': function () { return '<input type="checkbox">'; },
				'orderable': false,
				'searchable': false,
				'className': 'select-column'
			},
			{
				'data': 'title',
				'className': 'title-column'
			},
			{
				'data': 'publishedAt',
				'className': 'published-column'
			},
			{
				'data': 'description',
				'className': 'summary-column'
			},
			{
				'data': 'source',
				'className': 'source-column'
			},
			{
				'data': 'url',
				'render': function (data, type, row) {
					// 给按钮添加一个数据属性来保存新闻的URL
					return '<button class="open-news-content" data-url="' + data + '">打开</button>';
				},
				'orderable': false,
				'searchable': false,
				'className': 'url-column'
			}
		],
		"dom": "<'top'ip>rt<'bottom'l><'clear'>"
		// ... 其他设置 ...
	});

	// Column-specific search functionality
	$('#newsTable thead').on('keyup change', '.column-filter', function () {
		table.column($(this).parent().index() + ':visible')
			.search(this.value)
			.draw();
	});

	// Custom search box button click functionality
	$('#searchButton').click(function () {
		startTime = new Date(); // 记录开始时间
		// 在这里可以调用 reload 方法，重新加载表格
		// 并传递自定义搜索框的值给后台
		var customSearch = $('#customSearchBox').val();
		if (customSearch) {
			// 需要重新配置 DataTables 使用 ajax 选项
			// 只有在 customSearch 有值时才加载数据
			table.ajax.url('/fetch_news?customSearch=' + encodeURIComponent(customSearch)).load();
			table.ajax.reload(null, false); // 设为 false 以保留当前分页位置
		} else {
			alert('请输入查询关键字.');
		}
	});


	// 全选复选框的点击事件
	$('#selectAll').on('click', function () {
		// 获取所有checkbox的选中状态
		var rows = table.rows({ 'search': 'applied' }).nodes();
		$('input[type="checkbox"]', rows).prop('checked', this.checked);
	});

	// 响应单个复选框的变化以更新全选复选框的状态
	$('#newsTable tbody').on('change', 'input[type="checkbox"]', function () {
		// 如果单个复选框取消选中，则全选复选框也应取消选中
		if (!this.checked) {
			var el = $('#selectAll').get(0);
			// 如果全选已经被选中，则取消选中
			if (el && el.checked && ('indeterminate' in el)) {
				el.indeterminate = true;
			}
		}
	});
	// 显示进度条模态
	function showProgressModal() {
		$('#progressModal').css('display', 'block');
		$('button').prop('disabled', true); // 禁用所有按钮
		// 也可以添加其他UI元素的禁用逻辑
	}

	// 隐藏进度条模态
	function hideProgressModal() {
		$('#progressModal').css('display', 'none');
		$('button').prop('disabled', false); // 启用所有按钮
		// 也可以添加其他UI元素的启用逻辑
	}
	// "AI生成"按钮点击事件
	$('#generateButton').click(function () {
		var selectedData = []; // 用于存储选中行的数据
		var selected = false;  // 用于标记是否有复选框被选中
		// 遍历所有检查框，并且检查是否选中
		$('#newsTable tbody input[type="checkbox"]:checked').each(function () {
			selected = true;
			var row = $(this).closest('tr');
			var rowData = table.row(row).data(); // 获取对应行的数据
			selectedData.push(rowData);
		});

		// 判断是否至少有一个复选框被选中
		if (!selected) {
			alert('提示：请至少选择一条新闻.');
		} else {
			var confirmMessage = "您确定要生成 " + selectedData.length + " 条新闻吗？"; // 修改确认消息
			var userConfirmed = confirm(confirmMessage);
			if (userConfirmed) {
				// 你可以在这里处理选中的行数据
				// 例如，将它们发送到服务器端的脚本
				showProgressModal(); // 点击生成按钮时显示进度条模态窗口
				$.ajax({
					url: 'ai_generate', // 这里应该改为你的后端处理URL
					type: 'POST',
					dataType: 'json', // 期待的响应类型
					data: JSON.stringify({ selectedItems: selectedData }),
					contentType: 'application/json', // 发送数据的格式
					success: function (response) {
						hideProgressModal(); // 请求完成后隐藏进度条模态窗口
						// 假设服务器返回的是JSON对象，并且你设置了dataType为'json'
						if (response.status === 'success') {
							// 操作成功
							alert('提示: ' + response.message);
							var fileUrl = response.fileUrl; // 后端提供的文件URL
							var link = document.createElement('a');
							link.href = fileUrl;
							link.download = fileUrl.substr(fileUrl.lastIndexOf('/') + 1);
							document.body.appendChild(link);
							link.click();
							document.body.removeChild(link);
						} else {
							// 操作失败，但HTTP请求本身是成功的
							alert('提示: ' + response.message);
						}
					},
					error: function (xhr, status, error) {
						// 如果有错误发生，可以在这里处理
						hideProgressModal(); // 请求完成后隐藏进度条模态窗口
						alert('错误: ' + error);
					}
				});
			} else {

			}
		}
	});
	// 初始化模式窗口
	$("#aiConfigModal").dialog({
		autoOpen: false,
		height: 600,
		width: 900,
		modal: true,
		buttons: {
			"保存": function () {
				var configData = {
					prompt_text: $("#promptText").val(),
					api_endpoint: $("#apiEndpoint").val(),
					api_key: $("#apiKey").val(),
					ai_model: $("#aiModel").val()
				};
				// 发送数据到服务器保存配置
				// 这里你需要创建一个保存配置的服务器端脚本
				// 发送数据到服务器保存配置
				$.ajax({
					url: 'save_config',
					type: 'POST',
					dataType: 'json', // 期待的响应类型
					contentType: 'application/json', // 设置发送数据的格式为 JSON
					data: JSON.stringify(configData), // 将对象转换为 JSON 字符串
					success: function (response) {
						// 假设服务器返回的是JSON对象，并且你设置了dataType为'json'
						if (response.status === 'success') {
							// 操作成功
							alert('提示: ' + response.message);
						} else {
							// 操作失败，但HTTP请求本身是成功的
							alert('提示: ' + response.message);
						}
					},
					error: function (xhr, status, error) {
						// 如果有错误发生，可以在这里处理
						alert('错误: ' + error);
					}
				});
				$(this).dialog("close");
			},
			"关闭": function () {
				$(this).dialog("close");
			}
		},
		create: function () {
			// 更改按钮颜色
			var buttons = $(this).parent().find('.ui-dialog-buttonpane button');
			buttons.each(function () {
				var buttonText = $(this).text();
				if (buttonText === '保存') {
					$(this).addClass('ui-button-success').css({ 'background-color': '#28a745', 'color': 'white' });
				} else if (buttonText === '关闭') {
					$(this).addClass('ui-button-danger').css({ 'background-color': '#dc3545', 'color': 'white' });
				}
			});
		}
	});

	// 给"AI调用配置"按钮添加点击事件
	$("#generateConfig").click(function () {
		$.ajax({
			url: 'get_config', // 配置文件地址
			type: 'GET',
			cache: false, // 禁用缓存
			dataType: 'json',
			success: function (config) {
				// 读取配置并赋值给表单
				$("#promptText").val(config.prompt_text);
				$("#apiEndpoint").val(config.api_endpoint);
				$("#apiKey").val(config.api_key);
				$("#aiModel").val(config.ai_model);
			}
		});
		// 打开模式窗口
		$("#aiConfigModal").dialog("open");
	});

	// 添加自定义按钮样式
	$.fn.addStylesToDialogButton = function (buttonName, className) {
		this.each(function () {
			$(this).parent().find(`button:contains('${buttonName}')`).addClass(className);
		});
		return this;
	};

	// 在模式窗口创建时，修改按钮样式
	$("#aiConfigModal").dialog({
		// ... 窗口配置 ...
		create: function () {
			$(this).addStylesToDialogButton('保存', 'ui-button-success');
			$(this).addStylesToDialogButton('关闭', 'ui-button-danger');
		}
	});
	$('#newsTable tbody').on('click', '.open-news-content', function () {
		var newsUrl = $(this).data('url');
		var newsTitle = $(this).closest('tr').find('.title-column').text();
		// 在这里调用后端接口获取新闻内容
		// 显示正在加载的信息
		showLoading(newsTitle, newsUrl);
		$.ajax({
			url: '/get_news_content',
			type: 'POST',
			contentType: 'application/json',
			dataType: 'json',
			data: JSON.stringify({ url: newsUrl }),
			success: function (response) {
				if (response.status) {
					// 当数据接收成功后，将内容显示在Quill编辑器中
					newsContent = response.content;
					quillInstance.clipboard.dangerouslyPasteHTML(convertTextToHTML('<b>新闻标题：' + newsTitle + '</b>\n\n' + newsContent));
				} else {
					//alert('获取新闻内容失败: ' + response.message);
					quillInstance.clipboard.dangerouslyPasteHTML(convertTextToHTML('<b>新闻标题：' + newsTitle + '</b>\n\n获取新闻内容失败: ' + response.message));
				}
				newsContent = '';
			},
			error: function (xhr, status, error) {
				newsContent = '';
				alert('错误: ' + error);
			}
		});
	});
	// 初始化新闻内容模态窗口
	$("#newsContentModal").dialog({
		autoOpen: false,
		height: 720,
		width: 1280,
		modal: true,
		open: function () {
			if (!quillInstance) {
				quillInstance = new Quill('#newsContentText', {
					theme: 'snow',
					readOnly: true
				});
			}
			if (newsContent) {
				quillInstance.clipboard.dangerouslyPasteHTML(convertTextToHTML(newsContent));
			}
		},
		buttons: {
			"关闭": function () {
				$(this).dialog("close");
			}
		}
	});
});
function showLoading(title, url) {
	if (!quillInstance) {
		// 如果quill实例还没有初始化
		quillInstance = new Quill('#newsContentText', {
			theme: 'snow',
			readOnly: true
		});
	}
	// 设置Quill编辑器的内容为加载提示
	quillInstance.setText('正在载入，请耐心等待...\n\n');
	quillInstance.insertText(quillInstance.getLength(), title + '\n');
	quillInstance.insertText(quillInstance.getLength(), url + '\n', { 'link': url });

	quillInstance.formatText(0, 14, {
		'bold': true,
		'size': 'large'
	});
	// 打开模态窗口
	$('#newsContentModal').dialog('open');
}
function convertTextToHTML(text) {
	// 首先转换换行符为HTML的<br>标签
	let htmlContent = text.replace(/\n/g, '<br>');

	// 然后将URL转换为超链接
	// 这个正则表达式可以匹配大部分URL
	const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
	htmlContent = htmlContent.replace(urlRegex, function (url) {
		// 创建一个a标签来包裹URL，使其可点击
		return '<a href="' + url + '" target="_blank">' + url + '</a>';
	});

	return htmlContent;
}