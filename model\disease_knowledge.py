from datetime import datetime
from model.models import db

class DiseaseRelation(db.Model):
    """疾病关系模型"""
    __tablename__ = 'disease_relations'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    source_disease = db.Column(db.String(255), db.<PERSON><PERSON><PERSON>('news_diseases.data_value'))
    target_disease = db.Column(db.String(255), db.<PERSON><PERSON>('news_diseases.data_value'))
    relation_type = db.Column(db.String(50))  # 关系类型：并发、相关、因果等
    confidence = db.Column(db.Float)  # 关联度
    evidence = db.Column(db.Text)  # 支持证据
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)

class DiseaseAttribute(db.Model):
    """疾病属性模型"""
    __tablename__ = 'disease_attributes'
    
    id = db.Column(db.Integer, primary_key=True, autoincrement=True)
    disease_id = db.Column(db.String(255), db.<PERSON><PERSON>('news_diseases.data_value'))
    attribute_key = db.Column(db.String(50))  # 属性键：症状、传播途径、预防措施等
    attribute_value = db.Column(db.Text)  # 属性值
    created_at = db.Column(db.DateTime, default=datetime.now)
    updated_at = db.Column(db.DateTime, default=datetime.now, onupdate=datetime.now)
