import requests
from bs4 import BeautifulSoup
from flask import jsonify, request
from urllib.parse import urljoin
import concurrent.futures
import traceback

BASE_LIST_URL = "https://www.chinacdc.cn/jksj/jksj02"
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/123.0.0.0 Safari/537.36"
}


def build_page_url(page):
    if not page or page <= 1:
        return f"{BASE_LIST_URL}/index.html"
    # chinacdc uses index_{page-1}.html for page >= 2
    return f"{BASE_LIST_URL}/index_{page - 1}.html"


def extract_pdf_url(detail_or_pdf_url):
    """Return a direct PDF url. If the given url is already a PDF, return it;
    otherwise fetch the detail page and extract the first PDF link.
    """
    try:
        if detail_or_pdf_url.lower().endswith(".pdf"):
            return detail_or_pdf_url
        # fetch detail page to find pdf link
        resp = requests.get(detail_or_pdf_url, headers=HEADERS, timeout=10)
        if resp.status_code != 200:
            return None
        resp.encoding = "utf-8"
        soup = BeautifulSoup(resp.text, "html.parser")
        # try anchors first
        for a in soup.select("a[href]"):
            href = a.get("href", "").strip()
            if href and href.lower().endswith(".pdf"):
                return urljoin(detail_or_pdf_url, href)
        # fallback: iframe or embed
        iframe_pdf = soup.select_one("iframe[src]")
        if iframe_pdf and str(iframe_pdf.get("src", "")).lower().endswith(".pdf"):
            return urljoin(detail_or_pdf_url, iframe_pdf.get("src"))
        embed_pdf = soup.select_one("embed[src]")
        if embed_pdf and str(embed_pdf.get("src", "")).lower().endswith(".pdf"):
            return urljoin(detail_or_pdf_url, embed_pdf.get("src"))
        return None
    except Exception:
        return None


def fetch_page(page_url):
    try:
        resp = requests.get(page_url, headers=HEADERS, timeout=10)
        if resp.status_code != 200:
            return []
        resp.encoding = "utf-8"
        soup = BeautifulSoup(resp.text, "html.parser")
        items = soup.select("ul.xw_list li")  # consistent with jksj01
        results = []
        for li in items:
            a = li.select_one("a")
            if not a:
                continue
            # date usually appears inside span within a
            date_span = a.select_one("span")
            date = date_span.get_text(strip=True) if date_span else ""
            title_text = a.get_text(strip=True)
            if date:
                title_text = title_text.replace(date, "").strip()
            href = a.get("href", "").strip()
            if not href:
                continue
            # normalize link
            if href.startswith("./"):
                href = href[2:]
            full_detail_or_pdf_url = urljoin(f"{BASE_LIST_URL}/", href)
            results.append(
                {
                    "title": title_text,
                    "date": date,
                    "detailOrPdfUrl": full_detail_or_pdf_url,
                }
            )
        # resolve PDF links concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=8) as executor:
            future_to_idx = {
                executor.submit(extract_pdf_url, r["detailOrPdfUrl"]): i
                for i, r in enumerate(results)
            }
            for future in concurrent.futures.as_completed(future_to_idx):
                i = future_to_idx[future]
                pdf_url = None
                try:
                    pdf_url = future.result()
                except Exception:
                    pdf_url = None
                results[i]["pdfUrl"] = pdf_url or results[i]["detailOrPdfUrl"]
        return results
    except Exception as e:
        print(f"Error fetching page {page_url}: {e}")
        traceback.print_exc()
        return []


def handler():
    try:
        page_param = request.args.get("page")
        # 支持单页抓取：若提供 page 参数，则仅抓取该页；否则自动抓取所有页聚合
        if page_param:
            try:
                page = int(page_param)
            except ValueError:
                page = 1
            data = fetch_page(build_page_url(page))
        else:
            # 自动翻页聚合，最多 50 页做保护
            data = []
            page = 1
            while True:
                page_url = build_page_url(page)
                page_data = fetch_page(page_url)
                if not page_data:
                    break
                data.extend(page_data)
                page += 1
                if page > 50:
                    break
        # 去重并排序
        unique = {}
        for r in data:
            key = f"{r.get('title', '')}__{r.get('date', '')}"
            if key not in unique:
                unique[key] = r
        merged = list(unique.values())
        merged.sort(key=lambda x: x.get("date", ""), reverse=True)
        payload = [
            {"title": r["title"], "date": r["date"], "pdfUrl": r.get("pdfUrl")}
            for r in merged
        ]
        return jsonify(payload)
    except Exception as e:
        print(f"Unhandled error in cdc_risk_reports_china handler: {e}")
        traceback.print_exc()
        return jsonify([])
