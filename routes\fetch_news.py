from flask import jsonify, request
import requests
from datetime import datetime
from routes.searchBing import search_bing_news
from routes.searchDuckduckgo import search_duckduckgo_news
from deep_translator import GoogleTranslator

def handler():
    custom_search = request.args.get('customSearch')
    start_date = request.args.get('startDate').strip() if request.args.get('startDate') else None
    end_date = request.args.get('endDate').strip() if request.args.get('endDate') else None
    news_source = request.args.get('newsSource')
    lang = request.args.get('lang')
    useDuckduckgo = True

    if news_source == 'true':
        if not custom_search or not start_date or not end_date:
            return jsonify({'data': []})
        return search_bing_news(custom_search, start_date, end_date)
    
    if useDuckduckgo:
        return search_duckduckgo_news(custom_search, start_date, end_date, lang) 