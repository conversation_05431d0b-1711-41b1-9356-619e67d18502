version: '3.8'

services:
  web:
    build: 
      context: ..
      dockerfile: docker/Dockerfile
    ports:
      - "83:83"
    volumes:
      - ../:/app
    environment:
      - FLASK_ENV=development
      - FLASK_APP=app.py
    restart: unless-stopped

  # 如果需要添加数据库服务，取消下面的注释
  # db:
  #   image: mysql:8.0
  #   environment:
  #     - MYSQL_ROOT_PASSWORD=your_root_password
  #     - MYSQL_DATABASE=news_web
  #     - MYSQL_USER=news_web_user
  #     - MYSQL_PASSWORD=your_password
  #   volumes:
  #     - mysql_data:/var/lib/mysql
  #   ports:
  #     - "3306:3306"

# volumes:
#   mysql_data:
