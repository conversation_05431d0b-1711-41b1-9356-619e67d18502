<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>传染病趋势图</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/jquery-ui.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/flatpickr.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/app.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
    <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
    <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
    <script src="{{ url_for('static', filename='js/jquery-ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/flatpickr.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/flatpickr_zh.js') }}"></script>
</head>

<body>
    <br>
    <div id="message-container" style="position: fixed; top: 20px; right: 20px; z-index: 1000;"></div>
    <div class="ui container">
        <h2 class="ui top attached header">
            <i class="twitch icon"></i>
            感染趋势图
        </h2>
        <div class="ui attached segment">
            <div class="filter-wrapper" style="margin-top: 5px;">
                <div class="ui left icon input">
                    <i class="calendar alternate icon"></i>
                    <input type="text" id="dateRange" class="filter-box date-input" placeholder="请选择日期范围">
                </div>
                <div class="ui floating dropdown labeled icon button">
                    <input type="hidden" name="country">
                    <i class="globe icon"></i>
                    <span class="text">请选择国家</span>
                    <div class="menu">
                        <div class="ui icon search input">
                            <i class="search icon"></i>
                            <input type="text" placeholder="选择国家..." id="countrySearchInput">
                        </div>
                        <div class="divider"></div>
                        <div class="header">
                            <i class="world icon"></i>
                            国家列表
                        </div>
                        <div class="scrolling menu" id="countryList">
                            {% for country in countries %}
                            <div class="item" data-value="{{ country.data_value }}">
                                <div class="ui green empty circular label"></div>{{ country.name }}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="ui floating dropdown labeled icon button">
                    <input type="hidden" name="filters">
                    <i class="filter icon"></i>
                    <span class="text">请选择传染病名称</span>
                    <div class="menu">
                        <div class="ui icon search input">
                            <i class="search icon"></i>
                            <input type="text" placeholder="请选择传染病名称..." id="searchInput">
                        </div>
                        <div class="divider"></div>
                        <div class="header">
                            <i class="tags icon"></i>
                            传染病列表
                        </div>
                        <div class="scrolling menu" id="diseaseList">
                            {% for disease in diseases %}
                            <div class="item" data-value="{{ disease.data_value }}">
                                <div class="ui green empty circular label"></div>{{ disease.name }}
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            <div class="filter-wrapper" style="margin-top: 10px;">
                <button id="resetButton" class="ui compact labeled icon gray button" style="margin-left: 10px;">
                    <i class="redo alternate icon"></i>
                    搜索重置
                </button>
                <button id="generateButton" class="ui compact labeled icon green button">
                    <i class="pencil alternate icon"></i>
                    生成图片
                </button>
                <button id="manualButton" class="ui compact labeled icon blue button" style="margin-left: 10px;">
                    <i class="edit icon"></i>
                    手动输入生成图片
                </button>
            </div>
        </div>
        <div id="url-list" class="ui ignored info message">
            <div class="url-list-header" style="display: flex; justify-content: space-between; align-items: center;">
                <h3 class="ui horizontal divider header" style="flex-grow: 1;">
                    <i class="image outline icon"></i>
                    趋势图
                </h3>
                <div class="ui small basic icon buttons">
                    <button id="openModalButton" class="ui button" style="cursor:pointer; display:none;"><i class="table
                            icon"></i></button>
                </div>
            </div>
            <div class="ui horizontal divider"><i class="chevron down icon"></i></div>
            <div id="url-image"></div>
        </div>

    </div>
    <!-- 全屏 Modal 弹出层 -->
    <div class="ui fullscreen modal" id="viewModal">
        <i class="close icon"></i>
        <div class="header">
            详细数据一览
        </div>

        <div class="content">
            <div class="button-container" style="text-align: right; margin-bottom: 5px;">
                <button id="exportButtonCSV" class="ui blue active button">
                    <i class="external alternate icon"></i>
                    导出 CSV
                </button>
            </div>
            <div id="myGrid" class="ag-theme-quartz" style="width: 100%; height: 600px;"></div>
        </div>
        <div class="actions">
            <button class="ui red labeled icon button" id="closeModalButton">
                <i class="close icon"></i>
                关闭
            </button>
        </div>
    </div>
    <!-- 手动输入数据 Modal 弹出层 -->
    <div class="ui fullscreen modal" id="manualInputModal">
        <i class="close icon"></i>
        <div class="header">
            手动输入数据
        </div>
        <div class="content">
            <div class="ui form">
                <div class="field">
                    <label>请在下面粘贴您的数据（包括表头，列顺序为：日期、国家、传染病名、新增病例、死亡人数、治愈人数）。每行一条记录，列之间用制表符（Tab）或逗号分隔：</label>
                    <textarea id="manualDataInput" rows="30"
                        placeholder="日期\t国家\t传染病名\t新增病例\t死亡人数\t治愈人数\n2024/9/6\tThailand\tcovid-19\t10\t0\t0\n..."></textarea>
                </div>
            </div>
        </div>
        <div class="actions">
            <button class="ui blue button" id="manualGenerateButton">
                <i class="pencil alternate icon"></i>
                生成图片
            </button>
            <button class="ui red button" id="manualCloseButton">
                <i class="close icon"></i>
                关闭
            </button>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            var endDate = new Date().toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-');
            var startDate = new Date(new Date().getFullYear(), 0, 1).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-');

            // 初始化 flatpickr
            flatpickr("#dateRange", {
                mode: "range",
                maxDate: "today",
                locale: "zh",
                dateFormat: "Y-m-d",
                defaultDate: [startDate, endDate]
            });

            // 初始化疾病字典
            let diseaseDict = {};
            {% for disease in diseases %}
            diseaseDict["{{ disease.data_value }}"] = {
                name: "{{ disease.name }}"
            };
            {% endfor %}

            // 初始化国家字典
            let countryDict = {};
            {% for country in countries %}
            countryDict["{{ country.data_value }}"] = {
                name: "{{ country.name }}"
            };
            {% endfor %}

            // 初始化下拉框
            $('.ui.dropdown').dropdown({
                fullTextSearch: true,
                match: 'text',
                onChange: function(value, text, $choice) {
                    if ($choice.closest('.dropdown').find('input[name="filters"]').length) {
                        // 这是疾病下拉框的变化
                        const diseaseName = diseaseDict[value] ? diseaseDict[value].name : value;
                        console.log('Selected disease:', diseaseName);
                    } else if ($choice.closest('.dropdown').find('input[name="country"]').length) {
                        // 这是国家下拉框的变化
                        const countryName = countryDict[value] ? countryDict[value].name : value;
                        console.log('Selected country:', countryName);
                    }
                }
            });

            // 监听生成图片按钮的点击事件
            $('#generateButton').on('click', function () {
                // 检查传染病下拉框是否为空
                if ($('input[name="filters"]').val() === '') {
                    alert('传染病名称不能为空'); // 提示用户
                    return; // 终止后续操作
                }
                // 将按钮变为加载状态
                $(this).addClass('loading').prop('disabled', true);
                var dateRange = $('#dateRange').val().replace(/\s+/g, ''); // 获取日期范围
                var dates = dateRange.split("至");
                var startDate = dates[0];
                var endDate = dates[1];
                var country = $('input[name="country"]').val(); // 获取选择的国家
                var disease = $('input[name="filters"]').val(); // 获取选择的传染病

                // 发送POST请求到后端
                $.ajax({
                    type: 'POST',
                    url: '/urlFigure',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        startDate: startDate,
                        endDate: endDate,
                        country: country,
                        disease: disease
                    }),
                    success: function (response) {
                        console.log('成功:', response);
                        // 这里可以根据需要处理返回的数据
                        // 清空现有图像
                        $('#url-image').empty(); // 清空原有图像
                        if (response.status === 'no_data') {
                            // 显示没有数据的提示
                            $('#url-image').append('<p><h2>没有查询到数据</h2></p>');
                            $('#openModalButton').hide(); // 隐藏按钮
                        } else {
                            // 更新图像显示
                            $('#url-image').append('<img src="' + response.graph_image + '" alt="趋势图" style="max-width:100%; height:auto;">');
                            // 存储返回的数据集，用于 ag-grid 显示
                            window.dataSet = response.data_set;
                            $('#openModalButton').show();// 显示按钮
                        }
                    },
                    error: function (error) {
                        console.error('错误:', error);
                    },
                    complete: function () {
                        // 操作结束，恢复按钮状态
                        $('#generateButton').removeClass('loading').prop('disabled', false);
                    }
                });
            });

            // 监听重置按钮的点击事件
            $('#resetButton').on('click', function () {
                // 重置日期范围
                var endDate = new Date().toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-');
                var startDate = new Date(new Date().getFullYear(), 0, 1).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '-');
                flatpickr("#dateRange", {
                    mode: "range",
                    maxDate: "today",
                    locale: "zh",
                    dateFormat: "Y-m-d",
                    defaultDate: [startDate, endDate]
                });
                // 清空国家下拉框和传染病下拉框中的值
                $('input[name="country"]').val('');
                $('input[name="filters"]').val('');
                // 恢复下拉框的默认提示文字
                $('.ui.dropdown').dropdown('restore defaults');
                $('#openModalButton').hide(); // 隐藏按钮
                // 删除显示的图片
                $('#url-image').empty(); // 清空图像显示区域
            });

            // 初始化 modal
            $('.ui.modal').modal({
                autofocus: false, // 防止自动聚焦导致页面跳动
                observeChanges: true // 如果 modal 内容会动态变化，需要添加这个配置
            });

            // 点击 table icon 时，显示 modal 并初始化 ag-grid
            $('#openModalButton').on('click', function () {
                $('#viewModal').modal('show');
                initializeAgGrid(window.dataSet);
            });

            // 监听弹出层关闭按钮的点击事件
            $('#closeModalButton, #closeModalButton2').on('click', function () {
                $('#viewModal.modal').modal('hide'); // 隐藏模态框
            });

            // 监听手动输入数据按钮的点击事件
            $('#manualButton').on('click', function () {
                $('#manualInputModal').modal('show');
            });

            // 监听手动输入数据弹出层的生成图片按钮
            $('#manualGenerateButton').on('click', function () {
                var manualData = $('#manualDataInput').val();

                // 检查数据是否为空
                if (!manualData.trim()) {
                    alert('请输入数据');
                    return;
                }

                // 将按钮变为加载状态
                $(this).addClass('loading').prop('disabled', true);

                // 发送POST请求到后端
                $.ajax({
                    type: 'POST',
                    url: '/manualInput',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        data: manualData
                    }),
                    success: function (response) {
                        console.log('成功:', response);
                        // 关闭弹出层
                        $('#manualInputModal').modal('hide');
                        // 清空数据输入框
                        $('#manualDataInput').val('');
                        // 显示生成的图像
                        $('#url-image').empty(); // 清空原有图像
                        if (response.status === 'success') {
                            $('#url-image').append('<img src="' + response.graph_image + '" alt="趋势图" style="max-width:100%; height:auto;">');
                            // 存储返回的数据集，用于 ag-grid 显示
                            window.dataSet = response.data_set;
                            $('#openModalButton').show(); // 显示按钮
                        } else {
                            alert(response.message || '生成图像失败');
                        }
                    },
                    error: function (error) {
                        console.error('错误:', error);
                        alert('生成图像时发生错误');
                    },
                    complete: function () {
                        // 操作结束，恢复按钮状态
                        $('#manualGenerateButton').removeClass('loading').prop('disabled', false);
                    }
                });
            });

            // 监听手动输入数据弹出层的关闭按钮
            $('#manualCloseButton').on('click', function () {
                $('#manualInputModal').modal('hide');
            });
        });
        function initializeAgGrid(dataSet) {
            var columnDefs = [
                { headerName: '日期', field: 'time', sortable: true, filter: true, sort: 'desc', flex: 1 },
                { headerName: '国家', field: 'cities', sortable: true, filter: true, flex: 1 },
                { headerName: '传染病名', field: 'disaster', sortable: true, filter: true, flex: 1 },
                { headerName: '新增病例', field: 'growth', sortable: true, filter: true, flex: 1 },
                { headerName: '死亡人数', field: 'deaths', sortable: true, filter: true, flex: 1 },
                { headerName: '治愈人数', field: 'cures', sortable: true, filter: true, flex: 1 }
            ];
            var gridOptions = {
                columnDefs: columnDefs,
                rowData: dataSet,
                defaultColDef: {
                    resizable: true,
                    sortable: true,
                    filter: true,
                    cellStyle: { 'text-align': 'center' }//全部列居中对齐
                },
                pagination: true,
                paginationPageSize: 20,
                localeText: AG_GRID_LOCALE_ZH,
                onGridReady: function (params) {
                    // 可以在这里添加导出按钮的事件
                    document.getElementById('exportButtonCSV').addEventListener('click', function () {
                        params.api.exportDataAsCsv(); // 导出CSV
                    });
                }
                /*
                //动态调整列宽
                onFirstDataRendered: function (params) {
                    params.columnApi.autoSizeAllColumns();
                },
                onPaginationChanged: function (params) {
                    if (params.newPage) {
                        // 在新页面加载完成后，调整列宽
                        setTimeout(function () {
                            params.columnApi.autoSizeAllColumns();
                        }, 0);
                    }
                }
                */
            };
            /*
            //变更每页多少条记录后动态调整列宽
            var lastPageSize = gridOptions.paginationPageSize;

            gridOptions.onPaginationChanged = function (params) {
                var currentPageSize = gridOptions.api.paginationGetPageSize();
                if (currentPageSize !== lastPageSize) {
                    lastPageSize = currentPageSize;
                    setTimeout(function () {
                        params.columnApi.autoSizeAllColumns();
                    }, 0);
                }
            };
            */
            var eGridDiv = document.querySelector('#myGrid');

            // 如果已经有 ag-grid 实例，先销毁
            if (window.gridApi) {
                window.gridApi.destroy();
            }

            new agGrid.Grid(eGridDiv, gridOptions);
            window.gridApi = gridOptions.api;
        }

    </script>
</div>
</body>

</html>