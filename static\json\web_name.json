[{"id": "google-news-ar", "name": "Google News (Argentina)", "url": "news.google.com", "country": "阿根廷"}, {"id": "infobae", "name": "Infobae", "url": "www.infobae.com", "country": "阿根廷"}, {"id": "la-gaceta", "name": "La Gaceta", "url": "www.lagaceta.com.ar", "country": "阿根廷"}, {"id": "la-nacion", "name": "La Nacion", "url": "www.lanacion.com.ar", "country": "阿根廷"}, {"id": "abc-news-au", "name": "ABC News (AU)", "url": "www.abc.net.au", "country": "澳大利亚"}, {"id": "australian-financial-review", "name": "Australian Financial Review", "url": "www.afr.com", "country": "澳大利亚"}, {"id": "google-news-au", "name": "Google News (Australia)", "url": "news.google.com", "country": "澳大利亚"}, {"id": "news-com-au", "name": "News.com.au", "url": "www.news.com.au", "country": "澳大利亚"}, {"id": "blasting-news-br", "name": "Blasting News (BR)", "url": "br.blastingnews.com", "country": "巴西"}, {"id": "globo", "name": "Globo", "url": "www.globo.com", "country": "巴西"}, {"id": "google-news-br", "name": "Google News (Brasil)", "url": "news.google.com", "country": "巴西"}, {"id": "info-money", "name": "InfoMoney", "url": "www.infomoney.com.br", "country": "巴西"}, {"id": "cbc-news", "name": "CBC News", "url": "www.cbc.ca", "country": "加拿大"}, {"id": "financial-post", "name": "Financial Post", "url": "business.financialpost.com", "country": "加拿大"}, {"id": "google-news-ca", "name": "Google News (Canada)", "url": "news.google.com", "country": "加拿大"}, {"id": "the-globe-and-mail", "name": "The Globe And Mail", "url": "www.theglobeandmail.com", "country": "加拿大"}, {"id": "bild", "name": "Bild", "url": "www.bild.de", "country": "德国"}, {"id": "der-<PERSON>ess<PERSON><PERSON>", "name": "Der Tagesspiegel", "url": "www.tagesspiegel.de", "country": "德国"}, {"id": "die-zeit", "name": "Die Zeit", "url": "www.zeit.de", "country": "德国"}, {"id": "focus", "name": "Focus", "url": "www.focus.de", "country": "德国"}, {"id": "gruenderszene", "name": "Gruenderszene", "url": "www.gruenderszene.de", "country": "德国"}, {"id": "handelsblatt", "name": "Handelsblatt", "url": "www.handelsblatt.com", "country": "德国"}, {"id": "spiegel-online", "name": "Spiegel Online", "url": "www.spiegel.de", "country": "德国"}, {"id": "t3n", "name": "T3n", "url": "t3n.de", "country": "德国"}, {"id": "wired-de", "name": "Wired.de", "url": "www.wired.de", "country": "德国"}, {"id": "wirtschafts-woche", "name": "Wirtschafts Woche", "url": "www.wiwo.de", "country": "德国"}, {"id": "el-mundo", "name": "El Mundo", "url": "www.elmundo.es", "country": "西班牙"}, {"id": "marca", "name": "<PERSON><PERSON>", "url": "www.marca.com", "country": "西班牙"}, {"id": "google-news-fr", "name": "Google News (France)", "url": "news.google.com", "country": "法国"}, {"id": "le-monde", "name": "Le Monde", "url": "www.lemonde.fr", "country": "法国"}, {"id": "lequipe", "name": "L'equipe", "url": "www.lequipe.fr", "country": "法国"}, {"id": "les-echos", "name": "<PERSON>", "url": "www.lesechos.fr", "country": "法国"}, {"id": "liberation", "name": "Libération", "url": "www.liberation.fr", "country": "法国"}, {"id": "bbc-news", "name": "BBC News", "url": "www.bbc.co.uk", "country": "英国"}, {"id": "bbc-sport", "name": "BBC Sport", "url": "www.bbc.co.uk", "country": "英国"}, {"id": "business-insider-uk", "name": "Business Insider (UK)", "url": "uk.businessinsider.com", "country": "英国"}, {"id": "four-four-two", "name": "FourFourTwo", "url": "www.fourfourtwo.com", "country": "英国"}, {"id": "google-news-uk", "name": "Google News (UK)", "url": "news.google.com", "country": "英国"}, {"id": "independent", "name": "Independent", "url": "www.independent.co.uk", "country": "英国"}, {"id": "mtv-news-uk", "name": "MTV News (UK)", "url": "www.mtv.co.uk", "country": "英国"}, {"id": "talksport", "name": "TalkSport", "url": "talksport.com", "country": "英国"}, {"id": "the-lad-bible", "name": "The Lad Bible", "url": "www.theladbible.com", "country": "英国"}, {"id": "the-sport-bible", "name": "The Sport Bible", "url": "www.thesportbible.com", "country": "英国"}, {"id": "rte", "name": "RTE", "url": "www.rte.ie", "country": "爱尔兰"}, {"id": "the-irish-times", "name": "The Irish Times", "url": "www.irishtimes.com", "country": "爱尔兰"}, {"id": "google-news-in", "name": "Google News (India)", "url": "news.google.com", "country": "印度"}, {"id": "the-hindu", "name": "The Hindu", "url": "www.thehindu.com", "country": "印度"}, {"id": "the-times-of-india", "name": "The Times of India", "url": "timesofindia.indiatimes.com", "country": "印度"}, {"id": "google-news-is", "name": "Google News (Israel)", "url": "news.google.com", "country": "冰岛"}, {"id": "the-jerusalem-post", "name": "The Jerusalem Post", "url": "www.jpost.com", "country": "冰岛"}, {"id": "ynet", "name": "Ynet", "url": "www.ynet.co.il", "country": "冰岛"}, {"id": "ansa", "name": "ANSA.it", "url": "www.ansa.it", "country": "意大利"}, {"id": "football-italia", "name": "Football Italia", "url": "www.football-italia.net", "country": "意大利"}, {"id": "google-news-it", "name": "Google News (Italy)", "url": "news.google.com", "country": "意大利"}, {"id": "il-sole-24-ore", "name": "Il Sole 24 Ore", "url": "www.ilsole24ore.com", "country": "意大利"}, {"id": "la-repubblica", "name": "La Repubblica", "url": "www.repubblica.it", "country": "意大利"}, {"id": "rtl-nieuws", "name": "RTL Nieuws", "url": "www.rtlnieuws.nl", "country": "荷兰"}, {"id": "aftenposten", "name": "Aftenposten", "url": "www.aftenposten.no", "country": "挪威"}, {"id": "nrk", "name": "NRK", "url": "www.nrk.no", "country": "挪威"}, {"id": "ary-news", "name": "Ary News", "url": "arynews.tv", "country": "巴基斯坦"}, {"id": "google-news-ru", "name": "Google News (Russia)", "url": "news.google.com", "country": "俄罗斯"}, {"id": "lenta", "name": "<PERSON><PERSON>", "url": "lenta.ru", "country": "俄罗斯"}, {"id": "rbc", "name": "RBC", "url": "www.rbc.ru", "country": "俄罗斯"}, {"id": "rt", "name": "RT", "url": "russian.rt.com", "country": "俄罗斯"}, {"id": "argaam", "name": "<PERSON><PERSON><PERSON>", "url": "www.argaam.com", "country": "沙特阿拉伯"}, {"id": "google-news-sa", "name": "Google News (Saudi Arabia)", "url": "news.google.com", "country": "沙特阿拉伯"}, {"id": "sabq", "name": "SABQ", "url": "sabq.org", "country": "沙特阿拉伯"}, {"id": "goteborgs-posten", "name": "Göteborgs-Posten", "url": "www.gp.se", "country": "瑞典"}, {"id": "svenska-dagbladet", "name": "Svenska Dagbladet", "url": "www.svd.se", "country": "瑞典"}, {"id": "abc-news", "name": "ABC News", "url": "abcnews.go.com", "country": "美国"}, {"id": "al-jazeera-english", "name": "Al Jazeera English", "url": "www.aljazeera.com", "country": "美国"}, {"id": "ars-technica", "name": "Ars Technica", "url": "arstechnica.com", "country": "美国"}, {"id": "associated-press", "name": "Associated Press", "url": "apnews.com", "country": "美国"}, {"id": "axios", "name": "A<PERSON>os", "url": "www.axios.com", "country": "美国"}, {"id": "bleacher-report", "name": "Bleacher Report", "url": "www.bleacherreport.com", "country": "美国"}, {"id": "bloomberg", "name": "Bloomberg", "url": "www.bloomberg.com", "country": "美国"}, {"id": "breitbart-news", "name": "Breitbart News", "url": "www.breitbart.com", "country": "美国"}, {"id": "business-insider", "name": "Business Insider", "url": "www.businessinsider.com", "country": "美国"}, {"id": "buzzfeed", "name": "Buzzfeed", "url": "www.buzzfeed.com", "country": "美国"}, {"id": "cbs-news", "name": "CBS News", "url": "www.cbsnews.com", "country": "美国"}, {"id": "cnn", "name": "CNN", "url": "us.cnn.com", "country": "美国"}, {"id": "cnn-es", "name": "CNN Spanish", "url": "cnnespanol.cnn.com", "country": "美国"}, {"id": "crypto-coins-news", "name": "Crypto Coins News", "url": "www.ccn.com", "country": "美国"}, {"id": "engadget", "name": "<PERSON><PERSON><PERSON><PERSON>", "url": "www.engadget.com", "country": "美国"}, {"id": "entertainment-weekly", "name": "Entertainment Weekly", "url": "www.ew.com", "country": "美国"}, {"id": "espn", "name": "ESPN", "url": "www.espn.com", "country": "美国"}, {"id": "espn-cric-info", "name": "ESPN Cric Info", "url": "www.espncricinfo.com", "country": "美国"}, {"id": "fortune", "name": "Fortune", "url": "fortune.com", "country": "美国"}, {"id": "fox-news", "name": "Fox News", "url": "www.foxnews.com", "country": "美国"}, {"id": "fox-sports", "name": "Fox Sports", "url": "www.foxsports.com", "country": "美国"}, {"id": "google-news", "name": "Google News", "url": "news.google.com", "country": "美国"}, {"id": "hacker-news", "name": "Hacker News", "url": "news.ycombinator.com", "country": "美国"}, {"id": "ign", "name": "IGN", "url": "www.ign.com", "country": "美国"}, {"id": "mashable", "name": "Mashable", "url": "mashable.com", "country": "美国"}, {"id": "medical-news-today", "name": "Medical News Today", "url": "www.medicalnewstoday.com", "country": "美国"}, {"id": "msnbc", "name": "MSNBC", "url": "www.msnbc.com", "country": "美国"}, {"id": "mtv-news", "name": "MTV News", "url": "www.mtv.com", "country": "美国"}, {"id": "national-geographic", "name": "National Geographic", "url": "news.nationalgeographic.com", "country": "美国"}, {"id": "national-review", "name": "National Review", "url": "www.nationalreview.com", "country": "美国"}, {"id": "nbc-news", "name": "NBC News", "url": "www.nbcnews.com", "country": "美国"}, {"id": "new-scientist", "name": "New Scientist", "url": "www.newscientist.com", "country": "美国"}, {"id": "newsweek", "name": "Newsweek", "url": "www.newsweek.com", "country": "美国"}, {"id": "new-york-magazine", "name": "New York Magazine", "url": "nymag.com", "country": "美国"}, {"id": "next-big-future", "name": "Next Big Future", "url": "www.nextbigfuture.com", "country": "美国"}, {"id": "nfl-news", "name": "NFL News", "url": "www.nfl.com", "country": "美国"}, {"id": "nhl-news", "name": "NHL News", "url": "www.nhl.com", "country": "美国"}, {"id": "politico", "name": "Politico", "url": "www.politico.com", "country": "美国"}, {"id": "polygon", "name": "Polygon", "url": "www.polygon.com", "country": "美国"}, {"id": "recode", "name": "Recode", "url": "www.recode.net", "country": "美国"}, {"id": "reddit-r-all", "name": "Reddit /r/all", "url": "www.reddit.com", "country": "美国"}, {"id": "reuters", "name": "Reuters", "url": "www.reuters.com", "country": "美国"}, {"id": "techcrunch", "name": "TechCrunch", "url": "techcrunch.com", "country": "美国"}, {"id": "techradar", "name": "TechRadar", "url": "www.techradar.com", "country": "美国"}, {"id": "the-american-conservative", "name": "The American Conservative", "url": "www.theamericanconservative.com", "country": "美国"}, {"id": "the-hill", "name": "The Hill", "url": "thehill.com", "country": "美国"}, {"id": "the-huffington-post", "name": "The Huffington Post", "url": "www.huffingtonpost.com", "country": "美国"}, {"id": "the-next-web", "name": "The Next Web", "url": "thenextweb.com", "country": "美国"}, {"id": "the-verge", "name": "The Verge", "url": "www.theverge.com", "country": "美国"}, {"id": "the-wall-street-journal", "name": "The Wall Street Journal", "url": "www.wsj.com", "country": "美国"}, {"id": "the-washington-post", "name": "The Washington Post", "url": "www.washingtonpost.com", "country": "美国"}, {"id": "the-washington-times", "name": "The Washington Times", "url": "www.washingtontimes.com", "country": "美国"}, {"id": "time", "name": "Time", "url": "time.com", "country": "美国"}, {"id": "usa-today", "name": "USA Today", "url": "www.usatoday.com", "country": "美国"}, {"id": "vice-news", "name": "Vice News", "url": "news.vice.com", "country": "美国"}, {"id": "wired", "name": "Wired", "url": "www.wired.com", "country": "美国"}, {"id": "news24", "name": "News24", "url": "www.news24.com", "country": "南非"}, {"id": "geekpark", "name": "Geekpark.net", "url": "www.geekpark.net", "country": "中国"}, {"id": "techcrunch-cn", "name": "TechCrunch (CN)", "url": "techcrunch.cn", "country": "中国"}, {"id": "xinhua-net", "name": "Xinhua Net", "url": "xinhuanet.com", "country": "中国"}]