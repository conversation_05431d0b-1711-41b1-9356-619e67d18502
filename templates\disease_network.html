<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Disease Network Analysis</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" href="//unpkg.com/@arco-design/web-react@2.65.0/dist/css/arco.css">
    <!-- Load all scripts in the head with the proper order -->
    <script src="{{ url_for('static', filename='js/jquery.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <script src="//unpkg.com/react@18.2.0/umd/react.production.min.js"></script>
    <script src="//unpkg.com/react-dom@18.2.0/umd/react-dom.production.min.js"></script>
    <script src="//unpkg.com/@arco-design/web-react@2.65.0/dist/arco.min.js"></script>
    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
        }

        body {
            padding: 20px;
            background-color: #f8f9fa;
        }

        .container-fluid {
            height: calc(100% - 40px);
            /* account for body padding */
        }

        .row,
        .col-12 {
            height: 100%;
        }

        .ui.card {
            width: 100%;
            height: 100%;
            margin: 0;
            display: flex;
            flex-direction: column;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
        }

        .card-header {
            background-color: #fff;
            border-bottom: 1px solid rgba(0, 0, 0, 0.125);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            flex: 0 0 auto;
        }

        .card-body {
            flex: 1 1 auto;
            padding: 0;
            position: relative;
            min-height: 0;
        }

        #networkChart {
            width: 100%;
            height: 100%;
            background-color: #fff;
            border-radius: 0 0 4px 4px;
            position: relative;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .error-message {
            color: red;
            text-align: center;
            margin: 20px;
            display: none;
        }

        .slider-container {
            padding: 12px 20px;
            display: flex;
            align-items: center;
            background-color: #f8f9fa;
            border-radius: 5px;
            margin-top: 10px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .slider-label {
            width: 100px;
            margin-right: 15px;
            color: #333;
            font-weight: 500;
            display: flex;
            align-items: center;
        }

        .slider-label i {
            margin-right: 6px;
            color: #4080ff;
        }

        .slider-component {
            flex: 1;
            margin: 0 15px;
        }

        .date-range-text {
            min-width: 200px;
            background: #fff;
            border: 1px solid #e5e6eb;
            border-radius: 4px;
            padding: 6px 12px;
            color: #333;
            font-size: 13px;
            text-align: center;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
            transition: all 0.2s;
        }

        .date-range-text:hover {
            border-color: #d0d1d6;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
        }

        /* Arco slider customizations */
        .arco-slider {
            width: 100% !important;
        }

        /* Smaller font for month marks */
        .arco-slider-marks-text {
            font-size: 10px;
            transform: scale(0.9) translateX(-5px);
        }
    </style>
</head>

<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="ui card">
                    <div class="card-header">
                        <h3 class="card-title">疾病传播网络分析</h3>
                    </div>
                    <div class="card-body">
                        <div class="filter-controls"
                            style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 5px;">
                            <div class="ui floating labeled icon button dropdown" style="margin-right: 10px;">
                                <input type="hidden" name="disease">
                                <i class="filter icon"></i>
                                <span class="text">选择传染病</span>
                                <div class="menu">
                                    <div class="ui icon search input">
                                        <i class="search icon"></i>
                                        <input type="text" placeholder="搜索传染病..." id="diseaseSearchInput">
                                    </div>
                                    <div class="divider"></div>
                                    <div class="header">
                                        <i class="tags icon"></i>
                                        传染病列表
                                    </div>
                                    <div class="scrolling menu" id="diseaseList">
                                        <div class="item" data-value="">全部传染病</div>
                                    </div>
                                </div>
                            </div>

                            <!-- Time range slider container -->
                            <div class="slider-container">
                                <div class="slider-label">
                                    <i class="calendar alternate outline icon"></i>
                                    时间范围
                                </div>
                                <div id="timeRangeSlider" class="slider-component"></div>
                                <div id="dateRangeText" class="date-range-text"></div>
                            </div>
                        </div>
                        <div id="networkChart">
                            <div id="loading" class="loading-overlay">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <div id="error-message" class="error-message"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Define global variables
        window.myChart = null;
        window.currentTimeRange = {
            startDate: new Date(new Date().setDate(new Date().getDate() - 30)),
            endDate: new Date()
        };
        window.diseaseDict = {};
        window.loading = document.getElementById('loading');
        window.errorMessage = document.getElementById('error-message');
        window.allData = null;

        // Format date for display
        window.formatDate = function (date) {
            return date.toISOString().split('T')[0];
        };

        // Format date as YYYY-MM
        window.formatYearMonth = function (date) {
            return date.toISOString().substring(0, 7);
        };

        // Format date for API parameters
        window.formatDateParam = function (date) {
            return date.toISOString().substring(0, 10);
        };

        // Format date in a more readable form (YYYY年MM月DD日)
        window.formatReadableDate = function (date) {
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        };

        window.showLoading = function () {
            window.loading.style.display = 'flex';
            window.errorMessage.style.display = 'none';
        };

        window.hideLoading = function () {
            window.loading.style.display = 'none';
        };

        window.showError = function (message) {
            window.errorMessage.textContent = message;
            window.errorMessage.style.display = 'block';
            window.hideLoading();
        };

        // 转换疾病名称函数
        window.translateDisease = function (disease) {
            if (window.diseaseDict[disease]) {
                // 提取括号内的全部内容（包括中文名称和等级）
                const match = window.diseaseDict[disease].name.match(/\((.*?)\)/);
                return match ? match[1] : disease;
            }
            return disease;
        };

        // 获取疾病数量
        window.getDiseaseCount = function (disease) {
            const match = disease.match(/\((\d+)\)/);
            return match ? match[1] : '';
        };

        // 格式化疾病列表
        window.formatDiseases = function (diseases) {
            return diseases.map(d => {
                // 先提取原始疾病名（去掉可能的数量括号）
                const originalDisease = d.replace(/\(\d+\)/, '').trim();

                // 获取疾病数量（如果有的话）
                const countMatch = d.match(/\((\d+)\)/);
                const count = countMatch ? countMatch[1] : null;

                // 使用原始疾病名进行转换
                const name = window.translateDisease(originalDisease);

                // 构建最终显示字符串
                return count
                    ? `${name}(${count})`
                    : name;
            }).join('\n');
        };

        window.colors = [
            '#5470c6', '#91cc75', '#fac858', '#ee6666',
            '#73c0de', '#3ba272', '#fc8452', '#9a60b4'
        ];

        window.initChart = function () {
            if (window.myChart !== null) {
                window.myChart.dispose();
            }
            window.myChart = echarts.init(document.getElementById('networkChart'));
            window.fetchAllData();
        };

        window.fetchAllData = function () {
            window.showLoading();

            // 获取选中的疾病
            const selectedDisease = $('input[name="disease"]').val();

            // 构建URL参数 - 只获取最近1个月的数据
            let url = `/disease_network_filter?startDate=${window.formatDateParam(window.currentTimeRange.startDate)}&endDate=${window.formatDateParam(window.currentTimeRange.endDate)}`;
            if (selectedDisease) {
                url += `&disease=${selectedDisease}`;
            }

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }
                    if (!data.nodes || !data.edges) {
                        throw new Error('Invalid data format received from server');
                    }

                    // Directly render the data without storing full dataset
                    window.renderChart(data);
                    window.hideLoading();
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.showError(error.message || '加载数据时发生错误，请稍后重试');
                });
        };

        window.updateTimeFilteredChart = function () {
            window.showLoading();

            // Update date range display with more readable format
            document.getElementById('dateRangeText').textContent =
                `${window.formatReadableDate(window.currentTimeRange.startDate)} - ${window.formatReadableDate(window.currentTimeRange.endDate)}`;

            // Send current date range to server to get filtered data
            let url = `/disease_network_filter?startDate=${window.formatDateParam(window.currentTimeRange.startDate)}&endDate=${window.formatDateParam(window.currentTimeRange.endDate)}`;

            const selectedDisease = $('input[name="disease"]').val();
            if (selectedDisease) {
                url += `&disease=${selectedDisease}`;
            }

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.error) {
                        throw new Error(data.error);
                    }

                    window.renderChart(data);
                    window.hideLoading();
                })
                .catch(error => {
                    console.error('Error:', error);
                    window.showError(error.message || '加载数据时发生错误，请稍后重试');
                });
        };

        window.renderChart = function (data) {
            data.nodes.forEach((node, index) => {
                node.itemStyle = {
                    color: window.colors[index % window.colors.length]
                };
            });

            const option = {
                backgroundColor: '#F5F5F5',
                title: {
                    show: false
                },
                tooltip: {
                    confine: true,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    borderColor: '#ccc',
                    borderWidth: 1,
                    padding: [8, 12],
                    textStyle: {
                        color: '#333',
                        fontSize: 12,
                        lineHeight: 20
                    },
                    formatter: function (params) {
                        if (params.dataType === 'node') {
                            const diseases = window.formatDiseases(params.data.diseases);
                            return `<div style="min-width: 150px; max-width: 300px;">
                                <div style="font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #eee; padding-bottom: 5px; word-break: break-all;">
                                    ${params.data.name}
                                </div>
                                <div style="margin: 5px 0">
                                    <span style="color: #666">感染数：</span>${params.data.value}
                                </div>
                                <div style="margin: 5px 0">
                                    <span style="color: #666">疾病：</span>
                                    <div style="margin-top: 3px; white-space: pre-line; word-break: break-all;">${diseases}</div>
                                </div>
                            </div>`;
                        } else {
                            const diseases = window.formatDiseases(params.data.diseases);
                            return `<div style="min-width: 150px; max-width: 300px;">
                                <div style="font-weight: bold; margin-bottom: 8px; border-bottom: 1px solid #eee; padding-bottom: 5px; word-break: break-all;">
                                    ${params.data.source} → ${params.data.target}
                                </div>
                                <div style="margin: 5px 0">
                                    <span style="color: #666">关联：</span>${params.data.value}
                                </div>
                                <div style="margin: 5px 0">
                                    <span style="color: #666">疾病：</span>
                                    <div style="margin-top: 3px; white-space: pre-line; word-break: break-all;">${diseases}</div>
                                </div>
                            </div>`;
                        }
                    }
                },
                graphic: [{
                    type: 'text',
                    left: 20,
                    top: 20,
                    style: {
                        text: `节点大小表示感染规模，连线表示疾病传播关系\n共 ${data.nodes.length} 个地区, ${data.edges.length} 个关联`,
                        fontSize: 12,
                        fill: '#666',
                        width: 300,
                        overflow: 'break'
                    }
                }],
                series: [{
                    type: 'graph',
                    layout: 'force',
                    force: {
                        repulsion: 100,
                        gravity: 0.1,
                        edgeLength: 50,
                        layoutAnimation: true
                    },
                    data: data.nodes.map(node => ({
                        ...node,
                        symbolSize: node.symbolSize,
                        x: undefined,
                        y: undefined
                    })),
                    links: data.edges,
                    categories: data.nodes.map(node => ({ name: node.name })),
                    roam: true,
                    label: {
                        show: true,
                        position: 'right',
                        formatter: '{b}',
                        fontSize: 12,
                        color: '#333',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)',
                        padding: [4, 8],
                        borderRadius: 3
                    },
                    edgeSymbol: ['none', 'arrow'],
                    edgeSymbolSize: [0, 8],
                    lineStyle: {
                        color: '#2f4554',
                        opacity: 0.6,
                        width: 1,
                        curveness: 0.3,
                        type: 'solid'
                    },
                    emphasis: {
                        focus: 'adjacency',
                        lineStyle: {
                            width: 4,
                            opacity: 1
                        },
                        label: {
                            show: true
                        }
                    }
                }]
            };

            window.myChart.setOption(option);
        };

        // Initialize jQuery features when document is ready
        $(document).ready(function () {
            console.log("Document ready function started");

            // Initialize Semantic UI dropdown
            $('.ui.dropdown').dropdown({
                fullTextSearch: true,
                match: 'text',
                forceSelection: false,
                selectOnKeydown: false,
                showOnFocus: true,
                allowAdditions: false,
                onChange: function (value) {
                    console.log("Disease selection changed to:", value);
                    // When disease selection changes, refetch all data
                    window.fetchAllData();
                }
            });
            console.log("Dropdown initialized");

            // Set up resize event listener
            window.addEventListener('resize', function () {
                if (window.myChart) {
                    window.myChart.resize();
                }
            });

            // Load disease data
            fetch('/get_dict_config')
                .then(response => response.json())
                .then(data => {
                    if (data.diseases) {
                        const diseaseList = $('#diseaseList');
                        console.log("Fetched diseases:", data.diseases.length);

                        data.diseases.forEach(disease => {
                            window.diseaseDict[disease.data_value] = disease;

                            // 添加到下拉菜单
                            diseaseList.append(`
                                <div class="item" data-value="${disease.data_value}">
                                    <div class="ui green empty circular label"></div>
                                    ${disease.name}
                                </div>
                            `);
                        });

                        console.log("Refreshing dropdown after adding items");
                        // 重新初始化下拉框
                        $('.ui.dropdown').dropdown('refresh');
                    }
                })
                .catch(error => {
                    console.error('Error loading disease dictionary:', error);
                });
        });
    </script>

    <!-- React component for the slider -->
    <script type="text/javascript">
        const { Slider } = arco;
        const { useState, useEffect } = React;

        function TimeRangeSlider() {
            // Get date range for the past year
            const oneYearAgo = new Date();
            oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
            const today = new Date();

            // Default to last 30 days
            const defaultValue = [
                today.getTime() - (30 * 24 * 60 * 60 * 1000),
                today.getTime()
            ];

            const [value, setValue] = useState(defaultValue);

            // Initialize current time range on component mount
            useEffect(() => {
                window.currentTimeRange = {
                    startDate: new Date(value[0]),
                    endDate: new Date(value[1])
                };
                // Use more readable date format for display
                document.getElementById('dateRangeText').textContent =
                    `${window.formatReadableDate(new Date(value[0]))} - ${window.formatReadableDate(new Date(value[1]))}`;

                // Initialize the chart after React component is mounted
                window.initChart();
            }, []);

            // Format the tooltip shown when slider is moved
            const formatTooltip = (val) => {
                return window.formatReadableDate(new Date(val));
            };

            // When slider value changes
            const handleChange = (vals) => {
                setValue(vals);
                window.currentTimeRange = {
                    startDate: new Date(vals[0]),
                    endDate: new Date(vals[1])
                };
                window.updateTimeFilteredChart();
            };

            // Generate marks for each month
            const generateMonthlyMarks = () => {
                const marks = {};
                const current = new Date(oneYearAgo);

                // Reset to first day of month
                current.setDate(1);

                // Create a mark for each month until today
                while (current <= today) {
                    const timestamp = current.getTime();

                    // Format as YYYY-MM (year-month)
                    marks[timestamp] = window.formatYearMonth(current);

                    // Move to next month
                    current.setMonth(current.getMonth() + 1);
                }

                return marks;
            };

            return React.createElement(Slider, {
                style: { width: '100%' },
                min: oneYearAgo.getTime(),
                max: today.getTime(),
                step: 24 * 60 * 60 * 1000, // One day step
                range: {
                    draggableBar: true
                },
                defaultValue: defaultValue,
                formatTooltip: formatTooltip,
                onAfterChange: handleChange,
                marks: generateMonthlyMarks()
            });
        }

        // Render the React component when the DOM is loaded
        document.addEventListener('DOMContentLoaded', function () {
            const container = document.getElementById('timeRangeSlider');
            const root = ReactDOM.createRoot(container);
            root.render(React.createElement(TimeRangeSlider));
        });
    </script>
</body>

</html>