{% extends "main.html" %}

{% block content %}
<style>
    /* 简单的表格高度修复 */
    #myGrid {
        height: calc(100vh - 200px) !important;
        /* 视口高度减去导航栏和其他元素的高度 */
        min-height: 400px !important;
        /* 最小高度保证 */
    }

    /* 修复jQuery UI Dialog关闭按钮图标 */
    .ui-dialog-titlebar-close {
        position: absolute !important;
        right: 0.3em !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        width: 20px !important;
        height: 20px !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background: transparent !important;
        cursor: pointer !important;
    }

    .ui-dialog-titlebar-close:before {
        content: "×" !important;
        font-size: 18px !important;
        font-weight: bold !important;
        color: #666 !important;
        position: absolute !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        line-height: 1 !important;
        width: 20px !important;
        height: 20px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
    }

    .ui-dialog-titlebar-close:hover:before {
        color: #000 !important;
        background-color: rgba(0, 0, 0, 0.1) !important;
        border-radius: 3px !important;
    }

    .ui-dialog-titlebar-close .ui-button-icon,
    .ui-dialog-titlebar-close .ui-icon {
        display: none !important;
    }

    /* 确保关闭按钮可见 */
    .ui-dialog-titlebar-close span {
        display: none !important;
    }

    /* 确保标题栏有足够的相对定位 */
    .ui-dialog-titlebar {
        position: relative !important;
    }
</style>

<div class="filter-wrapper" style="margin-top: 5px;">
    <div class=" ui left icon action input" style="width: 45%;">
        <i class="search icon"></i>
        <input type="text" id="customSearchBox" placeholder="请输入关键词...">
        <div class="ui buttons">
            <button class="ui primary button" id="searchButton1">搜索</button>
            <div class="or"></div>
            <button class="ui primary button" id="searchButton2">双语搜索</button>
        </div>
    </div>
    <div class="ui left icon input">
        <i class="calendar alternate icon"></i>
        <input type="text" id="dateRange" class="filter-box date-input" placeholder="日期范围">
    </div>
    <!-- 开关样式开始 -->
    <div class="radio-inputs">
        <label class="radio">
            <input id="newsSource" type="radio" name="radio" checked="">
            <span class="name">NewsAPI</span>
        </label>
        <label class="radio">
            <input id="newsSource" type="radio" name="radio">
            <span class="name">bing</span>
        </label>
    </div>
    <!-- 开关样式结束 -->
    <button id="generateButton" class="ui compact labeled icon green button">
        <i class="pencil alternate icon"></i>
        AI生成
    </button>
    <button id="generateConfig" class="ui compact labeled icon teal button" style="display: none;">
        <i class="cog icon"></i>
        AI调用配置
    </button>
</div>

<div id="myGrid" style="width:100%; margin-top:10px;" class="ag-theme-quartz"></div>

<!-- 新闻检索功能相关的JavaScript代码 -->
<script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
<script>
    var startTime;
    var newsContent = '';
    var quillInstance = null;

    // 暴露给框架调用的配置函数
    window.triggerGenerateConfig = function () {
        $("#generateConfig").click();
    };

    function checkNewsSource() {
        var radioButton = document.querySelector("input[name='radio']:checked");
        //检查所选项是否为 "bing"
        if (radioButton != null && radioButton.nextElementSibling.textContent === "bing") {
            // 初始化并打开对话框
            $("#dialog-message").dialog({
                modal: true,
                width: 'auto', // 自动调整宽度
                buttons: {
                    关闭: function () {
                        $(this).dialog("close");
                    }
                }
            });
        }
    }

    function fetchNewsContent(newsTitle, newsUrl) {
        $.ajax({
            url: '/get_news_content',
            type: 'POST',
            contentType: 'application/json',
            dataType: 'json',
            data: JSON.stringify({ url: newsUrl }),
            success: function (response) {
                if (response.status) {
                    // 当数据接收成功后，将内容显示在Quill编辑器中
                    newsContent = response.content;
                    displayNewsContent(newsTitle, newsContent);
                } else {
                    displayNewsContent(newsTitle, '获取新闻内容失败: ' + response.message);
                }
            },
            error: function (xhr, status, error) {
                alert('错误: ' + error);
            }
        });
    }

    // 显示新闻内容在Quill编辑器中
    function displayNewsContent(title, content) {
        if (!quillInstance) {
            quillInstance = new Quill('#newsContentText', {
                theme: 'snow',
                readOnly: true
            });
        }
        // 清空之前的内容
        quillInstance.setText('');
        // 转换文本为HTML并显示
        const htmlContent = convertTextToHTML('<b>新闻标题：</b>' + title + '<br><br>' + content);
        quillInstance.clipboard.dangerouslyPasteHTML(htmlContent);
        // 打开模态窗口
        $('#newsContentModal').dialog('open');
    }

    var gridOptions = {
        pagination: true,  // 启用分页
        paginationPageSize: 10,  // 每页显示10条数据
        paginationPageSizeSelector: [10, 20, 50, 100],
        overlayLoadingTemplate: '<span class="ag-overlay-loading-center">暂无数据...</span>',
        localeText: AG_GRID_LOCALE_ZH,
        columnDefs: [
            {
                headerName: "#",
                valueGetter: (params) => parseInt(params.node.id) + 1,
                width: 50,
                filter: false,
                suppressSizeToFit: true,
                cellClass: "cell-text-center", // 居中对齐
            },
            {
                headerName: "",
                field: "checkbox",
                filter: false,
                checkboxSelection: true,
                headerCheckboxSelection: true,
                width: 50,
                suppressSizeToFit: true,
                cellClass: "cell-text-center", // 居中对齐
            },
            {
                headerName: "新闻标题",
                field: "title",
                flex: 2.5,
                minWidth: 100,
                cellStyle: { "white-space": "normal", "line-height": "30px" },
                cellClass: "cell-text-left-bold", // 左对齐
                autoHeight: true,
            },
            {
                headerName: "发布时间",
                field: "publishedAt",
                flex: 1,
                minWidth: 100,
                cellClass: "cell-text-center", // 居中对齐
            },
            {
                headerName: "新闻摘要",
                field: "description",
                flex: 4,
                minWidth: 100,
                cellStyle: { "white-space": "normal", "line-height": "30px" },
                cellClass: "cell-text-left", // 左对齐
                autoHeight: true,
            },
            {
                headerName: "新闻来源",
                field: "source",
                flex: 1.2,
                sortable: true,
                filter: true,
                cellRenderer: function (params) {
                    var country = params.data.country;
                    var sourceName = params.data.source;
                    var flexCenterStyle = 'display: flex; align-items: center; height: 28px;';
                    var sourceHtml = '<div style="' + flexCenterStyle + '">';
                    sourceHtml += '<div class="ui basic teal label" style="' + flexCenterStyle + '">' + sourceName + '</div>';
                    if (country) {
                        sourceHtml += '<div class="ui basic teal label" style="' + flexCenterStyle + '">' + country + '</div>';
                    }
                    sourceHtml += '</div>';
                    return sourceHtml;
                },
                cellStyle: { "white-space": "normal", "line-height": "30px" },
                cellClass: "cell-text-center", // 居中对齐
                valueGetter: function (params) {
                    // 返回用于过滤的字符串
                    return params.data.source + ' ' + params.data.country;
                },
            },
            {
                headerName: "URL",
                field: "url",
                flex: 0.8,
                cellRenderer: function (params) {
                    var buttonStyle = 'display: flex; align-items: center; justify-content: center; height: 28px;';
                    return '<button class="ui primary basic button openUrlButton" style="' + buttonStyle + '" data-url="' + params.value + '"><i class="linkify icon"></i>打开</button>';
                },
                cellStyle: { "white-space": "normal", "line-height": "30px" },
                cellClass: "cell-text-center" // 居中对齐
            }
        ],
        onCellClicked: function (event) {
            // 检查是否点击了具有 'openUrlButton' class 的元素
            if (event.event.target.classList.contains('openUrlButton') || event.event.target.closest('.openUrlButton')) {
                var buttonElement = event.event.target.closest('.openUrlButton');
                var newsUrl = buttonElement.getAttribute('data-url');
                var newsTitle = event.data.title;
                showLoading(newsTitle, newsUrl);
                fetchNewsContent(newsTitle, newsUrl);
            }
        },
        defaultColDef: {
            autoHeight: true,
            filter: true,
            resizable: true,
            sortable: true,
        },
        suppressRowClickSelection: true,
        rowSelection: 'multiple',
    };

    $(document).ready(function () {
        // 新闻源开关提示 - 安全地添加事件监听器
        var radioButtons = document.querySelectorAll("input[name='radio']");
        radioButtons.forEach(function (radioButton) {
            if (radioButton) {
                radioButton.addEventListener('change', checkNewsSource);
            }
        });

        var endDate = new Date().toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '/');
        var startDate = new Date(new Date().setDate(new Date().getDate() - 7)).toLocaleString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' }).replace(/\//g, '/');

        flatpickr("#dateRange", {
            mode: "range",
            maxDate: "today",
            locale: "zh", // 设置本地化为中文
            dateFormat: "Y-m-d",
            defaultDate: [startDate, endDate] // 设置默认日期，开始是7天前，结束是今天
        });

        // AG Grid实例化
        var gridApi = new agGrid.Grid(document.getElementById('myGrid'), gridOptions).gridOptions.api;

        // 从服务器获取数据并加载到AG Grid
        function fetchAndLoadNewsData(gridApi, lang) {
            var customSearch = $('#customSearchBox').val();
            var dateRange = $('#dateRange').val();
            var dates = dateRange.split(" 至 ");
            var startDate = dates[0] ? dates[0].trim() : '';
            var endDate = dates[1] ? dates[1].trim() : '';
            var newsSource = 'false';
            var radioButton = document.querySelector("input[name='radio']:checked");

            // 检查 customSearch 是否为空
            if (!customSearch) {
                alert("关键字不能为空。");
                return; // 提早返回，不发送请求
            }

            //检查所选项是否为 "bing"
            if (radioButton != null && radioButton.nextElementSibling.textContent === "bing") {
                var newsSource = 'true';
            }

            // 构建请求URL
            var fetchUrl = '/fetch_news?customSearch=' + encodeURIComponent(customSearch) +
                '&startDate=' + encodeURIComponent(startDate) +
                '&endDate=' + encodeURIComponent(endDate) +
                '&newsSource=' + newsSource +
                '&lang=' + lang;

            //检查是否会话超时
            checkSession();

            // 发送GET请求获取新闻数据
            $.getJSON(fetchUrl, function (response) {
                // 加载数据到AG Grid
                gridApi.setGridOption('rowData', response.data);
            }).fail(function (jqXHR, textStatus, errorThrown) {
                console.error('Error fetching news: ', textStatus, errorThrown);
            });
        }

        // 设置搜索按钮动作
        $('#searchButton1').click(function () {
            fetchAndLoadNewsData(gridOptions.api, "");
        });

        $('#searchButton2').click(function () {
            fetchAndLoadNewsData(gridOptions.api, "bilingual");
        });

        // 显示进度条模态
        function showProgressModal() {
            $('#progressModal').css('display', 'block');
            $('button').prop('disabled', true); // 禁用所有按钮
        }

        // 隐藏进度条模态
        function hideProgressModal() {
            $('#progressModal').css('display', 'none');
            $('button').prop('disabled', false); // 启用所有按钮
        }

        // "AI生成"按钮点击事件
        $('#generateButton').click(function () {
            var selectedData = gridApi.getSelectedRows(); // 获取所有已选中的行的数据
            //检查是否会话超时
            checkSession();

            if (selectedData.length === 0) { // 检查是否有行被选中
                alert('提示：请至少选择一条新闻.');
            } else {
                var aiTypeName = '';
                $.ajax({
                    url: 'get_config', // 配置文件地址
                    type: 'GET',
                    cache: false, // 禁用缓存
                    dataType: 'json',
                    success: function (config) {
                        switch (config.ai_model) {
                            case 'gpt-4o-mini':
                                aiTypeName = 'OpenAI-ChatGPT-4o mini';
                                break;
                            case 'gpt-4-0125-preview':
                                aiTypeName = 'OpenAI-ChatGPT-4';
                                break;
                            case 'gpt-4o':
                                aiTypeName = 'OpenAI-ChatGPT-4o';
                                break;
                            case 'claude-3-5-sonnet-20240620':
                                aiTypeName = 'Anthropic-Claude3.5';
                                break;
                            case 'gemini-pro':
                                aiTypeName = 'Google-Gemini';
                                break;
                            case 'gemma':
                                aiTypeName = 'Google-Gemma';
                                break;
                            case 'sensenova':
                                aiTypeName = '商汤-SenseNova';
                                break;
                            case 'qwen':
                                aiTypeName = '阿里-通义千问';
                                break;
                            default:
                                aiTypeName = '';
                                break;
                        }
                        var confirmMessage = "您确定要通过【" + aiTypeName + "】生成 " + selectedData.length + " 条新闻吗？";
                        var userConfirmed = confirm(confirmMessage);

                        if (userConfirmed) {
                            showProgressModal(); // 点击生成按钮时显示进度条模态窗口
                            $.ajax({
                                url: 'ai_generate', // 这里应该改为你的后端处理URL
                                type: 'POST',
                                dataType: 'json', // 期待的响应类型
                                data: JSON.stringify({ selectedItems: selectedData }),
                                contentType: 'application/json', // 发送数据的格式
                                success: function (response) {
                                    hideProgressModal(); // 请求完成后隐藏进度条模态窗口
                                    // 假设服务器返回的是JSON对象，并且你设置了dataType为'json'
                                    if (response.status === 'success') {
                                        // 操作成功
                                        alert('提示: ' + response.message);
                                        var fileUrl = response.fileUrl; // 后端提供的文件URL
                                        var link = document.createElement('a');
                                        link.href = fileUrl;
                                        link.download = fileUrl.substr(fileUrl.lastIndexOf('/') + 1);
                                        document.body.appendChild(link);
                                        link.click();
                                        document.body.removeChild(link);
                                    } else {
                                        // 操作失败，但HTTP请求本身是成功的
                                        alert('提示: ' + response.message);
                                    }
                                },
                                error: function (xhr, status, error) {
                                    // 如果有错误发生，可以在这里处理
                                    hideProgressModal(); // 请求完成后隐藏进度条模态窗口
                                    alert('错误: ' + error);
                                }
                            });
                        }
                    }
                });
            }
        });

        // 初始化模式窗口
        $("#aiConfigModal").dialog({
            autoOpen: false,
            height: 700,
            width: 950,
            modal: true,
            buttons: {
                "保存": function () {
                    var configData = {
                        prompt_text: $("#promptText").val(),
                        api_endpoint: $("#apiEndpoint").val(),
                        api_key: $("#apiKey").val(),
                        ai_model: $("#aiModel").val(),
                        ai_type: $("#aiType").val()
                    };
                    // 发送数据到服务器保存配置
                    $.ajax({
                        url: 'save_config',
                        type: 'POST',
                        dataType: 'json', // 期待的响应类型
                        contentType: 'application/json', // 设置发送数据的格式为 JSON
                        data: JSON.stringify(configData), // 将对象转换为 JSON 字符串
                        success: function (response) {
                            // 假设服务器返回的是JSON对象，并且你设置了dataType为'json'
                            if (response.status === 'success') {
                                // 操作成功
                                alert('提示: ' + response.message);
                            } else {
                                // 操作失败，但HTTP请求本身是成功的
                                alert('提示: ' + response.message);
                            }
                        },
                        error: function (xhr, status, error) {
                            // 如果有错误发生，可以在这里处理
                            alert('错误: ' + error);
                        }
                    });
                    $(this).dialog("close");
                },
                "关闭": function () {
                    $(this).dialog("close");
                }
            },
            create: function () {
                // 更改按钮颜色
                var buttons = $(this).parent().find('.ui-dialog-buttonpane button');
                buttons.each(function () {
                    var buttonText = $(this).text();
                    if (buttonText === '保存') {
                        $(this).addClass('ui-button-success').css({ 'background-color': '#28a745', 'color': 'white' });
                    } else if (buttonText === '关闭') {
                        $(this).addClass('ui-button-danger').css({ 'background-color': '#dc3545', 'color': 'white' });
                    }
                });
            }
        });

        // 给"AI调用配置"按钮添加点击事件
        $("#generateConfig").click(function () {
            //检查是否会话超时
            checkSession();
            $.ajax({
                url: 'get_config', // 配置文件地址
                type: 'GET',
                cache: false, // 禁用缓存
                dataType: 'json',
                success: function (config) {
                    // 读取配置并赋值给表单
                    $("#promptText").val(config.prompt_text);
                    $("#apiEndpoint").val(config.api_endpoint);
                    $("#apiKey").val(config.api_key);
                    $("#aiModel").val(config.ai_model);
                }
            });
            // 打开模式窗口
            $("#aiConfigModal").dialog("open");
        });

        // 初始化新闻内容模态窗口
        $("#newsContentModal").dialog({
            autoOpen: false,
            height: 720,
            width: 1280,
            modal: true,
            open: function () {
                if (!quillInstance) {
                    quillInstance = new Quill('#newsContentText', {
                        theme: 'snow',
                        readOnly: true
                    });
                }
                if (newsContent) {
                    quillInstance.clipboard.dangerouslyPasteHTML(convertTextToHTML(newsContent));
                }
            },
            beforeClose: function (event, ui) {
                // 在模态窗口关闭前清空newsContent的内容
                if (quillInstance) {
                    newsContent = "";
                }
            },
            buttons: {
                "关闭": function () {
                    $(this).dialog("close");
                }
            }
        });

        // 页面加载完成后立即计算高度
        setTimeout(function () {
            window.calculateGridHeight();
        }, 100);

        // 再次确保高度正确
        setTimeout(function () {
            window.calculateGridHeight();
        }, 500);

        // 窗口大小改变时重新计算
        var resizeTimer;
        $(window).resize(function () {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(function () {
                window.calculateGridHeight();
            }, 100);
        });
    });

    function showLoading(title, url) {
        //检查是否会话超时
        checkSession();
        if (!quillInstance) {
            // 如果quill实例还没有初始化
            quillInstance = new Quill('#newsContentText', {
                theme: 'snow',
                readOnly: true
            });
        }
        // 设置Quill编辑器的内容为加载提示
        quillInstance.setText('正在载入，请耐心等待...\n\n');
        quillInstance.insertText(quillInstance.getLength(), title + '\n');
        quillInstance.insertText(quillInstance.getLength(), url + '\n', { 'link': url });

        quillInstance.formatText(0, 14, {
            'bold': true,
            'size': 'large'
        });
        // 打开模态窗口
        $('#newsContentModal').dialog('open');
    }

    function convertTextToHTML(text) {
        // 首先转换换行符为HTML的<br>标签
        let htmlContent = text.replace(/\n/g, '<br>');

        // 然后将URL转换为超链接
        // 这个正则表达式可以匹配大部分URL
        const urlRegex = /(\b(https?|ftp|file):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/ig;
        htmlContent = htmlContent.replace(urlRegex, function (url) {
            // 创建一个a标签来包裹URL，使其可点击
            return '<a href="' + url + '" target="_blank">' + url + '</a>';
        });

        return htmlContent;
    }

    function openDialog() {
        $("#dialog-message").dialog({
            modal: true,
            buttons: {
                Ok: function () {
                    $(this).dialog("close");
                }
            }
        });
    }

    // 简化的高度计算函数
    window.calculateGridHeight = function () {
        var container = document.querySelector('.news-search-container');
        var filterWrapper = document.querySelector('.filter-wrapper');
        var gridElement = document.getElementById('myGrid');

        if (!container || !filterWrapper || !gridElement) {
            console.log('Elements not found, skipping height calculation');
            return;
        }

        var containerHeight = container.offsetHeight;
        var filterHeight = filterWrapper.offsetHeight;
        var availableHeight = containerHeight - filterHeight - 20; // 20px for margins

        console.log('优化高度计算:');
        console.log('容器高度:', containerHeight, '筛选区域高度:', filterHeight);
        console.log('可用高度:', availableHeight);

        // 确保最小高度 - 调整为与CSS一致
        var finalHeight = Math.max(availableHeight, 220);

        // 应用高度
        gridElement.style.height = finalHeight + 'px';

        // 通知AG Grid重新布局
        if (window.gridApi) {
            setTimeout(function () {
                if (window.gridApi.sizeColumnsToFit) {
                    window.gridApi.sizeColumnsToFit();
                }
                if (window.gridApi.redrawRows) {
                    window.gridApi.redrawRows();
                }
            }, 100);
        }

        return finalHeight;
    };
</script>

<!-- 进度条模态窗口HTML -->
<div id="progressModal" class="modal">
    <div class="modal-content">
        <h3>正在进行AI生成，请耐心等待...</h3>
        <h4>提示：请勿刷新页面！</h4>
        <div class="progress">
            <div class="indeterminate"></div>
        </div>
    </div>
</div>

<!-- 新闻内容模态窗口HTML -->
<div id="newsContentModal" title="新闻内容" style="display:none;">
    <div id="newsContentText" style="height:92%;"></div>
</div>

<!-- AI调用配置模态窗口HTML -->
<div id="aiConfigModal" title="AI调用配置" style="display:none;">
    <label for="apiEndpoint" class="ui label">接口地址:</label>
    <div class="ui fluid right input">
        <input type="text" placeholder="请输入接口地址..." id="apiEndpoint" style="margin-top: 3px;">
    </div>
    <label for="apiKey" class="ui label" style="margin-top: 10px;">API Key:</label>
    <div class="ui fluid right input">
        <input type="text" placeholder="请输入API Key..." id="apiKey" style="margin-top: 3px;">
    </div>
    <label for="aiModel" class="ui label" style="margin-top: 10px;">AI模型</label>
    <select class="ui fluid dropdown" id="aiModel" style="margin-top: 3px;">
        <option value="gpt-4o-mini">OpenAI-ChatGPT-4o mini</option>
        <option value="gpt-4o">OpenAI-ChatGPT-4o</option>
        <option value="claude-3-5-sonnet-20240620">Anthropic-Claude3.5</option>
        <option value="gemini-pro">Google-Gemini</option>
        <option value="gemma">Google-Gemma</option>
        <option value="sensenova">商汤-SenseNova</option>
        <option value="qwen">阿里-通义千问</option>
    </select>
    <label for="aiModel" class="ui label" style="margin-top: 10px;">提示词</label>
    <div class="ui form" style="height: 300px;">
        <div class="field" style="height: 100%;">
            <textarea id="promptText" class="ui fluid textarea" style="height: 100%;margin-top: 3px;"></textarea>
        </div>
    </div>
</div>

<div id="dialog-message" title="bing search使用说明" style="display:none;">
    <p>
        使用bing search功能，请注意以下几点：
    <ul>
        <li>bing搜索api：<b>1000次/20美元</b>，请适度使用。</li>
        <li>bing搜索来源：已限定检索<b>国内新闻源</b>的数据。</li>
        <li>bing搜索限制：每次检索最多返回<b>50条记录</b>。</li>
        <li>bing搜索日期：bing <b>不支持日期范围搜索</b>，只返回关键字所对应的最新资讯。</li>
    </ul>
    </p>
</div>
{% endblock %}