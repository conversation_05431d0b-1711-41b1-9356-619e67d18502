"""
CDC风险评估报告AI分析接口
处理PDF内容的AI分析请求
"""

from flask import jsonify, request
import json
import os
from openai import OpenAI
from routes.pdf_extractor import extract_pdf_text, validate_pdf_url
from routes.cdc_report_analyzer import (
    get_analysis_prompt,
    format_analysis_result,
    generate_html_report,
)


def handler():
    """
    处理CDC报告AI分析请求

    Returns:
        JSON响应包含分析结果
    """
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "请求数据为空"}), 400

        pdf_url = data.get("pdf_url", "").strip()
        pdf_text = data.get("pdf_text", "").strip()  # 允许直接提供文本内容

        if not pdf_url and not pdf_text:
            return jsonify(
                {"success": False, "error": "请提供PDF URL或PDF文本内容"}
            ), 400

        # 如果没有提供文本内容，则从PDF URL提取
        if not pdf_text and pdf_url:
            # 验证PDF URL
            is_valid, error_msg = validate_pdf_url(pdf_url)
            if not is_valid:
                return jsonify(
                    {"success": False, "error": f"PDF URL无效: {error_msg}"}
                ), 400

            # 提取PDF文本
            print(f"开始提取PDF文本: {pdf_url}")
            pdf_text = extract_pdf_text(pdf_url)

            # 检查提取结果
            if pdf_text.startswith("错误："):
                return jsonify({"success": False, "error": pdf_text}), 500

            if not pdf_text.strip():
                return jsonify({"success": False, "error": "PDF文本提取为空"}), 500

            print(f"PDF文本提取成功，长度: {len(pdf_text)} 字符")
        elif pdf_text:
            print(f"使用提供的文本内容，长度: {len(pdf_text)} 字符")
        else:
            return jsonify({"success": False, "error": "无法获取PDF文本内容"}), 400

        # 调用AI分析
        print("开始AI分析...")
        ai_result = call_ai_analysis(pdf_text)

        if ai_result.get("success"):
            # 格式化分析结果
            formatted_result = format_analysis_result(ai_result["content"])

            if formatted_result.get("success"):
                # 生成HTML报告
                html_report = generate_html_report(formatted_result)

                return jsonify(
                    {
                        "success": True,
                        "data": {
                            "analysis": formatted_result["data"],
                            "html_report": html_report,
                            "pdf_url": pdf_url,
                            "text_length": len(pdf_text),
                        },
                    }
                )
            else:
                return jsonify(
                    {
                        "success": False,
                        "error": formatted_result.get("error", "分析结果格式化失败"),
                        "raw_ai_response": ai_result["content"],
                    }
                ), 500
        else:
            return jsonify(
                {"success": False, "error": ai_result.get("error", "AI分析失败")}
            ), 500

    except Exception as e:
        print(f"CDC报告AI分析异常: {str(e)}")
        return jsonify({"success": False, "error": f"服务器内部错误: {str(e)}"}), 500


def call_ai_analysis(pdf_text):
    """
    调用AI进行报告分析

    Args:
        pdf_text (str): PDF提取的文本内容

    Returns:
        dict: AI分析结果
    """
    try:
        # 加载API配置
        config = load_api_config()
        if not config.get("api_endpoint") or not config.get("api_key"):
            return {
                "success": False,
                "error": "AI API配置不完整，请检查api_config.json文件",
            }

        # 获取分析提示词
        prompt = get_analysis_prompt()

        # 构建完整的用户消息
        user_message = prompt + pdf_text

        # 检查文本长度，如果太长需要截断
        max_length = 100000  # 约10万字符限制
        if len(user_message) > max_length:
            print(f"文本过长({len(user_message)}字符)，截断到{max_length}字符")
            # 保留提示词，截断PDF文本
            prompt_length = len(prompt)
            available_length = max_length - prompt_length - 100  # 留一些缓冲
            truncated_pdf_text = pdf_text[:available_length] + "\n\n[文本已截断]"
            user_message = prompt + truncated_pdf_text

        # 创建OpenAI客户端
        client = OpenAI(base_url=config["api_endpoint"], api_key=config["api_key"])

        # 尝试使用gpt-5-mini，如果不存在则使用gpt-4o-mini
        models_to_try = ["gpt-5-mini", "gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo"]

        for model in models_to_try:
            try:
                print(f"尝试使用模型: {model}")
                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {
                            "role": "system",
                            "content": "你是一位专业的公共卫生专家，擅长分析中国疾控中心发布的风险评估报告。请严格按照要求的JSON格式输出分析结果。",
                        },
                        {"role": "user", "content": user_message},
                    ],
                    temperature=0.3,  # 降低随机性，提高一致性
                    max_tokens=4000,  # 限制输出长度
                )

                ai_content = response.choices[0].message.content
                print(
                    f"AI分析完成，使用模型: {model}，响应长度: {len(ai_content)} 字符"
                )

                return {"success": True, "content": ai_content, "model_used": model}

            except Exception as model_error:
                print(f"模型 {model} 调用失败: {str(model_error)}")
                continue

        # 所有模型都失败了
        return {
            "success": False,
            "error": "所有AI模型调用都失败了，请检查API配置和网络连接",
        }

    except Exception as e:
        print(f"AI分析调用异常: {str(e)}")
        return {"success": False, "error": f"AI分析调用失败: {str(e)}"}


def load_api_config():
    """
    加载API配置

    Returns:
        dict: API配置信息
    """
    config_details = {}
    try:
        # 获取上一级目录的路径
        current_directory = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_path = os.path.join(current_directory, "api_config.json")

        if os.path.exists(config_path):
            with open(config_path, "r", encoding="utf-8-sig") as json_file:
                config = json.load(json_file)
                config_details["api_endpoint"] = config.get("api_endpoint", "")
                config_details["api_key"] = config.get("api_key", "")
                config_details["ai_model"] = config.get("ai_model", "gpt-4o-mini")
        else:
            print(f"API配置文件不存在: {config_path}")

    except Exception as e:
        print(f"加载API配置失败: {str(e)}")

    return config_details


def test_analysis(test_pdf_url):
    """
    测试分析功能

    Args:
        test_pdf_url (str): 测试用的PDF URL
    """
    print(f"测试CDC报告AI分析功能")
    print(f"测试PDF URL: {test_pdf_url}")

    # 模拟请求数据
    test_data = {"pdf_url": test_pdf_url}

    # 这里可以添加测试逻辑
    print("测试完成")


if __name__ == "__main__":
    # 可以在这里添加测试代码
    pass
