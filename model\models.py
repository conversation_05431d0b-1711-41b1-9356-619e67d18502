from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from datetime import datetime

db = SQLAlchemy()


class User(UserMixin, db.Model):
    __tablename__ = "sys_users2"
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.String(32), unique=True, nullable=False)
    pwd = db.Column(db.String(255), nullable=False)
    user_name = db.Column(db.String(32), nullable=False)
    salt = db.Column(db.String(32), nullable=False)
    status = db.Column(db.Integer, nullable=False, default=1)  # 1: 启用, 0: 禁用
    email = db.Column(db.String(64), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    role = db.Column(
        db.String(20), nullable=False, default="user"
    )  # admin, user, viewer
    department = db.Column(db.String(50), nullable=True)
    created_time = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    updated_time = db.Column(
        db.DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow
    )
    last_login_time = db.Column(db.DateTime, nullable=True)
    remark = db.Column(db.String(255), nullable=True)

    def check_password(self, password):
        return check_password_hash(self.pwd, password + self.salt)


class Disaster(db.Model):
    __tablename__ = "t_disaster"
    id = db.Column(db.Integer, primary_key=True)
    time = db.Column(db.DateTime, nullable=False)
    cities = db.Column(db.String(50), nullable=False)
    disaster = db.Column(db.String(50), nullable=False)
    growth = db.Column(db.Float, nullable=True)
    deaths = db.Column(db.Integer, nullable=True)
    cures = db.Column(db.Integer, nullable=True)


class Country(db.Model):
    __tablename__ = "news_countries"
    data_value = db.Column(db.String(255), primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    capital_latitude = db.Column(db.Float)
    capital_longitude = db.Column(db.Float)
    population = db.Column(db.BigInteger)


class Disease(db.Model):
    __tablename__ = "news_diseases"
    data_value = db.Column(db.String(255), primary_key=True)
    name = db.Column(db.String(255), nullable=False)


class DisasterChina(db.Model):
    """中国疾控中心疾病数据模型"""

    __tablename__ = "t_disaster_china"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment="主键")
    time = db.Column(db.DateTime, comment="数据时间")
    disaster = db.Column(db.String(255), comment="疾病名称")
    disaster_type = db.Column(db.String(255), comment="疾病类别")
    sub_disaster = db.Column(db.String(255), comment="子疾病名称")
    cities = db.Column(db.String(255), comment="地点")
    source_url = db.Column(db.String(1000), comment="源网址")
    growth = db.Column(db.Integer, nullable=False, comment="增长数")
    deaths = db.Column(db.Integer, comment="死亡数")
    cures = db.Column(db.Integer, comment="治愈数")
    sub_growth = db.Column(db.Integer, nullable=False, comment="子疾病增长数")
    sub_deaths = db.Column(db.Integer, comment="子疾病死亡数")
    sub_cures = db.Column(db.Integer, comment="子疾病治愈数")
    update_time = db.Column(db.DateTime, comment="最后更新时间")


class DisasterShanghai(db.Model):
    """上海疾控中心疾病数据模型"""

    __tablename__ = "t_disaster_shanghai"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment="主键")
    time = db.Column(db.DateTime, comment="数据时间")
    disaster = db.Column(db.String(255), comment="疾病名称")
    disaster_type = db.Column(db.String(255), comment="疾病类别")
    sub_disaster = db.Column(db.String(255), comment="子疾病名称")
    cities = db.Column(db.String(255), comment="地点")
    source_url = db.Column(db.String(1000), comment="源网址")
    local_hukou = db.Column(db.Integer, nullable=False, comment="本市户籍人口增长数")
    nonlocal_hukou = db.Column(
        db.Integer, nullable=False, comment="非本市户籍人口增长数"
    )
    total_hukou = db.Column(db.Integer, nullable=False, comment="总人口增长数")
    update_time = db.Column(db.DateTime, comment="最后更新时间")


class GlobalRiskAssessment(db.Model):
    """全球传染病事件风险评估数据模型"""

    __tablename__ = "t_global_risk_assessment"

    id = db.Column(db.Integer, primary_key=True, autoincrement=True, comment="主键")
    disease = db.Column(db.String(255), comment="疾病名称")
    continent = db.Column(db.String(255), comment="大洲")
    country = db.Column(db.String(255), comment="国家")
    start_date = db.Column(db.String(255), comment="统计起始日期")
    end_date = db.Column(db.String(255), comment="报告截止日期")
    total_cases = db.Column(db.String(255), comment="累计病例数")
    suspected_cases = db.Column(db.String(255), comment="疑似病例数")
    confirmed_cases = db.Column(db.String(255), comment="确诊病例数")
    cumulative_deaths = db.Column(db.String(255), comment="累计死亡数")
    import_risk_level = db.Column(db.String(50), comment="境外输入风险等级")
    travel_risk_level = db.Column(db.String(50), comment="海外旅行风险等级")
    data_time = db.Column(db.String(255), comment="数据时间")
    data_source = db.Column(db.String(1000), comment="数据来源")
    source_url = db.Column(db.String(1000), comment="源网址")
    update_time = db.Column(db.String(255), comment="最后更新时间")
