<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球传染病事件风险评估</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="https://unpkg.com/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://unpkg.com/@fortawesome/fontawesome-free@6.5.1/css/all.min.css">
    
    <style>
        html, body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 0;
            height: 100%;
            overflow-x: hidden;
        }

        .page-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }

        .header h1 {
            margin: 0;
            font-size: 2.2rem;
            font-weight: 600;
        }

        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .controls {
            background: white;
            padding: 20px;
            margin: 20px 20px 0 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex-shrink: 0;
        }

        .map-container {
            background: white;
            margin: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        #worldMap {
            width: 100%;
            flex: 1;
            min-height: 400px;
        }
        
        .legend {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        
        .legend h5 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .legend-color {
            width: 20px;
            height: 15px;
            margin-right: 8px;
            border-radius: 3px;
        }
        
        .legend-text {
            font-size: 12px;
        }

        .clickable-legend {
            transition: all 0.2s ease;
        }

        .clickable-legend:hover {
            background-color: rgba(0,0,0,0.05);
            border-radius: 4px;
        }

        .clickable-legend.disabled {
            opacity: 0.4;
        }

        .risk-change-stats {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .risk-change-stats h5 {
            margin: 0 0 10px 0;
            font-size: 14px;
            font-weight: 600;
        }

        .stats-item {
            display: flex;
            align-items: center;
        }

        .stats-text {
            font-size: 12px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        
        .loading i {
            font-size: 2rem;
            margin-bottom: 10px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .btn-group .btn {
            border-radius: 0;
        }
        
        .btn-group .btn:first-child {
            border-top-left-radius: 0.25rem;
            border-bottom-left-radius: 0.25rem;
        }
        
        .btn-group .btn:last-child {
            border-top-right-radius: 0.25rem;
            border-bottom-right-radius: 0.25rem;
        }
        
        .form-control {
            border-radius: 0.25rem;
        }
        
        .tooltip-content {
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 12px;
            max-width: 400px;
            max-height: 60vh;  /* 最大高度为视口高度的60% */
            overflow-y: auto;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .tooltip-title {
            font-weight: bold;
            margin-bottom: 8px;
            color: #fff;
            font-size: 14px;
            border-bottom: 1px solid rgba(255,255,255,0.3);
            padding-bottom: 5px;
        }

        .tooltip-item {
            margin-bottom: 4px;
            line-height: 1.4;
        }

        /* 滚动条样式 */
        .tooltip-content::-webkit-scrollbar {
            width: 8px;
        }

        .tooltip-content::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            margin: 4px 0;
        }

        .tooltip-content::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.4);
            border-radius: 4px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .tooltip-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.6);
        }

        .tooltip-content::-webkit-scrollbar-thumb:active {
            background: rgba(255,255,255,0.8);
        }

        /* Firefox滚动条样式 */
        .tooltip-content {
            scrollbar-width: thin;
            scrollbar-color: rgba(255,255,255,0.4) rgba(255,255,255,0.1);
        }

        /* 地图控制按钮样式 */
        .map-controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .map-control-btn {
            width: 32px;
            height: 32px;
            border: none;
            background: #fff;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            color: #333;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0,0,0,0.2);
        }

        .map-control-btn:hover {
            background: #f0f0f0;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .map-control-btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="page-container">
        <div class="header">
            <h1><i class="fas fa-globe-americas"></i> 全球传染病事件风险评估</h1>
            <p>Global Infectious Disease Risk Assessment</p>
        </div>

        <div class="controls">
            <div class="row align-items-center">
                <div class="col-md-4">
                    <div class="btn-group w-100" role="group">
                        <button type="button" class="btn btn-outline-primary active" data-risk="import">境外输入风险</button>
                        <button type="button" class="btn btn-outline-primary" data-risk="travel">海外旅行风险</button>
                    </div>
                </div>
                <div class="col-md-4">
                    <select id="diseaseSelect" class="form-control">
                        <option value="">所有疾病</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <a href="https://www.chinacdc.cn/jksj/jksj03/" target="_blank" class="btn btn-outline-info">
                        <i class="fas fa-external-link-alt"></i> 疾控中心风险评估报告
                    </a>
                </div>
            </div>
        </div>

        <div class="map-container" style="position: relative;">
            <div id="worldMap"></div>

            <!-- 地图控制按钮 -->
            <div class="map-controls">
                <button class="map-control-btn" id="zoomInBtn" title="放大">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="map-control-btn" id="zoomOutBtn" title="缩小">
                    <i class="fas fa-minus"></i>
                </button>
                <button class="map-control-btn" id="resetZoomBtn" title="重置视图">
                    <i class="fas fa-redo-alt"></i>
                </button>
            </div>

            <!-- 风险等级图例 -->
            <div class="legend">
                <h5>风险等级</h5>
                <div class="legend-item clickable-legend" data-risk-level="high" style="cursor: pointer;">
                    <div class="legend-color" style="background-color: #ff4444;"></div>
                    <span class="legend-text">高风险</span>
                </div>
                <div class="legend-item clickable-legend" data-risk-level="medium" style="cursor: pointer;">
                    <div class="legend-color" style="background-color: #ffaa00;"></div>
                    <span class="legend-text">中风险</span>
                </div>
                <div class="legend-item clickable-legend" data-risk-level="low" style="cursor: pointer;">
                    <div class="legend-color" style="background-color: #44ff44;"></div>
                    <span class="legend-text">低风险</span>
                </div>
                <div class="legend-item clickable-legend" data-risk-level="none" style="cursor: pointer;">
                    <div class="legend-color" style="background-color: #cccccc;"></div>
                    <span class="legend-text">无数据</span>
                </div>
                <div class="legend-item clickable-legend" data-risk-level="publisher" style="cursor: pointer;">
                    <div class="legend-color" style="background-color: #2E5BBA;"></div>
                    <span class="legend-text">报告发布方</span>
                </div>
            </div>

            <!-- 风险变化统计 -->
            <div class="risk-change-stats">
                <h5>风险变化统计</h5>
                <div id="riskChangeStats" style="font-size: 11px;">
                    <div class="stats-item" style="margin-bottom: 3px;">
                        <div class="stats-indicator" style="background-color: #ff4444; border-radius: 50%; width: 12px; height: 12px; display: inline-block; margin-right: 8px;"></div>
                        <span id="riskUpCount" class="stats-text">上升: 0</span>
                    </div>
                    <div class="stats-item" style="margin-bottom: 3px;">
                        <div class="stats-indicator" style="background-color: #44ff44; border-radius: 50%; width: 12px; height: 12px; display: inline-block; margin-right: 8px;"></div>
                        <span id="riskDownCount" class="stats-text">下降: 0</span>
                    </div>
                    <div class="stats-item" style="margin-bottom: 3px;">
                        <div class="stats-indicator" style="background-color: #999; border-radius: 50%; width: 12px; height: 12px; display: inline-block; margin-right: 8px;"></div>
                        <span id="riskStableCount" class="stats-text">稳定: 0</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本文件 -->
    <script src="https://unpkg.com/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://unpkg.com/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
    
    <script>
        let chart;
        let historyChart; // 历史趋势图表实例
        let currentRiskType = 'import';
        let currentDisease = '';
        let allData = [];
        let includeHistory = true; // 默认开启历史比较
        let hiddenRiskLevels = new Set(); // 跟踪被隐藏的风险等级
        let currentZoom = 1; // 当前缩放级别
        let currentCenter = [0, 0]; // 当前中心点
        
        $(document).ready(function() {
            initChart();
            loadData();
            bindEvents();

            // 监听窗口大小变化
            $(window).resize(function() {
                if (chart) {
                    chart.resize();
                }
            });

            // 定期清理tooltip图表实例，防止内存泄漏
            setInterval(cleanupTooltipCharts, 10000); // 每10秒清理一次
        });
        
        function initChart() {
            chart = echarts.init(document.getElementById('worldMap'));
            
            // 显示加载动画
            chart.showLoading('default', {
                text: '加载中...',
                color: '#667eea',
                textColor: '#000',
                maskColor: 'rgba(255, 255, 255, 0.8)',
                zlevel: 0
            });
        }

        function bindEvents() {
            // 风险类型切换
            $('.btn-group .btn').click(function() {
                $('.btn-group .btn').removeClass('active');
                $(this).addClass('active');
                currentRiskType = $(this).data('risk');
                // 风险类型切换时重新加载数据，因为后端需要重新计算风险值
                loadData();
            });

            // 疾病筛选
            $('#diseaseSelect').change(function() {
                currentDisease = $(this).val();
                loadData();
            });

            // 图例点击事件
            $('.clickable-legend').click(function() {
                const riskLevel = $(this).data('risk-level');

                if (hiddenRiskLevels.has(riskLevel)) {
                    // 显示该风险等级
                    hiddenRiskLevels.delete(riskLevel);
                    $(this).removeClass('disabled');
                } else {
                    // 隐藏该风险等级
                    hiddenRiskLevels.add(riskLevel);
                    $(this).addClass('disabled');
                }

                // 重新渲染地图
                updateMap();
            });

            // 地图控制按钮事件
            $('#zoomInBtn').click(function() {
                if (chart) {
                    currentZoom = Math.min(currentZoom * 1.2, 5); // 最大5倍缩放
                    chart.setOption({
                        series: [{
                            zoom: currentZoom,
                            center: currentCenter
                        }]
                    });
                }
            });

            $('#zoomOutBtn').click(function() {
                if (chart) {
                    currentZoom = Math.max(currentZoom * 0.8, 0.5); // 最小0.5倍缩放
                    chart.setOption({
                        series: [{
                            zoom: currentZoom,
                            center: currentCenter
                        }]
                    });
                }
            });

            $('#resetZoomBtn').click(function() {
                if (chart) {
                    currentZoom = 1;
                    currentCenter = [0, 0];
                    chart.setOption({
                        series: [{
                            zoom: 1,
                            center: [0, 0]
                        }]
                    });
                }
            });
        }

        function loadData() {
            const params = {
                risk_type: currentRiskType,
                include_history: 'true' // 默认开启历史比较
            };

            if (currentDisease) {
                params.disease = currentDisease;
            }
            
            $.ajax({
                url: '/api/global_risk_assessment',
                method: 'GET',
                data: params,
                success: function(response) {
                    allData = response.countries || [];
                    updateDiseaseSelect(response.diseases || []);
                    updateRiskChangeStats();
                    updateMap();
                },
                error: function(xhr, status, error) {
                    console.error('数据加载失败:', error);
                    chart.hideLoading();
                    chart.setOption({
                        title: {
                            text: '数据加载失败',
                            left: 'center',
                            top: 'middle',
                            textStyle: {
                                color: '#ff4444',
                                fontSize: 18
                            }
                        }
                    });
                }
            });
        }
        
        function updateDiseaseSelect(diseases) {
            const select = $('#diseaseSelect');
            const currentValue = select.val();
            
            select.empty();
            select.append('<option value="">所有疾病</option>');
            
            diseases.forEach(disease => {
                select.append(`<option value="${disease}">${disease}</option>`);
            });
            
            // 恢复之前的选择
            if (currentValue && diseases.includes(currentValue)) {
                select.val(currentValue);
            }
        }

        function updateRiskChangeStats() {
            if (!allData) {
                return;
            }

            let upCount = 0, downCount = 0, stableCount = 0;

            allData.forEach(country => {
                if (country.risk_trend) {
                    switch(country.risk_trend) {
                        case 'up':
                            upCount++;
                            break;
                        case 'down':
                            downCount++;
                            break;
                        case 'stable':
                            stableCount++;
                            break;
                    }
                }
            });

            $('#riskUpCount').text(`上升: ${upCount}`);
            $('#riskDownCount').text(`下降: ${downCount}`);
            $('#riskStableCount').text(`稳定: ${stableCount}`);
        }

        function updateMap() {
            if (!allData || allData.length === 0) {
                chart.hideLoading();
                chart.setOption({
                    title: {
                        text: '暂无数据',
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            color: '#999',
                            fontSize: 18
                        }
                    }
                });
                return;
            }

            // 加载世界地图数据
            $.get('/static/json/world.json', function(worldJson) {
                echarts.registerMap('world', worldJson);



                // 处理数据
                const mapData = processMapData();

                const option = {
                    title: {
                        text: currentRiskType === 'import' ? '境外输入风险等级' : '海外旅行风险等级',
                        left: 'center',
                        top: 20,
                        textStyle: {
                            color: '#333',
                            fontSize: 18,
                            fontWeight: 'bold'
                        }
                    },
                    tooltip: {
                        trigger: 'item',
                        enterable: true,  // 允许鼠标进入tooltip
                        hideDelay: 300,   // 延迟300ms隐藏
                        formatter: function(params) {
                            if (params.data && params.data.riskData) {
                                const data = params.data.riskData;
                                return createTooltipContent(data);
                            }
                            // 无数据时也使用相同的样式
                            return createNoDataTooltipContent(params.name);
                        },
                        backgroundColor: 'transparent',
                        borderColor: 'transparent',
                        textStyle: {
                            color: '#fff'
                        },
                        padding: 0,
                        position: function (point, params, dom, rect, size) {
                            // 智能定位，确保悬浮框不超出屏幕
                            const tooltipWidth = size.contentSize[0];
                            const tooltipHeight = size.contentSize[1];
                            const viewWidth = size.viewSize[0];
                            const viewHeight = size.viewSize[1];

                            let x = point[0] + 10;
                            let y = point[1] + 10;

                            // 右边界检查
                            if (x + tooltipWidth > viewWidth) {
                                x = point[0] - tooltipWidth - 10;
                            }

                            // 下边界检查
                            if (y + tooltipHeight > viewHeight) {
                                y = point[1] - tooltipHeight - 10;
                            }

                            // 确保不超出左上边界
                            x = Math.max(10, x);
                            y = Math.max(10, y);

                            return [x, y];
                        }
                    },
                    visualMap: {
                        type: 'piecewise',
                        pieces: [
                            {min: 4, max: 4, color: '#2E5BBA', label: '报告发布方'},
                            {min: 3, max: 3, color: '#ff4444', label: '高风险'},
                            {min: 2, max: 2, color: '#ffaa00', label: '中风险'},
                            {min: 1, max: 1, color: '#44ff44', label: '低风险'},
                            {min: 0, max: 0, color: '#cccccc', label: '无数据'}
                        ],
                        show: false
                    },
                    series: [{
                        id: 'worldMap',
                        name: '风险等级',
                        type: 'map',
                        map: 'world',
                        roam: true,
                        emphasis: {
                            label: {
                                show: true,
                                color: '#fff',
                                fontSize: 12,
                                fontWeight: 'bold',
                                textBorderColor: '#000',  // 黑色描边
                                textBorderWidth: 2,       // 描边宽度
                                textShadowColor: '#000',  // 阴影颜色
                                textShadowBlur: 3,        // 阴影模糊度
                                textShadowOffsetX: 1,     // 阴影X偏移
                                textShadowOffsetY: 1      // 阴影Y偏移
                            },
                            itemStyle: {
                                areaColor: '#389BB7',
                                borderWidth: 2
                            }
                        },
                        itemStyle: {
                            // 统一设置边界线，确保相邻国家能够区分
                            borderColor: '#333333', // 深灰色边界，在所有颜色上都清晰可见
                            borderWidth: 1.5, // 稍粗的边界线
                            borderType: 'solid',
                            // 风险变化的额外边框效果
                            shadowColor: function(params) {
                                if (params.data && params.data.riskData && params.data.riskData.riskTrend) {
                                    switch(params.data.riskData.riskTrend) {
                                        case 'up': return '#ff4444';    // 红色阴影表示风险上升
                                        case 'down': return '#44ff44';  // 绿色阴影表示风险下降
                                        case 'stable': return 'transparent'; // 稳定时无阴影
                                    }
                                }
                                return 'transparent';
                            },
                            shadowBlur: function(params) {
                                if (params.data && params.data.riskData && params.data.riskData.riskTrend && params.data.riskData.riskTrend !== 'stable') {
                                    return 8; // 风险变化时添加阴影效果
                                }
                                return 0;
                            },
                            shadowOffsetX: 0,
                            shadowOffsetY: 0
                        },
                        data: mapData.map(item => {
                            // 为中国特殊处理颜色，但只在"报告发布方"未被隐藏时
                            if (item.riskData && item.riskData.isPublisher && !hiddenRiskLevels.has('publisher')) {
                                return {
                                    ...item,
                                    itemStyle: {
                                        areaColor: '#2E5BBA', // 强制设置中国为蓝色
                                        borderColor: '#333333',
                                        borderWidth: 1.5,
                                        borderType: 'solid'
                                    }
                                };
                            }
                            return item;
                        })
                    }]
                };

                chart.hideLoading();
                chart.setOption(option, true);

                // 监听地图的缩放和平移事件
                chart.on('georoam', function(params) {
                    if (params.zoom != null) {
                        currentZoom = params.zoom;
                    }
                    if (params.center != null) {
                        currentCenter = params.center;
                    }
                });

            }).fail(function() {
                console.error('地图数据加载失败');
                chart.hideLoading();
                chart.setOption({
                    title: {
                        text: '地图数据加载失败',
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            color: '#ff4444',
                            fontSize: 18
                        }
                    }
                });
            });
        }

        function processMapData() {
            // 后端已经处理了数据分组和时间优先级，直接转换为地图格式
            const mapData = [];
            let chinaFound = false;

            allData.forEach(item => {
                const countryName = getStandardCountryName(item.name);

                // 检查是否是中国
                const isChina = isChineseCountry(item.name, countryName);
                if (isChina) {
                    chinaFound = true;
                }

                // 检查该风险等级是否被隐藏
                const riskLevelKey = isChina ? 'publisher' : getRiskLevelKey(item.risk_level);
                if (hiddenRiskLevels.has(riskLevelKey)) {
                    return; // 跳过被隐藏的风险等级（包括中国）
                }

                mapData.push({
                    name: countryName,
                    value: isChina ? 4 : item.risk_value, // 中国使用特殊值4
                    riskData: {
                        country: item.name,
                        diseases: item.diseases || [item.disease],
                        riskLevel: isChina ? '报告发布方' : item.risk_level,
                        totalCases: item.total_cases,
                        deaths: item.cumulative_deaths,
                        dataTime: item.data_time,
                        dataSource: item.data_source,
                        sourceUrl: item.source_url,
                        importRisk: item.import_risk_level,
                        travelRisk: item.travel_risk_level,
                        allDiseasesData: item.all_diseases_data || [],
                        // 历史对比数据
                        previousRisk: item.previous_risk,
                        riskTrend: item.risk_trend,
                        changeDate: item.change_date,
                        riskHistory: item.risk_history || [],
                        isPublisher: isChina
                    }
                });
            });

            // 处理中国的显示逻辑
            if (!chinaFound) {
                // 如果数据中没有中国，且"报告发布方"图例未被隐藏，则添加中国
                if (!hiddenRiskLevels.has('publisher')) {
                    mapData.push({
                        name: '中国',
                        value: 4,
                        riskData: {
                            country: '中华人民共和国',
                            diseases: [],
                            riskLevel: '报告发布方',
                            totalCases: null,
                            deaths: null,
                            dataTime: null,
                            dataSource: '中国疾病预防控制中心',
                            sourceUrl: 'https://www.chinacdc.cn/jksj/jksj03/',
                            importRisk: null,
                            travelRisk: null,
                            allDiseasesData: [],
                            previousRisk: null,
                            riskTrend: null,
                            changeDate: null,
                            riskHistory: [],
                            isPublisher: true
                        }
                    });
                }
            }

            return mapData;
        }

        function createTooltipContent(data) {
            let html = `<div class="tooltip-content">`;
            html += `<div class="tooltip-title">${data.country}</div>`;

            // 如果是中国（报告发布方），显示特殊内容
            if (data.isPublisher) {
                html += `<div class="tooltip-item"><strong>数据状态:</strong> 数据发布方</div>`;
                html += `<div class="tooltip-item" style="margin-top: 8px; line-height: 1.4; color: #ccc; font-size: 11px;">`;
                html += `<strong>说明:</strong> 作为全球传染病事件风险评估报告的发布机构，<br>`;
                html += `中国疾病预防控制中心负责收集、分析和发布<br>`;
                html += `全球各国的传染病风险信息。`;
                html += `</div>`;

                // 数据来源
                if (data.dataSource) {
                    if (data.sourceUrl) {
                        html += `<div class="tooltip-item" style="font-size: 10px; color: #ccc; margin-top: 8px;">数据来源: <a href="${data.sourceUrl}" target="_blank" style="color: #4a90e2; text-decoration: underline;">${data.dataSource}</a></div>`;
                    } else {
                        html += `<div class="tooltip-item" style="font-size: 10px; color: #ccc; margin-top: 8px;">数据来源: ${data.dataSource}</div>`;
                    }
                }

                html += `</div>`;
                return html;
            }

            // 显示当前选择的风险类型
            const currentRiskLabel = currentRiskType === 'import' ? '境外输入风险' : '海外旅行风险';
            html += `<div class="tooltip-item"><strong>${currentRiskLabel}:</strong> ${data.riskLevel || '未知'}</div>`;

            // 显示风险变化信息（如果启用了历史对比）
            if (includeHistory && data.previousRisk && data.riskTrend) {
                const trendIcon = getTrendIcon(data.riskTrend);
                const trendColor = getTrendColor(data.riskTrend);
                html += `<div class="tooltip-item" style="color: ${trendColor};">`;
                html += `<strong>风险变化:</strong> ${data.previousRisk} ${trendIcon} ${data.riskLevel}`;
                if (data.changeDate) {
                    html += ` (${data.changeDate})`;
                }
                html += `</div>`;
            }

            // 显示数据时间
            if (data.dataTime) {
                html += `<div class="tooltip-item"><strong>数据时间:</strong> ${data.dataTime}</div>`;
            }

            // 显示风险历史趋势（如果有历史数据）
            if (data.riskHistory && data.riskHistory.length > 1) {
                html += `<div class="tooltip-item"><strong>历史趋势 (最近6个月):</strong></div>`;

                // 创建一个唯一的图表容器ID
                const chartId = `history-chart-${Math.random().toString(36).substr(2, 9)}`;
                html += `<div id="${chartId}" style="width: 300px; height: 150px; margin: 10px 0;"></div>`;

                // 使用requestAnimationFrame确保DOM渲染完成后再创建图表
                requestAnimationFrame(() => {
                    requestAnimationFrame(() => {
                        // 双重requestAnimationFrame确保tooltip完全渲染
                        const element = document.getElementById(chartId);
                        if (element && element.offsetParent !== null) {
                            // 元素存在且可见，创建图表
                            createHistoryChart(chartId, data.riskHistory.slice(0, 6));
                        }
                    });
                });
            }

            // 显示所有疾病的详细信息
            if (data.allDiseasesData && data.allDiseasesData.length > 0) {
                html += `<div class="tooltip-item"><strong>详细数据:</strong></div>`;

                data.allDiseasesData.forEach((diseaseData, index) => {
                    html += `<div style="margin-left: 10px; margin-bottom: 8px; padding: 5px; background: rgba(255,255,255,0.1); border-radius: 3px;">`;
                    html += `<div style="font-weight: bold; color: #fff;">${diseaseData.disease || '未知疾病'}</div>`;

                    // 统计期间
                    if (diseaseData.start_date && diseaseData.end_date) {
                        html += `<div class="tooltip-item" style="font-size: 11px;">统计期间: ${diseaseData.start_date} - ${diseaseData.end_date}</div>`;
                    }

                    // 病例数据 - 显示所有字段，即使为空也显示
                    html += `<div class="tooltip-item" style="font-size: 11px;">累计病例: ${diseaseData.total_cases || '-'}</div>`;
                    html += `<div class="tooltip-item" style="font-size: 11px;">疑似病例: ${diseaseData.suspected_cases || '-'}</div>`;
                    html += `<div class="tooltip-item" style="font-size: 11px;">确诊病例: ${diseaseData.confirmed_cases || '-'}</div>`;
                    html += `<div class="tooltip-item" style="font-size: 11px;">累计死亡: ${diseaseData.cumulative_deaths || '-'}</div>`;

                    // 风险等级
                    html += `<div class="tooltip-item" style="font-size: 11px;">`;
                    html += `境外输入风险: <span style="color: ${getRiskColor(diseaseData.import_risk)}">${diseaseData.import_risk || '未知'}</span> | `;
                    html += `海外旅行风险: <span style="color: ${getRiskColor(diseaseData.travel_risk)}">${diseaseData.travel_risk || '未知'}</span>`;
                    html += `</div>`;

                    // 数据来源
                    if (diseaseData.data_source) {
                        if (diseaseData.source_url) {
                            html += `<div class="tooltip-item" style="font-size: 10px; color: #ccc;">数据来源: <a href="${diseaseData.source_url}" target="_blank" style="color: #4a90e2; text-decoration: underline;">${diseaseData.data_source}</a></div>`;
                        } else {
                            html += `<div class="tooltip-item" style="font-size: 10px; color: #ccc;">数据来源: ${diseaseData.data_source}</div>`;
                        }
                    }

                    html += `</div>`;
                });
            } else {
                // 如果没有详细数据，显示基本信息
                html += `<div class="tooltip-item">疾病: ${data.diseases.join(', ')}</div>`;

                if (data.totalCases && data.totalCases !== '-') {
                    html += `<div class="tooltip-item">累计病例: ${data.totalCases}</div>`;
                }

                if (data.deaths && data.deaths !== '-') {
                    html += `<div class="tooltip-item">累计死亡: ${data.deaths}</div>`;
                }

                // 显示双风险等级对比
                if (data.importRisk && data.travelRisk) {
                    html += `<div class="tooltip-item"><strong>风险对比:</strong></div>`;
                    html += `<div class="tooltip-item" style="margin-left: 10px;">境外输入: ${data.importRisk}</div>`;
                    html += `<div class="tooltip-item" style="margin-left: 10px;">海外旅行: ${data.travelRisk}</div>`;
                }

                // 数据来源
                if (data.dataSource) {
                    if (data.sourceUrl) {
                        html += `<div class="tooltip-item" style="font-size: 10px; color: #ccc;">数据来源: <a href="${data.sourceUrl}" target="_blank" style="color: #4a90e2; text-decoration: underline;">${data.dataSource}</a></div>`;
                    } else {
                        html += `<div class="tooltip-item" style="font-size: 10px; color: #ccc;">数据来源: ${data.dataSource}</div>`;
                    }
                }
            }

            html += `</div>`;
            return html;
        }

        function createNoDataTooltipContent(countryName) {
            let html = `<div class="tooltip-content">`;
            html += `<div class="tooltip-title">${countryName}</div>`;
            html += `<div class="tooltip-item"><strong>数据状态:</strong> 暂无数据</div>`;
            html += `<div class="tooltip-item" style="color: #ccc; font-size: 11px; margin-top: 8px;">`;
            html += `该国家/地区在当前数据源中暂无传染病风险评估信息。`;
            html += `</div>`;
            html += `</div>`;
            return html;
        }

        function getRiskColor(riskLevel) {
            if (!riskLevel) return '#999';

            const level = riskLevel.trim().toLowerCase();
            if (level === '高' || level === 'high') {
                return '#ff4444';
            } else if (level === '中' || level === 'medium' || level === 'moderate') {
                return '#ffaa00';
            } else if (level === '低' || level === 'low') {
                return '#44ff44';
            } else {
                return '#999';
            }
        }

        function getStandardCountryName(name) {
            // 后端已经处理了国家名称标准化，前端直接返回
            // 这里只处理一些可能的英文变体
            const nameMap = {
                'China': '中国',
                'People\'s Republic of China': '中国',
                'PRC': '中国',
                'CHN': '中国',
                'CN': '中国',
                '中国': '中国',
                '中华人民共和国': '中国',
                'United States': '美国',
                'USA': '美国',
                'Japan': '日本',
                'South Korea': '韩国',
                'United Kingdom': '英国',
                'UK': '英国',
                'France': '法国',
                'Germany': '德国',
                'Italy': '意大利',
                'Spain': '西班牙',
                'Russia': '俄罗斯',
                'India': '印度',
                'Brazil': '巴西',
                'Australia': '澳大利亚',
                'Canada': '加拿大',
                'Mexico': '墨西哥',
                'Argentina': '阿根廷',
                'South Africa': '南非',
                'Egypt': '埃及',
                'Turkey': '土耳其',
                'Iran': '伊朗',
                'Saudi Arabia': '沙特阿拉伯',
                'Israel': '以色列',
                'Thailand': '泰国',
                'Vietnam': '越南',
                'Indonesia': '印度尼西亚',
                'Malaysia': '马来西亚',
                'Singapore': '新加坡',
                'Philippines': '菲律宾',
                'New Zealand': '新西兰',
                'Fiji': '斐济',
                'Cook Islands': '库克群岛',
                'Mauritania': '毛里塔尼亚'
            };

            return nameMap[name] || name;
        }

        function getTrendIcon(trend) {
            switch(trend) {
                case 'up': return '↗';
                case 'down': return '↘';
                case 'stable': return '→';
                default: return '→';
            }
        }

        function getTrendColor(trend) {
            switch(trend) {
                case 'up': return '#ff4444';
                case 'down': return '#44ff44';
                case 'stable': return '#999';
                default: return '#999';
            }
        }

        function getRiskValueByLevel(riskLevel) {
            if (!riskLevel) return 0;

            const level = riskLevel.trim().toLowerCase();
            if (level === '高' || level === 'high') {
                return 3;
            } else if (level === '中' || level === 'medium' || level === 'moderate') {
                return 2;
            } else if (level === '低' || level === 'low') {
                return 1;
            } else {
                return 0;
            }
        }

        function getRiskLevelKey(riskLevel) {
            if (!riskLevel) return 'none';

            const level = riskLevel.trim().toLowerCase();
            if (level === '高' || level === 'high') {
                return 'high';
            } else if (level === '中' || level === 'medium' || level === 'moderate') {
                return 'medium';
            } else if (level === '低' || level === 'low') {
                return 'low';
            } else if (level === '报告发布方') {
                return 'publisher';
            } else {
                return 'none';
            }
        }

        function isChineseCountry(originalName, standardName) {
            if (!originalName && !standardName) return false;

            const chineseNames = [
                'china', 'people\'s republic of china', 'prc', 'cn',
                '中国', '中华人民共和国', '中国大陆', 'mainland china'
            ];

            const nameToCheck = (originalName || '').toLowerCase().trim();
            const standardToCheck = (standardName || '').toLowerCase().trim();

            return chineseNames.some(name =>
                nameToCheck === name ||
                standardToCheck === name ||
                nameToCheck.includes('china') ||
                standardToCheck.includes('china')
            );
        }

        // 存储tooltip中的图表实例，用于清理
        let tooltipCharts = new Map();

        function createHistoryChart(chartId, historyData) {
            // 创建小型历史趋势图表，用于在tooltip中显示
            try {
                const chartElement = document.getElementById(chartId);
                if (!chartElement) {
                    return; // 静默失败，避免控制台警告
                }

                // 检查元素是否可见
                if (chartElement.offsetWidth === 0 || chartElement.offsetHeight === 0) {
                    return;
                }

                // 如果已经存在同ID的图表，先销毁它
                if (tooltipCharts.has(chartId)) {
                    const existingChart = tooltipCharts.get(chartId);
                    existingChart.dispose();
                    tooltipCharts.delete(chartId);
                }

                const miniChart = echarts.init(chartElement);
                tooltipCharts.set(chartId, miniChart);

                // 准备数据
                const reversedData = historyData.slice(0, 6).reverse(); // 从早到晚排序
                const dates = reversedData.map(item => item.date);
                const riskValues = reversedData.map(item => item.risk_value || getRiskValueByLevel(item.risk));
                const riskLabels = reversedData.map(item => item.risk);

                const option = {
                    grid: {
                        left: '10%',
                        right: '5%',
                        top: '10%',
                        bottom: '25%'
                    },
                    xAxis: {
                        type: 'category',
                        data: dates,
                        axisLabel: {
                            color: '#fff',
                            fontSize: 8,
                            rotate: 45,
                            interval: 0,
                            margin: 5,
                            formatter: function(value) {
                                if (value && value.includes('-')) {
                                    const parts = value.split('-');
                                    if (parts.length >= 3) {
                                        return parts[1] + '-' + parts[2];
                                    }
                                }
                                return value;
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                        axisTick: {
                            alignWithLabel: true
                        }
                    },
                    yAxis: {
                        type: 'value',
                        min: 0,
                        max: 3,
                        interval: 1,
                        axisLabel: {
                            fontSize: 8,
                            formatter: function(value) {
                                const labels = {
                                    0: '',
                                    1: '低',
                                    2: '中',
                                    3: '高'
                                };
                                return labels[value] || '';
                            },
                            color: function(value) {
                                const colors = {
                                    1: '#44ff44',
                                    2: '#ffaa00',
                                    3: '#ff4444'
                                };
                                return colors[value] || '#fff';
                            }
                        },
                        axisLine: {
                            lineStyle: {
                                color: '#fff'
                            }
                        },
                        splitLine: {
                            lineStyle: {
                                color: 'rgba(255,255,255,0.2)'
                            }
                        }
                    },
                    series: [{
                        type: 'line',
                        data: riskValues.map((value, index) => ({
                            value: value,
                            itemStyle: {
                                color: getRiskColor(riskLabels[index])
                            }
                        })),
                        lineStyle: {
                            color: '#2E5BBA',
                            width: 2
                        },
                        symbol: 'circle',
                        symbolSize: 6,
                        emphasis: {
                            itemStyle: {
                                borderColor: '#fff',
                                borderWidth: 1
                            }
                        }
                    }],
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            const riskLabel = riskLabels[params.dataIndex];
                            const date = dates[params.dataIndex];
                            return `${date}<br/>风险等级: <span style="color: ${getRiskColor(riskLabel)}">${riskLabel}</span>`;
                        },
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        borderColor: 'rgba(255,255,255,0.2)',
                        textStyle: {
                            color: '#fff',
                            fontSize: 10
                        }
                    }
                };

                miniChart.setOption(option, true);

                // 设置一个定时器，在tooltip可能消失后清理图表
                setTimeout(() => {
                    if (tooltipCharts.has(chartId)) {
                        const element = document.getElementById(chartId);
                        if (!element || element.offsetParent === null) {
                            // 元素已经不可见，清理图表
                            const chart = tooltipCharts.get(chartId);
                            chart.dispose();
                            tooltipCharts.delete(chartId);
                        }
                    }
                }, 5000); // 5秒后检查并清理

            } catch (error) {
                // 静默处理错误，避免控制台输出
                if (tooltipCharts.has(chartId)) {
                    tooltipCharts.delete(chartId);
                }
            }
        }

        // 清理所有tooltip图表的函数
        function cleanupTooltipCharts() {
            tooltipCharts.forEach((chart, chartId) => {
                const element = document.getElementById(chartId);
                if (!element || element.offsetParent === null) {
                    chart.dispose();
                    tooltipCharts.delete(chartId);
                }
            });
        }

        function showHistoryChart(countryName, historyData) {
            // 注意：此函数需要相应的HTML容器元素才能正常工作
            // 当前版本中暂时不使用此函数，主要使用createHistoryChart在tooltip中显示小图表

            // 检查必要的DOM元素是否存在
            const containerElement = document.getElementById('historyChartContainer');
            const titleElement = document.getElementById('historyChartTitle');
            const chartElement = document.getElementById('historyChart');

            if (!containerElement || !titleElement || !chartElement) {
                console.warn('History chart container elements not found. This function requires historyChartContainer, historyChartTitle, and historyChart elements.');
                return;
            }

            // 显示图表容器
            $('#historyChartContainer').show();
            $('#historyChartTitle').text(`${countryName} - 历史趋势`);

            // 如果图表实例不存在，创建它
            if (!historyChart) {
                historyChart = echarts.init(chartElement);
            }

            // 准备数据
            const reversedData = historyData.slice(0, 6).reverse(); // 从早到晚排序
            const dates = reversedData.map(item => item.date);
            const riskValues = reversedData.map(item => item.risk_value || getRiskValueByLevel(item.risk));
            const riskLabels = reversedData.map(item => item.risk);

            const option = {
                grid: {
                    left: '15%',
                    right: '10%',
                    top: '15%',
                    bottom: '35%'
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    axisLabel: {
                        color: '#fff',
                        fontSize: 9,
                        rotate: 45,
                        interval: 0,
                        margin: 8,
                        formatter: function(value) {
                            if (value && value.includes('-')) {
                                const parts = value.split('-');
                                if (parts.length >= 3) {
                                    return parts[1] + '-' + parts[2];
                                }
                            }
                            return value;
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 3,
                    interval: 1,
                    axisLabel: {
                        fontSize: 10,
                        formatter: function(value) {
                            const labels = {
                                0: '',
                                1: '低风险',
                                2: '中风险',
                                3: '高风险'
                            };
                            return labels[value] || '';
                        },
                        color: function(value) {
                            const colors = {
                                1: '#44ff44',
                                2: '#ffaa00',
                                3: '#ff4444'
                            };
                            return colors[value] || '#fff';
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#fff'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,0.2)'
                        }
                    }
                },
                series: [{
                    type: 'line',
                    data: riskValues.map((value, index) => ({
                        value: value,
                        itemStyle: {
                            color: getRiskColor(riskLabels[index])
                        }
                    })),
                    lineStyle: {
                        color: '#2E5BBA',
                        width: 3
                    },
                    symbol: 'circle',
                    symbolSize: 8,
                    emphasis: {
                        itemStyle: {
                            borderColor: '#fff',
                            borderWidth: 2
                        }
                    }
                }],
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        const riskLabel = riskLabels[params.dataIndex];
                        const date = dates[params.dataIndex];
                        return `${date}<br/>风险等级: <span style="color: ${getRiskColor(riskLabel)}">${riskLabel}</span>`;
                    },
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    borderColor: 'rgba(255,255,255,0.2)',
                    textStyle: {
                        color: '#fff',
                        fontSize: 11
                    }
                }
            };

            historyChart.setOption(option, true);
        }


    </script>
</body>
</html>
