import requests
import io
import PyPDF2
import pdfplumber
from urllib.parse import urlparse
import tempfile
import os


def extract_pdf_text(pdf_url):
    """
    从PDF URL提取全部文字内容

    Args:
        pdf_url (str): PDF文件的URL

    Returns:
        str: 提取的文本内容，如果失败返回错误信息
    """
    try:
        # 验证URL格式
        parsed_url = urlparse(pdf_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return "错误：无效的PDF URL格式"

        # 下载PDF文件
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Accept": "application/pdf,application/octet-stream,*/*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Accept-Encoding": "gzip, deflate, br",
            "Connection": "keep-alive",
            "Upgrade-Insecure-Requests": "1",
        }

        print(f"正在下载PDF文件: {pdf_url}")
        try:
            response = requests.get(
                pdf_url, headers=headers, timeout=30, stream=True, verify=False
            )
            response.raise_for_status()
        except requests.exceptions.SSLError:
            print("SSL错误，尝试不验证SSL证书...")
            response = requests.get(
                pdf_url, headers=headers, timeout=30, stream=True, verify=False
            )
            response.raise_for_status()

        # 检查内容类型
        content_type = response.headers.get("content-type", "").lower()
        if "pdf" not in content_type and not pdf_url.lower().endswith(".pdf"):
            return "错误：URL指向的不是PDF文件"

        # 获取PDF内容
        pdf_content = response.content
        if len(pdf_content) == 0:
            return "错误：PDF文件为空"

        print(f"PDF文件下载成功，大小: {len(pdf_content)} bytes")

        # 尝试使用pdfplumber提取文本（通常效果更好）
        text = extract_with_pdfplumber(pdf_content)
        if text and text.strip():
            print("使用pdfplumber成功提取文本")
            return text.strip()

        # 如果pdfplumber失败，尝试使用PyPDF2
        text = extract_with_pypdf2(pdf_content)
        if text and text.strip():
            print("使用PyPDF2成功提取文本")
            return text.strip()

        return "错误：无法从PDF中提取文本内容，可能是扫描版PDF或加密PDF"

    except requests.exceptions.Timeout:
        return "错误：下载PDF文件超时"
    except requests.exceptions.RequestException as e:
        return f"错误：下载PDF文件失败 - {str(e)}"
    except Exception as e:
        return f"错误：处理PDF文件时发生异常 - {str(e)}"


def extract_with_pdfplumber(pdf_content):
    """
    使用pdfplumber提取PDF文本

    Args:
        pdf_content (bytes): PDF文件内容

    Returns:
        str: 提取的文本内容
    """
    try:
        with io.BytesIO(pdf_content) as pdf_file:
            with pdfplumber.open(pdf_file) as pdf:
                text_parts = []
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(f"--- 第{page_num}页 ---\n{page_text}\n")
                    except Exception as e:
                        print(f"提取第{page_num}页时出错: {str(e)}")
                        continue

                return "\n".join(text_parts)
    except Exception as e:
        print(f"pdfplumber提取失败: {str(e)}")
        return None


def extract_with_pypdf2(pdf_content):
    """
    使用PyPDF2提取PDF文本

    Args:
        pdf_content (bytes): PDF文件内容

    Returns:
        str: 提取的文本内容
    """
    try:
        with io.BytesIO(pdf_content) as pdf_file:
            pdf_reader = PyPDF2.PdfReader(pdf_file)
            text_parts = []

            for page_num, page in enumerate(pdf_reader.pages, 1):
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text_parts.append(f"--- 第{page_num}页 ---\n{page_text}\n")
                except Exception as e:
                    print(f"提取第{page_num}页时出错: {str(e)}")
                    continue

            return "\n".join(text_parts)
    except Exception as e:
        print(f"PyPDF2提取失败: {str(e)}")
        return None


def extract_with_temp_file(pdf_content):
    """
    使用临时文件方式提取PDF文本（备用方案）

    Args:
        pdf_content (bytes): PDF文件内容

    Returns:
        str: 提取的文本内容
    """
    try:
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            temp_file.write(pdf_content)
            temp_file_path = temp_file.name

        try:
            # 使用pdfplumber读取临时文件
            with pdfplumber.open(temp_file_path) as pdf:
                text_parts = []
                for page_num, page in enumerate(pdf.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text_parts.append(f"--- 第{page_num}页 ---\n{page_text}\n")
                    except Exception as e:
                        print(f"提取第{page_num}页时出错: {str(e)}")
                        continue

                return "\n".join(text_parts)
        finally:
            # 清理临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        print(f"临时文件方式提取失败: {str(e)}")
        return None


def validate_pdf_url(url):
    """
    验证PDF URL的有效性

    Args:
        url (str): 要验证的URL

    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return False, "无效的URL格式"

        if parsed_url.scheme not in ["http", "https"]:
            return False, "URL必须使用http或https协议"

        return True, ""
    except Exception as e:
        return False, f"URL验证失败: {str(e)}"


# 测试函数
def test_pdf_extraction(test_url):
    """
    测试PDF文本提取功能

    Args:
        test_url (str): 测试用的PDF URL
    """
    print(f"测试PDF提取功能，URL: {test_url}")

    # 验证URL
    is_valid, error_msg = validate_pdf_url(test_url)
    if not is_valid:
        print(f"URL验证失败: {error_msg}")
        return

    # 提取文本
    result = extract_pdf_text(test_url)
    print(f"提取结果长度: {len(result)} 字符")
    print(f"前500字符预览:\n{result[:500]}...")


if __name__ == "__main__":
    # 可以在这里添加测试代码
    pass
