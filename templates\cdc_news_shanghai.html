<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>上海市卫健委上海市法定传染病疫情概况</title>
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
    <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow: hidden;
        }

        .main-container {
            width: 90%;
            height: 90%;
            margin: 2% auto;
            display: flex;
            flex-direction: column;
        }

        .header-container {
            margin-bottom: 20px;
        }

        .grid-container {
            flex: 1;
            width: 100%;
            min-height: 500px;
        }

        /* 修改模态窗口样式 */
        .ui.fullscreen.modal {
            display: flex !important;
            flex-direction: column;
            height: 90vh !important;
            margin: 5vh auto !important;
        }

        .ui.fullscreen.modal>.content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 20px;
            overflow: auto;
        }

        .ui.fullscreen.modal .description {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        #diseaseDetailsGrid {
            height: 700px !important;
            width: 100% !important;
        }

        #myGrid {
            height: 100% !important;
            width: 100% !important;
        }

        /* Toast 消息样式 */
        #toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast-message {
            background-color: #fff;
            color: #000;
            padding: 15px 25px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            display: none;
        }

        .toast-message.success {
            border-left: 4px solid #21ba45;
        }

        .toast-message.error {
            border-left: 4px solid #db2828;
        }

        .toast-message.info {
            border-left: 4px solid #2185d0;
        }
    </style>
</head>

<body>
    <!-- Toast 消息容器 -->
    <div id="toast-container"></div>

    <div class="main-container">
        <div class="header-container">
            <h2 class="ui header">
                <i class="hospital icon"></i>
                <div class="content">
                    上海市卫健委上海市法定传染病疫情概况
                    <div class="sub header">数据实时获取</div>
                </div>
            </h2>
            <div class="ui input" style="margin-right: 10px;">
                <input type="number" id="pageNumberInput" placeholder="页码" value="1" min="1" style="width: 100px;">
            </div>
            <button class="ui secondary button" id="loadPageButton" style="margin-right: 10px;">
                <i class="sync alternate icon"></i>
                加载指定页码
            </button>
            <button class="ui primary button" id="manualUrlButton">
                <i class="linkify icon"></i>
                手动输入URL
            </button>
        </div>

        <div class="grid-container">
            <div id="myGrid" style="width: 100%; height: 100%;" class="ag-theme-quartz"></div>
        </div>
    </div>

    <!-- 详细信息模态窗口 -->
    <div class="ui fullscreen modal" id="detailModal">
        <i class="close icon"></i>
        <div class="header">
            详细信息
        </div>
        <div class="content">
            <div class="description">
                <div class="ui header" id="modalTitle"></div>
                <p id="modalSummary"></p>
                <div class="ui divider"></div>
                <div class="ui list">
                    <div class="item">
                        <i class="calendar icon"></i>
                        <div class="content" id="modalDate"></div>
                    </div>
                    <div class="item">
                        <i class="linkify icon"></i>
                        <div class="content">
                            <a id="modalUrl" target="_blank">查看原文</a>
                        </div>
                    </div>
                </div>
                <div class="ui divider"></div>
                <div style="display: flex; justify-content: flex-end; margin-bottom: 10px;">
                    <button class="ui primary button" id="importToDbButton">
                        <i class="database icon"></i>
                        导入数据库
                    </button>
                </div>
                <div id="diseaseDetailsGrid" class="ag-theme-quartz" style="width: 100%; height: 600px;"></div>
            </div>
        </div>
        <div class="actions">
            <button class="ui red labeled icon button" id="closeModalButton">
                <i class="close icon"></i>
                关闭
            </button>
        </div>
    </div>

    <!-- URL输入模态窗口 -->
    <div class="ui tiny modal" id="urlInputModal">
        <i class="close icon"></i>
        <div class="header">
            输入URL
        </div>
        <div class="content">
            <div class="ui form">
                <div class="field">
                    <label>URL地址</label>
                    <input type="url" id="manualUrlInput" placeholder="请输入完整的URL地址">
                </div>
                <div class="field">
                    <label>发布日期</label>
                    <input type="date" id="manualDateInput">
                </div>
            </div>
        </div>
        <div class="actions">
            <button class="ui black deny button">
                取消
            </button>
            <button class="ui positive button" id="confirmUrlButton">
                确定
            </button>
        </div>
    </div>

    <script>
        // Toast 消息函数
        function showToast(message, type = 'info', duration = 3000) {
            const container = document.getElementById('toast-container');
            const toast = document.createElement('div');
            toast.className = `toast-message ${type}`;
            toast.textContent = message;

            container.appendChild(toast);

            // 显示消息
            $(toast).fadeIn();

            // 设置定时器，自动隐藏消息
            setTimeout(() => {
                $(toast).fadeOut(() => {
                    container.removeChild(toast);
                });
            }, duration);
        }

        // 替换所有 $('body').toast() 调用
        function showMessage(message, type = 'info') {
            showToast(message, type);
        }

        // AG Grid配置
        const gridOptions = {
            pagination: true,
            paginationPageSize: 20,
            paginationPageSizeSelector: [10, 20, 50],
            localeText: AG_GRID_LOCALE_ZH,

            columnDefs: [
                {
                    headerName: "序号",
                    valueGetter: "node.rowIndex + 1",
                    width: 100,
                    filter: false,
                    suppressSizeToFit: true,
                    cellStyle: {
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center'
                    }
                },
                {
                    headerName: "标题",
                    field: "title",
                    flex: 1,
                    minWidth: 200,
                    autoHeight: true,
                    cellStyle: {
                        'text-align': 'left',
                        'white-space': 'normal',
                        'line-height': '20px',
                        'display': 'flex',
                        'align-items': 'center',
                        'padding': '10px'
                    }
                },
                {
                    headerName: "发布日期",
                    field: "date",
                    width: 150,
                    cellStyle: {
                        'text-align': 'center',
                        'display': 'flex',
                        'align-items': 'center',
                        'justify-content': 'center'
                    }
                },
                {
                    headerName: "原网址",
                    field: "url",
                    width: 120,
                    cellRenderer: function (params) {
                        return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><a href="' + params.value + '" target="_blank" class="ui basic blue button mini" style="margin: 0; padding: 5px 8px;">查看</a></div>';
                    },
                    cellStyle: { 'padding': '5px' }
                },
                {
                    headerName: "操作",
                    width: 80,
                    cellRenderer: function (params) {
                        return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><button class="ui primary basic button mini viewDetails" style="margin: 0; padding: 5px 8px;">详情</button></div>';
                    },
                    onCellClicked: function (params) {
                        if (params.event.target.classList.contains('viewDetails')) {
                            showDetails(params.data);
                        }
                    },
                    cellStyle: { 'padding': '5px' }
                }
            ],
            defaultColDef: {
                sortable: true,
                filter: true,
                resizable: true
            },
            rowHeight: 60,
            suppressHorizontalScroll: true  // 禁用水平滚动
        };

        let mainGrid;
        let detailsGrid;

        // Function to load news data
        function loadNewsData(apiUrl) {
            showMessage('正在加载数据，请稍候...', 'info');
            if (mainGrid) {
                mainGrid.setGridOption('rowData', []); // Clear existing data
            }

            fetch(apiUrl)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (!data || !Array.isArray(data) || data.length === 0) {
                        showMessage('没有获取到数据，请检查页码或稍后再试', 'error');
                        if (mainGrid) {
                            mainGrid.setGridOption('rowData', []);
                        }
                    } else {
                        showMessage(`成功加载 ${data.length} 条数据`, 'success');
                        if (mainGrid) {
                            mainGrid.setGridOption('rowData', data);
                        }
                    }
                    setTimeout(() => {
                        if (mainGrid && typeof mainGrid.sizeColumnsToFit === 'function') {
                            mainGrid.sizeColumnsToFit();
                        }
                    }, 200);
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('获取数据失败：' + error.message, 'error');
                    if (mainGrid) {
                        mainGrid.setGridOption('rowData', []);
                    }
                });
        }

        // 初始化表格
        document.addEventListener('DOMContentLoaded', function () {
            const gridDiv = document.querySelector('#myGrid');
            mainGrid = agGrid.createGrid(gridDiv, gridOptions);

            // Load initial data (page 1)
            loadNewsData('/api/cdc_news_shanghai');

            // Event listener for the new page load button
            $('#loadPageButton').on('click', function () {
                const pageNumberInput = $('#pageNumberInput');
                let pageNumber = parseInt(pageNumberInput.val(), 10);

                if (isNaN(pageNumber) || pageNumber < 1) {
                    showMessage('请输入有效的页码 (大于0的整数)', 'error');
                    pageNumberInput.val(1); // Reset to 1
                    // Optionally, load page 1 again or do nothing
                    // loadNewsData('/api/cdc_news_shanghai'); 
                    return;
                }

                let apiUrl = '/api/cdc_news_shanghai';
                if (pageNumber > 1) {
                    apiUrl += `?page=${pageNumber}`;
                }
                loadNewsData(apiUrl);
            });

            // 手动URL输入按钮点击事件
            $('#manualUrlButton').on('click', function () {
                // 设置默认日期为今天
                const today = new Date().toISOString().split('T')[0];
                $('#manualDateInput').val(today);

                $('#urlInputModal')
                    .modal({
                        closable: false,
                        onApprove: function () {
                            return false; // 防止模态窗口自动关闭
                        }
                    })
                    .modal('show');
            });

            // 确认URL按钮点击事件
            $('#confirmUrlButton').on('click', function () {
                const url = $('#manualUrlInput').val();
                const date = $('#manualDateInput').val();

                if (!url) {
                    showMessage('请输入URL地址', 'error');
                    return;
                }
                if (!date) {
                    showMessage('请输入发布日期', 'error');
                    return;
                }

                // 关闭URL输入模态窗口
                $('#urlInputModal').modal('hide');

                // 创建一个模拟的数据对象
                const mockData = {
                    url: url,
                    date: date,
                    title: '手动输入的URL',
                    summary: ''
                };

                // 显示详情模态窗口
                showDetails(mockData);

                // 清空输入框
                $('#manualUrlInput').val('');
            });
        });

        // 显示详细信息
        function showDetails(data) {
            $('#modalTitle').text(data.title);
            $('#modalDate').text(data.date);
            $('#modalSummary').text(data.summary);
            $('#modalUrl').attr('href', data.url);

            const diseaseGridOptions = {
                columnDefs: [
                    { headerName: "发布时间", field: "date", width: 100 },
                    { headerName: "疾病名称", field: "diseaseName", width: 150 },
                    { headerName: "子疾病名称", field: "subDiseaseName", width: 150 },
                    { headerName: "地点", field: "location", width: 80 },
                    { headerName: "源网址", field: "sourceUrl", width: 120 },
                    { headerName: "本市户籍感染数", field: "local_hukou", width: 130 },
                    { headerName: "非本市户籍感染", field: "nonlocal_hukou", width: 130 },
                    { headerName: "合计感染数", field: "total", width: 100 }
                ],
                defaultColDef: {
                    sortable: true,
                    filter: true,
                    resizable: true
                },
                localeText: AG_GRID_LOCALE_ZH,
                pagination: true,
                paginationPageSize: 50,
                domLayout: 'normal'
            };

            let currentDiseaseData = [];  // 存储当前表格数据

            // 显示模态窗口
            $('#detailModal')
                .modal({
                    onShow: function () {
                        // 在模态窗口显示后初始化表格
                        setTimeout(() => {
                            const gridDiv = document.querySelector('#diseaseDetailsGrid');
                            if (detailsGrid) {
                                gridDiv.innerHTML = '';
                            }
                            detailsGrid = agGrid.createGrid(gridDiv, diseaseGridOptions);

                            // 显示加载中消息
                            showMessage('正在解析疾病数据，请稍候...', 'info');

                            // 从后端获取解析好的数据
                            fetch(`/api/parse_disease_data_shanghai?url=${encodeURIComponent(data.url)}&date=${encodeURIComponent(data.date)}`)
                                .then(response => {
                                    if (!response.ok) {
                                        throw new Error(`HTTP error! Status: ${response.status}`);
                                    }
                                    return response.json();
                                })
                                .then(parsedData => {
                                    if (!parsedData || !Array.isArray(parsedData) || parsedData.length === 0) {
                                        if (parsedData && parsedData.error) {
                                            showMessage('获取数据失败：' + parsedData.error, 'error');
                                        } else {
                                            showMessage('未找到疾病数据，请检查URL是否正确', 'error');
                                        }
                                        // 设置空数组防止AG Grid出错
                                        detailsGrid.setGridOption('rowData', []);
                                    } else {
                                        currentDiseaseData = parsedData;
                                        showMessage(`成功解析 ${parsedData.length} 条疾病数据`, 'success');
                                        detailsGrid.setGridOption('rowData', currentDiseaseData);
                                    }
                                    setTimeout(() => {
                                        detailsGrid.sizeColumnsToFit();
                                    }, 200);
                                })
                                .catch(error => {
                                    console.error('Error:', error);
                                    showMessage('解析疾病数据失败：' + error.message, 'error');
                                    // 设置空数组防止AG Grid出错
                                    detailsGrid.setGridOption('rowData', []);
                                });
                        }, 100);
                    },
                    observeChanges: true
                })
                .modal('show');

            // 导入数据库按钮点击事件
            $('#importToDbButton').off('click').on('click', function () {
                if (currentDiseaseData && currentDiseaseData.length > 0) {
                    importData(currentDiseaseData, false);
                }
            });

            // 导入数据函数
            function importData(data, force = false) {
                const url = force ?
                    '/api/import_disease_data_shanghai?force=true' :
                    '/api/import_disease_data_shanghai';

                fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                    .then(response => response.json())
                    .then(result => {
                        if (!result.success && result.message === 'exists') {
                            // 显示确认对话框
                            const confirmModal = $('<div class="ui basic modal">')
                                .append($('<div class="header">').text('数据已存在'))
                                .append(
                                    $('<div class="content">').append(
                                        $('<p>').text(`数据库中已存在 ${result.date} 的数据，是否覆盖？`)
                                    )
                                )
                                .append(
                                    $('<div class="actions">').append(
                                        $('<div class="ui red basic cancel inverted button">').append(
                                            $('<i class="remove icon">'),
                                            '否'
                                        ),
                                        $('<div class="ui green ok inverted button">').append(
                                            $('<i class="checkmark icon">'),
                                            '是'
                                        )
                                    )
                                );

                            confirmModal
                                .modal({
                                    closable: false,
                                    onDeny: function () {
                                        showMessage('已取消导入操作', 'info');
                                    },
                                    onApprove: function () {
                                        importData(data, true);
                                    }
                                })
                                .modal('show');
                        } else {
                            // 显示导入结果提示
                            const message = result.success ?
                                `成功导入 ${result.imported_count} 条数据` :
                                '导入失败：' + result.message;

                            showMessage(message, result.success ? 'success' : 'error');
                        }
                    })
                    .catch(error => {
                        console.error('Error importing data:', error);
                        showMessage('导入失败：' + error.message, 'error');
                    });
            }
        }

        // 关闭模态窗口
        $('#closeModalButton').on('click', function () {
            $('#detailModal').modal('hide');
        });

        // 处理窗口大小改变
        window.addEventListener('resize', function () {
            if (mainGrid) {
                mainGrid.sizeColumnsToFit();
            }
        });
    </script>
</body>

</html>