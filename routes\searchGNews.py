from gnews import GNews
from flask import jsonify
from datetime import datetime

def format_date(date_str):
    try:
        # 尝试将字符串按GMT格式解析为datetime对象
        date_obj = datetime.strptime(date_str, '%a, %d %b %Y %H:%M:%S %Z')
        # 转换日期格式为 YYYY/MM/DD HH:MM:SS
        return date_obj.strftime('%Y/%m/%d %H:%M:%S')
    except ValueError:
        # 如果日期格式不正确，返回'Unknown Date'
        return 'Unknown Date'

def search_google_news(keyword, start_date, end_date):
    # 实例化GNews对象
    google_news = GNews(language='zh-Hans', country='CN')

    # 将字符串格式的日期转换为元组
    start_date_tuple = tuple(map(int, start_date.split('-')))
    end_date_tuple = tuple(map(int, end_date.split('-')))

    # 设置搜索日期范围
    google_news.start_date = start_date_tuple
    google_news.end_date = end_date_tuple
    google_news.exclude_websites = ['yahoo.com'] 
    
    # 开始搜索
    all_results = google_news.get_news(keyword)

    # 检查是否有有效结果，并且对结果进行格式化
    if all_results:
        formatted_data = {
            'data': [{
                'checkbox': '',  
                'title': news.get('title', ''),
                'publishedAt': format_date(news.get('published date', 'Unknown Date')),
                'description': news.get('description', 'Unknown'),
                'source': news.get('publisher', {}).get('title', 'Unknown'),
                'url': news.get('url', '')
            } for news in all_results]  
        }
    else:
        formatted_data = {'data': []}

    return jsonify(formatted_data)
