/* Custom CSS for filters and search */
.column-filter,
#customSearchBox,
#searchButton {
    height: 38px;
    /* Adjust the height to match the filter boxes */
    padding: 6px 12px;
    /* Provide sufficient padding */
    margin: 0;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100%;

}

.filter-wrapper {
    display: flex;
    flex-wrap: nowrap;
    /* 防止子元素折行 */
    align-items: center;
    /* 垂直居中对齐 */
    justify-content: flex-start;
    /* 水平开始位置对齐 */
    /* 根据需要调整下面的宽度和间距 */
    max-width: 100%;
    gap: 10px;
    /* 子元素之间的间距 */
}

.filter-label {
    display: inline-block;
    margin-right: 5px;
    line-height: 38px;
    vertical-align: top;
}

.filter-box,
#searchButton {
    height: 38px;
    padding: 6px 12px;
    margin-right: 5px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    vertical-align: top;
}

.filter-button,
#lableButton {
    height: 30px;
    padding: 6px 12px;
    margin-right: 5px;
    box-sizing: border-box;
    border: 1px solid #ccc;
    border-radius: 4px;
    vertical-align: center;

}

#customSearchBox {
    width: calc(30%);
    /* Adjust the width */
}

#startDate,
#endDate,
#dateRange {
    width: 250px;
}

#dateRange {
    text-align: center;
}

#searchButton {
    width: 80px;
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    cursor: pointer;
}

#searchButton1:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

#generateButton {
    background-color: #28a745;
    /* Bootstrap 'success' green color */
    border-color: #28a745;
    color: white;
    cursor: pointer;
}

#generateConfig {
    cursor: pointer;
    background-color: #e67e22;
    /* 橙色 */
    color: white;
    /* 文字颜色设为白色以提高对比度 */
    border: 1px solid #e67e22;
    /* 边框颜色与背景色一致 */
}

#generateConfig:hover {
    background-color: #d35400;
    /* 悬停时的背景色为稍微深一些的橙色 */
    border-color: #c0392b;
    /* 悬停时边框颜色也相应变得更深 */
}


#generateButton:hover {
    background-color: #218838;
    /* A slightly darker green for hover state */
    border-color: #1e7e34;
}

th {
    position: sticky;
    top: 0;
    background-color: white;
    z-index: 10;
}

.dataTables_wrapper .dataTables_filter {
    display: none;
    /* Hide default search box */
}

.seq-column {
    width: 50px;
    text-align: center;
}

.select-column {
    width: 25px;
    text-align: center;
}

.title-column {
    width: 30%;
}

.published-column {
    width: 10%;
    text-align: center;
}

.summary-column {
    width: 40%;
}

.source-column {
    width: 10%;
}

.url-column {
    width: 10%;
    text-align: center;
}

.ui-datepicker {
    z-index: 1000 !important;
    /* Ensure it's on top of other elements */
}

/* 进度条模态窗口样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 50%;
}

.progress {
    position: relative;
    height: 4px;
    display: block;
    width: 100%;
    background-color: #ace;
    border-radius: 2px;
    margin: 0.5rem 0 1rem 0;
    overflow: hidden;
}

.indeterminate {
    background-color: #2a2;
}

.indeterminate:before {
    content: '';
    position: absolute;
    background-color: inherit;
    top: 0;
    left: 50%;
    bottom: 0;
    will-change: left, right;
    animation: indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
}


@keyframes indeterminate {
    0% {
        left: -35%;
        right: 100%;
    }

    60% {
        left: 100%;
        right: -90%;
    }

    100% {
        left: 100%;
        right: -90%;
    }
}

#navbar {
    background-color: #f7f7f7;
    border-bottom: 1px solid #ddd;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 8px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    /* 在这里设置导航栏下方的间距 */
}

#branding {
    display: flex;
    align-items: center;
    font-size: 16px;
    /* 根据需要调整字体大小 */
    font-weight: bold;
    color: #333;
}

#user-info {
    display: flex;
    align-items: center;
}

#username {
    font-weight: bold;
    color: #555;
    font-size: 14px;
}

#logoutButton {
    background-color: #d9534f;
    color: white;
    border: none;
    padding: 4px 10px;
    font-size: 14px;
    line-height: 1.2;
    cursor: pointer;
    border-radius: 3px;
}

#logoutButton:hover {
    background-color: #c9302c;
}

/* 自定义样式以确保标签、输入框和按钮之间没有间隙 */
.ui.labeled.input>.label,
.ui.labeled.input>.ui.button {
    border-radius: 0 !important;
    /* 移除圆角的默认设置 */
}

/* 针对第一个元素（标签）的左侧圆角 */
.ui.labeled.input>.label {
    border-top-left-radius: 3px !important;
    border-bottom-left-radius: 3px !important;
    border-right: none !important;
    /* 移除标签和输入框之间的边界线 */
}

/* 针对最后一个元素（按钮）的右侧圆角 */
.ui.labeled.input>.ui.button {
    border-top-right-radius: 3px !important;
    border-bottom-right-radius: 3px !important;
}

/* 定义了整体的宽度 */
.custom-labeled-input {
    width: 100%;
}

/* 输入框的样式调整 */
.ui.labeled.input>input {
    border-radius: 0 !important;
    /* 移除圆角的默认设置 */
}

/* 修正因为移除边界线造成的输入框边框问题 */
.ui.labeled.input>input {
    border-left: none !important;
}

.ui.labeled.input>input:focus {
    border-color: #85b7d9 !important;
    /* 根据需要更改颜色 */
    /* 如果想要有阴影效果可以加上 */
    box-shadow: 0 0 8px -2px #85b7d9 !important;
}

.ag-header-cell-label {
    justify-content: center !important;
    font-weight: bold !important;
    font-size: 15px !important;
}

.cell-text-left {
    display: flex;
    justify-content: left;
    align-items: center;
    /* 默认就是左对齐，但这里写出来以便清晰表示 */
}

.cell-text-left-bold {
    display: flex;
    justify-content: left;
    align-items: center;
    font-weight: bold !important;
    /* 默认就是左对齐，但这里写出来以便清晰表示 */
}



.cell-text-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 设置偶数行的背景颜色 */
.ag-row-even {
    background-color: #f9f9f9;
    /* 浅灰色 */
}

/* 设置奇数行的背景颜色 */
.ag-row-odd {
    background-color: #ffffff;
    /* 白色 */
}

.ag-theme-quartz .ag-cell-focus {
    border: none !important;
}

/* 开关样式开始*/
.checkbox-wrapper-10 .tgl {
    display: none;
}

.checkbox-wrapper-10 .tgl,
.checkbox-wrapper-10 .tgl:after,
.checkbox-wrapper-10 .tgl:before,
.checkbox-wrapper-10 .tgl *,
.checkbox-wrapper-10 .tgl *:after,
.checkbox-wrapper-10 .tgl *:before,
.checkbox-wrapper-10 .tgl+.tgl-btn {
    box-sizing: border-box;
}

.checkbox-wrapper-10 .tgl::-moz-selection,
.checkbox-wrapper-10 .tgl:after::-moz-selection,
.checkbox-wrapper-10 .tgl:before::-moz-selection,
.checkbox-wrapper-10 .tgl *::-moz-selection,
.checkbox-wrapper-10 .tgl *:after::-moz-selection,
.checkbox-wrapper-10 .tgl *:before::-moz-selection,
.checkbox-wrapper-10 .tgl+.tgl-btn::-moz-selection,
.checkbox-wrapper-10 .tgl::selection,
.checkbox-wrapper-10 .tgl:after::selection,
.checkbox-wrapper-10 .tgl:before::selection,
.checkbox-wrapper-10 .tgl *::selection,
.checkbox-wrapper-10 .tgl *:after::selection,
.checkbox-wrapper-10 .tgl *:before::selection,
.checkbox-wrapper-10 .tgl+.tgl-btn::selection {
    background: none;
}

.checkbox-wrapper-10 .tgl+.tgl-btn {
    outline: 0;
    display: block;
    width: 100px;
    height: 38px;
    position: relative;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.checkbox-wrapper-10 .tgl+.tgl-btn:after,
.checkbox-wrapper-10 .tgl+.tgl-btn:before {
    position: relative;
    display: block;
    content: "";
    width: 50%;
    height: 100%;
}

.checkbox-wrapper-10 .tgl+.tgl-btn:after {
    left: 0;
}

.checkbox-wrapper-10 .tgl+.tgl-btn:before {
    display: none;
}

.checkbox-wrapper-10 .tgl:checked+.tgl-btn:after {
    left: 50%;
}

.checkbox-wrapper-10 .tgl-flip+.tgl-btn {
    display: flex;
    /* 使用Flexbox布局 */
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中，如果需要的话 */
    padding: 2px;
    transition: all 0.2s ease;
    font-family: sans-serif;
    perspective: 100px;
}

.checkbox-wrapper-10 .tgl-flip+.tgl-btn:after,
.checkbox-wrapper-10 .tgl-flip+.tgl-btn:before {
    display: flex;
    /* 使用Flexbox布局 */
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
    transition: all 0.4s ease;
    width: 100%;
    text-align: center;
    position: absolute;
    line-height: 2em;
    font-weight: bold;
    color: #fff;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 4px;
}

.checkbox-wrapper-10 .tgl-flip+.tgl-btn:after {
    content: attr(data-tg-on);
    background: #0278c6;
    transform: rotateY(-180deg);
}

.checkbox-wrapper-10 .tgl-flip+.tgl-btn:before {
    background: #389c02;
    content: attr(data-tg-off);
}

.checkbox-wrapper-10 .tgl-flip+.tgl-btn:active:before {
    transform: rotateY(-20deg);
}

.checkbox-wrapper-10 .tgl-flip:checked+.tgl-btn:before {
    transform: rotateY(180deg);
}

.checkbox-wrapper-10 .tgl-flip:checked+.tgl-btn:after {
    transform: rotateY(0);
    left: 0;
    background: #0278c6;
}

.checkbox-wrapper-10 .tgl-flip:checked+.tgl-btn:active:after {
    transform: rotateY(20deg);
}

/* 开关样式结束*/