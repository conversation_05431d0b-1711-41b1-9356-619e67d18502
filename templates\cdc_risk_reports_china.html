<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <title>重点传染病与突发公卫事件风险评估报告</title>
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-grid.min.css') }}">
  <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/ag-theme-quartz.min.css') }}">
  <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
  <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-community.min.noStyle.js') }}"></script>
  <script src="{{ url_for('static', filename='js/ag-grid-locale.zh.js') }}"></script>
  <style>
    html, body {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden;
    }
    .main-container {
      width: 90%;
      height: 90%;
      margin: 2% auto;
      display: flex;
      flex-direction: column;
    }
    .header-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-bottom: 20px;
    }
    .grid-container {
      flex: 1;
      width: 100%;
      min-height: 500px;
    }
    #myGrid {
      height: 100% !important;
      width: 100% !important;
    }

    /* AI分析遮罩样式 */
    .ai-analysis-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.9) 100%);
      z-index: 9999;
      display: none;
      justify-content: center;
      align-items: center;
      backdrop-filter: blur(5px);
    }

    .ai-analysis-content {
      background: white;
      padding: 40px;
      border-radius: 15px;
      text-align: center;
      box-shadow: 0 20px 60px rgba(0,0,0,0.3);
      max-width: 500px;
      width: 90%;
      animation: fadeInScale 0.5s ease-out;
    }

    @keyframes fadeInScale {
      from {
        opacity: 0;
        transform: scale(0.8);
      }
      to {
        opacity: 1;
        transform: scale(1);
      }
    }

    .ai-analysis-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #2185d0;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin: 0 auto 20px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .ai-analysis-text {
      font-size: 18px;
      color: #333;
      margin-bottom: 10px;
      font-weight: 500;
    }

    .ai-analysis-warning {
      font-size: 14px;
      color: #e74c3c;
      margin-top: 15px;
      font-weight: 400;
    }

    /* 分析结果模态框样式 */
    .analysis-result-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.8);
      z-index: 10000;
      display: none;
      justify-content: center;
      align-items: center;
      padding: 20px;
      box-sizing: border-box;
    }

    .analysis-result-content {
      background: white;
      border-radius: 10px;
      max-width: 1200px;
      width: 100%;
      max-height: 90vh;
      overflow-y: auto;
      position: relative;
      box-shadow: 0 20px 60px rgba(0,0,0,0.3);
    }

    .analysis-result-header {
      padding: 20px;
      border-bottom: 1px solid #e0e0e0;
      background: #f8f9fa;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .analysis-result-body {
      padding: 0;
    }

    .close-analysis-btn {
      background: #e74c3c;
      color: white;
      border: none;
      padding: 8px 15px;
      border-radius: 5px;
      cursor: pointer;
      font-size: 14px;
    }

    .close-analysis-btn:hover {
      background: #c0392b;
    }
  </style>
</head>
<body>
  <div class="main-container">
    <div class="header-container">
      <h2 class="ui header" style="margin: 0 12px 0 0;">
        <i class="hospital icon"></i>
        <div class="content">
          重点传染病与突发公卫事件风险评估报告
          <div class="sub header">数据来自中国疾控中心官网实时获取</div>
        </div>
      </h2>
    </div>

    <div class="grid-container">
      <div id="myGrid" class="ag-theme-quartz" style="width:100%; height:100%;"></div>
    </div>
  </div>

  <!-- AI分析遮罩 -->
  <div class="ai-analysis-overlay" id="aiAnalysisOverlay">
    <div class="ai-analysis-content">
      <div class="ai-analysis-spinner"></div>
      <div class="ai-analysis-text">正在调用AI分析PDF内容...</div>
      <div class="ai-analysis-text" style="font-size: 16px; margin-top: 10px;">请耐心等待，分析过程可能需要1-2分钟</div>
      <div class="ai-analysis-warning">请勿刷新或关闭页面</div>
    </div>
  </div>

  <!-- 分析结果模态框 -->
  <div class="analysis-result-modal" id="analysisResultModal">
    <div class="analysis-result-content">
      <div class="analysis-result-header">
        <h3 style="margin: 0; color: #333;">
          <i class="brain icon"></i>
          AI分析结果
        </h3>
        <button class="close-analysis-btn" onclick="closeAnalysisResult()">
          <i class="times icon"></i> 关闭
        </button>
      </div>
      <div class="analysis-result-body" id="analysisResultBody">
        <!-- 分析结果将在这里显示 -->
      </div>
    </div>
  </div>

  <script>
    const columnDefs = [
      { headerName: "序号", valueGetter: "node.rowIndex + 1", width: 80, filter: false, suppressSizeToFit: true,
        cellStyle: { 'text-align': 'center', 'display': 'flex', 'align-items': 'center', 'justify-content': 'center' } },
      { headerName: "标题", field: "title", flex: 1, minWidth: 500, autoHeight: true,
        cellStyle: { 'text-align': 'left', 'white-space': 'normal', 'line-height': '20px', 'display': 'flex', 'align-items': 'center', 'padding': '10px' } },
      { headerName: "发布日期", field: "date", width: 140,
        cellStyle: { 'text-align': 'center', 'display': 'flex', 'align-items': 'center', 'justify-content': 'center' } },
      { headerName: "查看原文", field: "pdfUrl", width: 110, suppressSizeToFit: true, cellRenderer: function(params){
          if (!params.value) return '';
          return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><a href="' + params.value + '" target="_blank" class="ui basic blue button mini" style="margin: 0;">查看原文</a></div>';
        }, cellStyle: { 'padding': '5px' }
      },
      { headerName: "操作", width: 150, suppressSizeToFit: true, cellRenderer: function(params){
          if (!params.data.pdfUrl) return '';
          return '<div style="display: flex; align-items: center; justify-content: center; height: 100%;"><button class="ui primary button mini generate-report-btn" style="margin: 0;" data-pdf-url="' + params.data.pdfUrl + '" data-title="' + (params.data.title || '') + '">生成可视化报告</button></div>';
        }
      },
    ];

    const gridOptions = {
      columnDefs,
      pagination: true,
      paginationPageSize: 20,
      paginationPageSizeSelector: [10, 20, 50],
      localeText: AG_GRID_LOCALE_ZH,
      defaultColDef: { resizable: true, filter: true, sortable: true },
    };

    async function loadAllPages(){
      // 自动翻页聚合：先取首页，再从第二页开始直到无数据
      let all = [];
      let page = 1;
      while(true){
        let url = '/api/cdc_risk_reports_china';
        if(page > 1) url += `?page=${page}`;
        const data = await fetch(url).then(r=>r.json()).catch(()=>[]);
        if(!Array.isArray(data) || data.length === 0) break;
        all = all.concat(data);
        page += 1;
        // 简单上限，避免意外无限循环
        if(page > 50) break;
      }
      // 去重（按标题+日期）
      const key = x => `${x.title}__${x.date}`;
      const map = new Map();
      for(const r of all){ if(!map.has(key(r))) map.set(key(r), r); }
      const merged = Array.from(map.values());
      // 按日期倒序
      merged.sort((a,b)=> (a.date<b.date?1:-1));
      gridApi.setGridOption('rowData', merged);
      setTimeout(()=> gridApi.sizeColumnsToFit(), 150);
    }

    let gridApi;
    document.addEventListener('DOMContentLoaded', () => {
      gridApi = agGrid.createGrid(document.getElementById('myGrid'), gridOptions);
      loadAllPages();

      // 绑定生成报告按钮事件
      document.addEventListener('click', function(e) {
        if (e.target.classList.contains('generate-report-btn')) {
          const pdfUrl = e.target.getAttribute('data-pdf-url');
          const title = e.target.getAttribute('data-title');
          generateAIReport(pdfUrl, title);
        }
      });
    });

    // AI分析相关函数
    function generateAIReport(pdfUrl, title) {
      if (!pdfUrl) {
        showMessage('错误', '无法获取PDF链接', 'error');
        return;
      }

      // 显示分析遮罩
      showAnalysisOverlay();

      // 调用AI分析接口
      fetch('/api/cdc_report_ai_analysis', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pdf_url: pdfUrl,
          title: title
        })
      })
      .then(response => response.json())
      .then(data => {
        hideAnalysisOverlay();

        if (data.success) {
          showAnalysisResult(data.data);
        } else {
          showMessage('分析失败', data.error || '未知错误', 'error');
        }
      })
      .catch(error => {
        hideAnalysisOverlay();
        console.error('AI分析请求失败:', error);
        showMessage('网络错误', '请求失败，请检查网络连接', 'error');
      });
    }

    function showAnalysisOverlay() {
      document.getElementById('aiAnalysisOverlay').style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }

    function hideAnalysisOverlay() {
      document.getElementById('aiAnalysisOverlay').style.display = 'none';
      document.body.style.overflow = 'auto';
    }

    function showAnalysisResult(analysisData) {
      const modal = document.getElementById('analysisResultModal');
      const body = document.getElementById('analysisResultBody');

      // 设置分析结果内容
      body.innerHTML = analysisData.html_report || '<div class="ui negative message">分析结果为空</div>';

      // 显示模态框
      modal.style.display = 'flex';
      document.body.style.overflow = 'hidden';
    }

    function closeAnalysisResult() {
      document.getElementById('analysisResultModal').style.display = 'none';
      document.body.style.overflow = 'auto';
    }

    function showMessage(title, message, type) {
      // 创建消息提示
      const messageDiv = document.createElement('div');
      messageDiv.className = `ui ${type} message`;
      messageDiv.style.position = 'fixed';
      messageDiv.style.top = '20px';
      messageDiv.style.right = '20px';
      messageDiv.style.zIndex = '10001';
      messageDiv.style.maxWidth = '400px';
      messageDiv.innerHTML = `
        <i class="close icon" onclick="this.parentElement.remove()"></i>
        <div class="header">${title}</div>
        <p>${message}</p>
      `;

      document.body.appendChild(messageDiv);

      // 3秒后自动移除
      setTimeout(() => {
        if (messageDiv.parentElement) {
          messageDiv.remove();
        }
      }, 5000);
    }

    // 点击遮罩外部关闭模态框
    document.getElementById('analysisResultModal').addEventListener('click', function(e) {
      if (e.target === this) {
        closeAnalysisResult();
      }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        if (document.getElementById('analysisResultModal').style.display === 'flex') {
          closeAnalysisResult();
        }
      }
    });
  </script>
</body>
</html>

