<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8">
    <title>AI搜索</title>
    <link rel="shortcut icon" href="{{ url_for('static', filename='images/favicon.ico') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/jquery-ui.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/semantic.min.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/app.css') }}">
    <script src="{{ url_for('static', filename='js/jquery-3.5.1.js') }}"></script>
    <script src="{{ url_for('static', filename='js/jquery-ui.js') }}"></script>
    <script src="{{ url_for('static', filename='js/semantic.min.js') }}"></script>
    <!-- 引入 Split.js -->
    <script src="https://unpkg.com/split.js/dist/split.min.js"></script>

    <style>
        html,
        body {
            height: 100%;
            margin: 0;
            font-family: Arial, sans-serif;
        }

        .container {
            display: flex;
            height: 100%;
        }

        .sidebar {
            width: 200px;
            background-color: #333;
            color: white;
            padding: 20px;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.5);
            position: relative;
            transition: width 0.3s;
            /* 添加过渡效果 */
        }

        .sidebar h2 {
            margin-top: 0;
            font-size: 24px;
        }

        .sidebar a {
            display: block;
            color: white;
            text-decoration: none;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            transition: background 0.3s;
        }

        .sidebar a:hover {
            background-color: #575757;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .search-area {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .ui.left.icon.action.input {
            width: 300px;
            /* 设置一个固定宽度，或根据需要调整 */
            max-width: 100%;
        }

        /* 更新 .iframe-container 样式 */
        .iframe-container {
            display: flex;
            flex: 1;
            height: 100%;
        }

        iframe {
            width: 100%;
            border: none;
            height: 100%;
        }

        /* 自定义 Split.js 的 gutter 样式 */
        .gutter {
            width: 8px;
            background-color: #e0e0e0;
            cursor: col-resize;
            position: relative;
        }

        .gutter::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 4px;
            height: 4px;
            background-color: #a0a0a0;
            border-radius: 50%;
            box-shadow:
                0 -8px 0 0 #a0a0a0,
                0 8px 0 0 #a0a0a0;
        }

        .hamburger {
            cursor: pointer;
            padding: 10px;
            background-color: #444;
            color: white;
            border: none;
            position: absolute;
            top: 10px;
            right: 10px;
            border-radius: 5px;
            transition: background 0.3s, right 0.3s;
            z-index: 1;
            /* 确保按钮在最上层 */
        }

        .hamburger:hover {
            background-color: #575757;
        }

        .hamburger:focus {
            outline: none;
        }

        /* 收缩后的样式 */
        .sidebar.collapsed {
            width: 50px;
            /* 增加宽度以容纳按钮 */
        }

        .sidebar.collapsed h2,
        .sidebar.collapsed a {
            display: none;
            /* 隐藏标题和链接 */
        }

        .sidebar.collapsed .hamburger {
            position: absolute;
            top: 10px;
            right: 0;
            /* 完全在侧边栏内部 */
            padding: 10px;
            /* 调整按钮大小以适应较窄的侧边栏 */
        }

        /* 响应式设计（可选） */
        @media (max-width: 768px) {
            .sidebar {
                width: 150px;
            }

            .sidebar.collapsed {
                width: 40px;
            }

            .search-area {
                padding: 10px;
            }

            .ui.left.icon.action.input {
                width: 100%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="sidebar" id="sidebar">
            <button class="hamburger" onclick="toggleSidebar()">☰</button>
            <h2>AI导航</h2>
            <a href="https://www.tiangong.cn/" target="_blank">天工AI</a>
            <a href="https://kimi.moonshot.cn/" target="_blank">KIMI</a>
            <a href="https://www.perplexity.ai/" target="_blank">Perplexity</a>
            <a href="https://www.so.com/" target="_blank">360AI问答</a>
            <a href="https://chat.baidu.com/" target="_blank">百度AI助手</a>
        </div>

        <div class="main-content">
            <div class="search-area">
                <div class="ui left icon action input" style="width: 40%;">
                    <i class="search icon"></i>
                    <input type="text" id="customSearchBox" placeholder="请输入关键词...">
                    <div class="ui buttons">
                        <button class="ui primary button" id="searchButton">协同搜索</button>
                    </div>
                </div>
            </div>
            <div class="iframe-container" id="split-container">
                <iframe name="content-frame" id="iframe1" src="http://************:3000" title="AI搜索"></iframe>
                <iframe id="iframe2" src="http://************:3001" title="AI搜索"></iframe>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed'); // 切换收缩类
        }

        // 等待文档加载完毕
        document.addEventListener('DOMContentLoaded', function () {
            const searchButton = document.getElementById('searchButton');
            const searchBox = document.getElementById('customSearchBox');
            const iframe1 = document.getElementById('iframe1');
            const iframe2 = document.getElementById('iframe2');

            // 定义搜索函数
            function performSearch() {
                const query = searchBox.value.trim();
                if (query === "") {
                    alert("请输入搜索关键词！");
                    return;
                }
                const encodedQuery = encodeURIComponent(query);
                const searchURL1 = `http://************:3000/search?q=${encodedQuery}`;
                const searchURL2 = `http://************:3001/search?q=${encodedQuery}`;

                // 更新 iframe 的 src 属性
                iframe1.src = searchURL1;
                iframe2.src = searchURL2;
            }

            // 添加点击事件监听器
            searchButton.addEventListener('click', performSearch);

            // 添加回车键事件监听器
            searchBox.addEventListener('keypress', function (event) {
                if (event.key === 'Enter') {
                    performSearch();
                }
            });

            // 初始化 Split.js
            Split(['#iframe1', '#iframe2'], {
                sizes: [50, 50], // 初始大小比例
                minSize: 300, // 最小宽度设置为300px
                gutterSize: 8, // 分割线宽度
                cursor: 'col-resize',
                direction: 'horizontal',
                // 使用自定义的 gutter 元素
                gutter: function (index, direction) {
                    var gutter = document.createElement('div');
                    gutter.className = 'gutter';
                    return gutter;
                }
            });
        });
    </script>
</body>

</html>