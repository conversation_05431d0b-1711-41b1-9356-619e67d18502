from datetime import datetime
from flask import jsonify
import requests

def format_date(date_str):
    try:
        date_str = date_str.rstrip("Z")
        # 如果日期字符串有超过6位小数秒，则截断到6位
        if '.' in date_str:
            date_str_parts = date_str.split('.')
            date_str = date_str_parts[0] + '.' + date_str_parts[1][:6]
        # 尝试将字符串按ISO 8601格式解析为datetime对象
        date_obj = datetime.strptime(date_str, '%Y-%m-%dT%H:%M:%S.%f')
        # 转换日期格式为 YYYY/MM/DD HH:MM:SS
        return date_obj.strftime('%Y/%m/%d %H:%M:%S')
    except ValueError as e:
        # 打印错误信息和日期字符串
        print(f"Error parsing date string '{date_str}': {e}")
        # 如果日期格式不正确，返回'Unknown Date'
        return 'Unknown Date'

def search_bing_news(keyword, start_date, end_date):
    # Bing Search API的订阅密钥和URL
    subscription_key = "5afcad05733245348ef559093e89a4dc"  # 替换为你的订阅密钥
    url = "https://api.bing.microsoft.com/v7.0/news/search"

    #设定参数和请求头
    headers = {"Ocp-Apim-Subscription-Key" : subscription_key}
    params  = {
        "q": keyword,
        "mkt": "zh-CN",
        "count": "50",
        "since": start_date,
        "until": end_date
    }
    
    # 发送请求
    response = requests.get(url, headers=headers, params=params)
    
    # 判断请求的状态
    if response.status_code == 200:
        search_results = response.json()

        # 检查并格式化结果
        formatted_data = {
            'data': [{
                'title': news.get('name', ''),
                'publishedAt': format_date(news['datePublished']) if 'datePublished' in news else 'Unknown Date',
                'description': news.get('description', ''),
                'source': news.get('provider', [{'name': 'Unknown Source'}])[0].get('name'),
                'url': news.get('url'),
            } for news in search_results.get('value', [])]
        }

        return jsonify(formatted_data)
    
    else:
        print('发生错误：', response.status_code)
        return jsonify({
            'data': [{
                'title': '无法获取新闻，请检查API密钥或网络连接。',
                'publishedAt': 'N/A',
                'description': 'N/A',
                'source': 'N/A',
                'url': 'N/A',
            }]
        })