from flask import jsonify, request
from routes.disease_prediction import DiseasePrediction, SEIRModel
import traceback
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import pymysql
from db.db_config import USERNAME, PASSWORD, HOST, PORT, DATABASE

class DiseasePredictionChina(DiseasePrediction):
    def get_data_from_db(self, start_date=None, end_date=None):
        """从数据库获取中国疾病数据"""
        try:
            connection = pymysql.connect(
                host=HOST,
                user=USERNAME,
                password=PASSWORD,
                database=DATABASE,
                port=int(PORT)
            )
            
            query = """
                SELECT time, disaster, growth, deaths, cures 
                FROM t_disaster_china 
                WHERE growth IS NOT NULL
                AND time BETWEEN %s AND %s
                ORDER BY time
            """
            
            with connection.cursor() as cursor:
                cursor.execute(query, (start_date, end_date))
                columns = [col[0] for col in cursor.description]
                data = cursor.fetchall()
                
            df = pd.DataFrame(list(data), columns=columns)
            df['time'] = pd.to_datetime(df['time'])
            
            connection.close()
            return df
            
        except Exception as e:
            print(f"Error in get_data_from_db: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def prepare_data(self, df, disease):
        """准备预测所需的数据"""
        try:
            disease_data = df[df['disaster'] == disease].copy()
            
            if len(disease_data) < 10:
                print(f"Insufficient data for disease {disease}")
                return None
            
            disease_data = disease_data.sort_values('time')
            disease_data['total_cases'] = disease_data['growth'].cumsum()
            disease_data['total_deaths'] = disease_data['deaths'].cumsum()
            disease_data['total_cures'] = disease_data['cures'].cumsum()
            
            return disease_data
            
        except Exception as e:
            print(f"Error in prepare_data: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            raise

    def get_prediction_for_china(self, disease, start_date, end_date, prediction_months=6):
        """获取中国特定疾病的预测"""
        try:
            # 获取数据
            df = self.get_data_from_db(start_date, end_date)
            disease_data = self.prepare_data(df, disease)
            
            if disease_data is None:
                return None, "insufficient_data"
            
            # 获取模型参数
            params = self.get_model_parameters(disease, disease_data)
            
            # 使用默认人口数据（可以根据需要修改）
            N = 1400000000  # 约14亿人口
            
            # 初始化SEIR模型
            model = SEIRModel(
                beta=params['beta'],
                sigma=1/params['incubation'],
                gamma=1/params['duration'],
                N=N
            )
            
            # 设置初始值
            latest_data = disease_data.iloc[-1]
            I0 = max(latest_data['total_cases'] - latest_data['total_cures'] - latest_data['total_deaths'], 0)
            R0 = latest_data['total_cures'] + latest_data['total_deaths']
            E0 = I0 * 0.5  # 估计潜伏人数
            S0 = N - E0 - I0 - R0
            
            # 预测时间点（按月预测）
            prediction_days = prediction_months * 30
            t = np.linspace(0, prediction_days, prediction_days)
            
            # 进行预测
            S, E, I, R = model.predict(S0, E0, I0, R0, t)
            
            # 计算每日新增
            daily_new_cases = np.diff(I + R, prepend=I[0] + R[0])
            
            # 准备返回结果
            last_date = disease_data['time'].iloc[-1]
            prediction_results = {
                'dates': [(last_date + timedelta(days=int(x))).strftime('%Y-%m-%d') for x in t],
                'predicted_growth': daily_new_cases.tolist(),
                'lower_bound': (daily_new_cases * 0.8).tolist(),
                'upper_bound': (daily_new_cases * 1.2).tolist(),
                'susceptible': S.tolist(),
                'exposed': E.tolist(),
                'infected': I.tolist(),
                'recovered': R.tolist()
            }
            
            return prediction_results, "success"
            
        except Exception as e:
            print(f"Error in get_prediction_for_china: {str(e)}")
            print(f"Traceback: {traceback.format_exc()}")
            return None, "prediction_failed"

def handler():
    try:
        disease = request.form.get('disease')
        start_date = request.form.get('start_date')
        end_date = request.form.get('end_date')
        prediction_period = int(request.form.get('prediction_period', 6))
        
        if not all([disease, start_date, end_date]):
            return jsonify({
                "error": "Missing required parameters",
                "status": "invalid_params"
            }), 400
        
        # 验证日期格式
        try:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        except ValueError:
            return jsonify({
                "error": "Invalid date format",
                "status": "invalid_date"
            }), 400
            
        # 初始化预测模型
        predictor = DiseasePredictionChina()
        prediction_results, status = predictor.get_prediction_for_china(
            disease, 
            start_date, 
            end_date, 
            prediction_period
        )
        
        if status != "success" or not prediction_results:
            error_messages = {
                "insufficient_data": "历史数据不足，预测需要至少10条历史数据记录。",
                "prediction_failed": "预测过程中发生错误，请稍后重试。",
            }
            return jsonify({
                "error": error_messages.get(status, "预测失败"),
                "status": status
            }), 400
            
        response_data = {
            "dates": prediction_results['dates'],
            "predictions": prediction_results['predicted_growth'],
            "seir": {
                "susceptible": prediction_results['susceptible'],
                "exposed": prediction_results['exposed'],
                "infected": prediction_results['infected'],
                "recovered": prediction_results['recovered']
            },
            "cumulative": [sum(prediction_results['predicted_growth'][:i+1]) 
                          for i in range(len(prediction_results['predicted_growth']))]
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        print(f"Error in predict_disease_china: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": str(e), "status": "server_error"}), 500 