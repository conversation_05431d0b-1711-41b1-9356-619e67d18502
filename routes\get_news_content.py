from flask import jsonify, request
import routes.call_chatgpt as call_chatgpt

def handler():
    # 从请求体中获取JSON数据
    data = request.get_json()
    if data and 'url' in data:
        news_url = data['url']
        try:
            content = call_chatgpt.getNewsContent(news_url)
            # 检查响应的内容是否不为空或不为None
            if content:
                content = '原文链接：' + news_url + '\n\n' + content
                return jsonify({'status': True, 'content': content})
            else:
                content = '原文链接：' + news_url + '\n\n' + content
                return jsonify({'status': False, 'message': '新闻内容为空'})
        except Exception as e:
            return jsonify({'status': False, 'message': '获取新闻内容时发生错误: {}'.format(e)})
    else:
        return jsonify({'status': False, 'message': '没有提供新闻URL'}) 