<!DOCTYPE html>
<html>
<head>
    <title>月份范围选择器示例</title>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css" />
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/momentjs/latest/moment.min.js"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
    <style>
        /* 日期选择器样式 */
        .daterangepicker.month-mode .calendar-table tbody tr td {
            height: 50px;
            cursor: pointer;
        }
        .daterangepicker.month-mode .calendar-table tbody tr td.in-range {
            background-color: #ebf4f8;
        }
        .daterangepicker.month-mode .calendar-table tbody tr td.active {
            background-color: #357ebd;
            color: #fff;
        }
        .daterangepicker select.monthselect,
        .daterangepicker select.yearselect {
            padding: 5px;
            height: auto;
            margin: 0;
            cursor: pointer;
            font-size: 14px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
        .daterangepicker .calendar-table th, 
        .daterangepicker .calendar-table td {
            min-width: 30px;
            width: auto;
            height: 24px;
        }
        .daterangepicker td.in-range {
            background-color: #ebf4f8;
            border-radius: 0;
        }

        /* 示例页面样式 */
        .container {
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-group">
            <label>选择月份范围</label>
            <input type="text" id="dateRange" class="form-control" placeholder="选择日期范围">
        </div>
    </div>

    <script>
        $(document).ready(function() {
            // 初始化日期范围选择器
            $('#dateRange').daterangepicker({
                startDate: moment().subtract(2, 'years'),
                endDate: moment(),
                showDropdowns: true,  // 允许年份和月份下拉选择
                minYear: 2010,
                maxYear: parseInt(moment().format('YYYY'), 10),
                locale: {
                    format: 'YYYY-MM',
                    applyLabel: '确定',
                    cancelLabel: '取消',
                    customRangeLabel: '自定义范围',
                    monthNames: [
                        '一月', '二月', '三月', '四月',
                        '五月', '六月', '七月', '八月',
                        '九月', '十月', '十一月', '十二月'
                    ]
                },
                ranges: {
                   '最近6个月': [moment().subtract(6, 'months'), moment()],
                   '最近1年': [moment().subtract(1, 'year'), moment()],
                   '最近2年': [moment().subtract(2, 'years'), moment()]
                },
                opens: 'right',
                autoApply: false,
                alwaysShowCalendars: true,
                showCustomRangeLabel: true,
                linkedCalendars: false
            });

            // 强制使用月份选择模式
            $('#dateRange').on('show.daterangepicker', function (ev, picker) {
                $('.daterangepicker').addClass('month-mode');
                $('.daterangepicker td.available').on('mouseenter', function() {
                    var td = $(this);
                    var month = td.data('month');
                    var year = td.data('year');
                    td.addClass('in-range');
                });
            });

            // 在日期选择时自动设置为月份的第一天
            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                picker.setStartDate(picker.startDate.startOf('month'));
                picker.setEndDate(picker.endDate.endOf('month'));
            });

            // 示例：监听日期变化
            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                console.log('选择的开始日期:', picker.startDate.format('YYYY-MM'));
                console.log('选择的结束日期:', picker.endDate.format('YYYY-MM'));
            });
        });
    </script>
</body>
</html> 