from flask import jsonify
from model.models import db
from sqlalchemy import text
import traceback

def handler():
    try:
        # 从t_disaster_shanghai表中获取不重复的疾病列表，按拼音排序
        query = text("""
            SELECT DISTINCT disaster as value, disaster as name
            FROM t_disaster_shanghai
            WHERE disaster IS NOT NULL
            ORDER BY CONVERT(disaster USING gbk) COLLATE gbk_chinese_ci
        """)
        
        result = db.session.execute(query)
        diseases = [{"value": row.value, "name": row.name} for row in result]
        
        return jsonify({"diseases": diseases})
        
    except Exception as e:
        print(f"Error in get_diseases_shanghai: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return jsonify({"error": "获取疾病列表失败", "details": str(e)}), 500 