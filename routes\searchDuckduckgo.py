# searchDuckduckgo.py
from flask import jsonify
from duckduckgo_search import DDGS
from datetime import datetime, timedelta
from deep_translator import GoogleTranslator
import pytz

def determine_timelimit(start_date, end_date):
    delta = end_date - start_date
    if delta <= timedelta(days=1):
        return 'd'  # day
    elif delta <= timedelta(weeks=1):
        return 'w'  # week
    elif delta <= timedelta(days=30):
        return 'm'  # month
    elif delta <= timedelta(days=365):
        return 'y'  # year
    else:
        return 'a'  # all time

def search_duckduckgo_news(keyword, start_date_str, end_date_str, lang):
    # 解析日期字符串为 datetime 对象
    try:
        start_date = datetime.fromisoformat(start_date_str)
        end_date = datetime.fromisoformat(end_date_str)
    except ValueError:
        return jsonify({'error': 'Invalid date format. Use ISO format YYYY-MM-DD.'}), 400

    # 将开始日期设置为午夜（00:00:00）
    start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)

    # 将结束日期设置为一天的最后一刻（23:59:59）
    end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)

    # 确保 start_date 和 end_date 都是带时区的
    if start_date.tzinfo is None:
        start_date = start_date.replace(tzinfo=pytz.utc)
    if end_date.tzinfo is None:
        end_date = end_date.replace(tzinfo=pytz.utc)

    # 确保 start_date <= end_date
    if start_date > end_date:
        return jsonify({'error': 'start_date must be before end_date.'}), 400

    # 初始化 DDGS 对象
    ddgs = DDGS()

    # 判断是否需要翻译关键词
    if lang == 'bilingual' and not keyword.isascii():
        translated_search = GoogleTranslator(source='auto', target='en').translate(keyword)
        keywords_to_search = [keyword, translated_search]
    else:
        keywords_to_search = [keyword]

    # 用于存储所有结果
    all_results = []

    # 对每个关键词执行搜索
    for search_keyword in keywords_to_search:
        results = ddgs.news(keywords=search_keyword, region="us-en", safesearch="off", max_results=100)
        all_results.extend(results)  # 合并结果

    # 打印总结果数量
    print(f"总共有 {len(all_results)} 条数据")

    # 过滤并格式化结果
    formatted_data = {
        'data': []
    }
    for news in all_results:
        news_date_str = news.get('date')
        if not news_date_str:
            continue
        try:
            news_date = datetime.fromisoformat(news_date_str.replace('Z', '+00:00'))
        except ValueError:
            continue

        # 确保 news_date 也是带时区的
        if news_date.tzinfo is None:
            news_date = news_date.replace(tzinfo=pytz.utc)

        # 按日期比较
        if start_date <= news_date <= end_date:
            formatted_data['data'].append({
                'title': news.get('title', ''),
                'publishedAt': news_date.strftime('%Y/%m/%d %H:%M:%S'),  # 使用原始新闻日期
                'description': news.get('body', ''),
                'source': news.get('source', 'Unknown Source'),
                'url': news.get('url'),
            })

    return jsonify(formatted_data)